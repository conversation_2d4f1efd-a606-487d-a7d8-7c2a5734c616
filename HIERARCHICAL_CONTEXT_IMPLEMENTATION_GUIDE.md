# Hierarchical Context Selection Implementation Guide

## Architecture Overview

The Hierarchical Context Selection system implements a top-down approach to code context selection, first understanding the system architecture, then drilling down to specific components based on query intent and architectural significance.

## Core Components

### 1. System Architecture Generator

**File**: `aider-main/aider/context_request/system_architecture_generator.py`

#### Purpose
Generates top-level system architecture from IR data by analyzing modules, dependencies, and creating architectural clusters.

#### Key Classes
```python
@dataclass
class ModuleCluster:
    name: str                    # Cluster identifier
    modules: List[str]           # Modules in this cluster
    purpose: str                 # Cluster purpose description
    module_type: ModuleType      # Type classification
    layer: ArchitecturalLayer    # Architectural layer
    dependencies: List[str]      # Other clusters this depends on
    dependents: List[str]        # Other clusters that depend on this
    criticality: float           # Criticality score (0.0-1.0)
    key_entities: List[str]      # Important entities in cluster

@dataclass
class SystemArchitecture:
    clusters: List[ModuleCluster]                    # All clusters
    dependency_graph: Dict[str, List[str]]           # Cluster dependencies
    layer_hierarchy: Dict[ArchitecturalLayer, List[str]]  # Layer organization
    critical_paths: List[List[str]]                  # Critical execution paths
    entry_points: List[str]                          # System entry points
    core_workflows: List[Dict[str, Any]]             # Core workflows
```

#### Implementation Details
```python
class SystemArchitectureGenerator:
    def generate_architecture(self, ir_data: Dict[str, Any]) -> SystemArchitecture:
        # Step 1: Analyze modules and their characteristics
        self._analyze_modules()
        
        # Step 2: Identify module clusters based on functionality
        clusters = self._identify_module_clusters()
        
        # Step 3: Determine architectural layers
        self._assign_architectural_layers(clusters)
        
        # Step 4: Build dependency graph between clusters
        dependency_graph = self._build_cluster_dependency_graph(clusters)
        
        # Step 5: Identify critical paths and workflows
        critical_paths = self._identify_critical_paths(clusters, dependency_graph)
        
        return SystemArchitecture(...)
```

### 2. Hierarchical Context Selector

**File**: `aider-main/aider/context_request/hierarchical_context_selector.py`

#### Purpose
Selects context using hierarchical architectural understanding, implementing different strategies based on query intent.

#### Selection Strategies
```python
class ContextSelectionStrategy(Enum):
    WORKFLOW_FOCUSED = "workflow_focused"      # Focus on specific workflow
    ARCHITECTURE_OVERVIEW = "architecture_overview"  # Broad architectural view
    CLUSTER_DEEP_DIVE = "cluster_deep_dive"   # Deep dive into specific cluster
    CROSS_CUTTING = "cross_cutting"           # Cross-cutting concerns
```

#### Strategy Implementation
```python
def _determine_selection_strategy(self, query_analysis: QueryContext, 
                                focus_entities: List[str] = None) -> ContextSelectionStrategy:
    intent = query_analysis.intent
    scope = query_analysis.scope
    
    if intent.value in ['workflow_analysis', 'debugging_assistance']:
        return ContextSelectionStrategy.WORKFLOW_FOCUSED
    elif intent.value in ['architecture_understanding', 'system_overview']:
        return ContextSelectionStrategy.ARCHITECTURE_OVERVIEW
    elif scope.value == 'single_component' or (focus_entities and len(focus_entities) <= 2):
        return ContextSelectionStrategy.CLUSTER_DEEP_DIVE
    else:
        return ContextSelectionStrategy.CROSS_CUTTING
```

#### Entity Scoring
```python
def _score_entities_for_query(self, entities: List[Dict[str, Any]], 
                            query_analysis: QueryContext, 
                            strategy: ContextSelectionStrategy = None,
                            cluster_name: str = None) -> List[Dict[str, Any]]:
    for entity in entities:
        score = 0.0
        
        # Base score from criticality
        criticality = entity.get('criticality', 'low')
        if criticality == 'high':
            score += 0.4
        elif criticality == 'medium':
            score += 0.2
        
        # Strategy-specific scoring
        if strategy == ContextSelectionStrategy.ARCHITECTURE_OVERVIEW:
            if entity_type == 'class':
                score += 0.5  # Prefer classes for architecture
        elif strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED:
            if entity_type in ['function', 'method']:
                score += 0.4  # Prefer functions for workflow
        # ... additional strategy logic
```

### 3. Enhanced Metadata Integration

**File**: `aider-main/aider/context_request/semantic_context_integration.py`

#### Enhanced Package Generation
```python
def create_enhanced_llm_package(self, context_package: Dict[str, Any],
                              max_chars: int = 30000) -> str:
    # Add component details with enhanced metadata
    for i, ir_slice in enumerate(ir_slices, 1):
        entity_name = ir_slice.get('entity_name', 'unknown')
        entity_type = ir_slice.get('entity_type', 'unknown')
        file_path = ir_slice.get('file_path', '')
        module_name = ir_slice.get('module_name', '')
        line_number = ir_slice.get('line_number', 'N/A')
        criticality = ir_slice.get('criticality', 'medium')
        change_risk = ir_slice.get('change_risk', 'medium')
        cluster = ir_slice.get('cluster', 'unknown')
        
        # Format with type icons
        type_icons = {
            'class': '🏛️', 'function': '⚙️', 'method': '🔧',
            'variable': '📊', 'constant': '🔒', 'property': '🏷️'
        }
        type_icon = type_icons.get(entity_type.lower(), '📄')
        
        package_content += f"""### {i}. {type_icon} {entity_name}
- **Type**: {entity_type.title()}
- **File**: {file_path}
- **Module**: {module_name}
- **Line**: {line_number}
- **Cluster**: {cluster}
- **Criticality**: {criticality.title()}
- **Change Risk**: {change_risk.title()}
- **Relevance Score**: {relevance_score:.3f}
- **Semantic Rationale**: {explanation}"""
```

## Integration Points

### 1. Context Request Handler Integration

**File**: `aider-main/aider/context_request/context_request_handler.py`

```python
def process_ir_context_request(self, request: IRContextRequest) -> Dict[str, Any]:
    # Use hierarchical context selector
    from .hierarchical_context_selector import HierarchicalContextSelector
    
    hierarchical_selector = HierarchicalContextSelector()
    
    # Use hierarchical selection based on system architecture
    hierarchical_context = hierarchical_selector.select_hierarchical_context(
        ir_data=ir_data,
        user_query=request.user_query,
        focus_entities=request.focus_entities,
        max_entities=request.max_entities
    )
    
    # Convert to semantic format for compatibility
    enhanced_context = self._convert_hierarchical_to_semantic_context(
        hierarchical_context, ir_data, request
    )
```

### 2. Metadata Conversion

```python
def _convert_hierarchical_to_semantic_context(self, hierarchical_context: Dict[str, Any], 
                                             ir_data: Dict[str, Any], 
                                             request) -> Dict[str, Any]:
    selected_entities = hierarchical_context.get('selected_entities', [])
    
    # Convert selected entities to IR slices format with enhanced metadata
    ir_slices = []
    for entity in selected_entities:
        ir_slice = {
            'entity_name': entity.get('name', ''),
            'entity_type': entity.get('type', 'function'),
            'module_name': entity.get('module_name', ''),
            'file_path': entity.get('file_path', ''),
            'line_number': entity.get('line_number', 'N/A'),
            'criticality': entity.get('criticality', 'medium'),
            'change_risk': entity.get('change_risk', 'medium'),
            'relevance_score': entity.get('relevance_score', 0.0),
            'calls': entity.get('calls', []),
            'used_by': entity.get('used_by', []),
            'side_effects': entity.get('side_effects', []),
            'cluster': entity.get('cluster', ''),
            
            # Inheritance information
            'inherits_from': entity.get('inherits_from', []),
            'method_overrides': entity.get('method_overrides', []),
            'calls_super': entity.get('calls_super', False),
            'class_name': entity.get('class_name', ''),
            
            'semantic_explanation': f"Selected from {entity.get('cluster', 'unknown')} cluster..."
        }
        ir_slices.append(ir_slice)
```

## Configuration and Customization

### 1. Cluster Pattern Configuration

```python
# In SystemArchitectureGenerator._identify_module_clusters()
cluster_patterns = {
    'core': {
        'keywords': ['main', 'core', 'base', 'coder', 'engine'],
        'type': ModuleType.CORE_BUSINESS,
        'layer': ArchitecturalLayer.APPLICATION
    },
    'context': {
        'keywords': ['context', 'request', 'handler', 'selector'],
        'type': ModuleType.CORE_BUSINESS,
        'layer': ArchitecturalLayer.APPLICATION
    },
    # ... additional patterns
}
```

### 2. Strategy Customization

```python
# Custom strategy implementation
def _select_custom_context(self, query_analysis: QueryContext, 
                          focus_entities: List[str]) -> ArchitecturalContext:
    # Custom cluster selection logic
    primary_clusters = self._select_custom_clusters(query_analysis)
    supporting_clusters = self._select_supporting_clusters(primary_clusters)
    
    return ArchitecturalContext(
        strategy=ContextSelectionStrategy.CUSTOM,
        primary_clusters=primary_clusters,
        supporting_clusters=supporting_clusters,
        workflow_path=primary_clusters + supporting_clusters,
        architectural_rationale="Custom selection logic applied",
        selected_entities=[]
    )
```

### 3. Scoring Customization

```python
# Custom entity scoring
def _custom_score_entities(self, entities: List[Dict[str, Any]], 
                          query_analysis: QueryContext) -> List[Dict[str, Any]]:
    for entity in entities:
        score = 0.0
        
        # Custom scoring logic
        if entity.get('type') == 'class' and 'Handler' in entity.get('name', ''):
            score += 0.5  # Boost handler classes
        
        # Apply domain-specific scoring
        if 'security' in query_analysis.intent.value:
            if any(keyword in entity.get('name', '').lower() 
                   for keyword in ['auth', 'security', 'validate']):
                score += 0.3
        
        entity['relevance_score'] = score
```

## Testing Framework

### 1. Unit Tests

```python
def test_architecture_generation():
    generator = SystemArchitectureGenerator()
    architecture = generator.generate_architecture(ir_data)
    
    assert len(architecture.clusters) > 0
    assert len(architecture.layer_hierarchy) > 0
    assert all(cluster.criticality >= 0.0 for cluster in architecture.clusters)

def test_strategy_selection():
    selector = HierarchicalContextSelector()
    query_analysis = create_test_query_analysis("How does authentication work?")
    
    strategy = selector._determine_selection_strategy(query_analysis, [])
    assert strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED
```

### 2. Integration Tests

```python
def test_end_to_end_hierarchical_selection():
    handler = ContextRequestHandler(".")
    request = IRContextRequest(
        user_query="What is the system architecture?",
        task_type="architecture_analysis",
        max_entities=8,
        llm_friendly=True
    )
    
    result = handler.process_ir_context_request(request)
    
    assert 'llm_friendly_package' in result
    assert 'Strategy: Architecture Overview' in result['llm_friendly_package']
    assert len(result.get('selected_entities', [])) > 0
```

## Performance Considerations

### 1. Caching Strategy

```python
class HierarchicalContextSelector:
    def __init__(self):
        self.system_architecture = None  # Cache architecture
        
    def select_hierarchical_context(self, ir_data, user_query, ...):
        # Generate architecture only once
        if not self.system_architecture:
            self.system_architecture = self.architecture_generator.generate_architecture(ir_data)
```

### 2. Optimization Points

- **Cluster Analysis**: Cache cluster assignments between requests
- **Entity Scoring**: Batch scoring operations for efficiency
- **Dependency Analysis**: Pre-compute dependency graphs
- **Strategy Selection**: Cache strategy decisions for similar queries

## Error Handling

### 1. Graceful Degradation

```python
def select_hierarchical_context(self, ir_data, user_query, ...):
    try:
        # Attempt hierarchical selection
        return self._full_hierarchical_selection(...)
    except Exception as e:
        # Fall back to simpler selection
        logger.warning(f"Hierarchical selection failed: {e}")
        return self._fallback_selection(...)
```

### 2. Validation

```python
def _validate_architectural_context(self, context: ArchitecturalContext) -> bool:
    if not context.primary_clusters:
        raise ValueError("No primary clusters selected")
    
    if context.strategy not in ContextSelectionStrategy:
        raise ValueError(f"Invalid strategy: {context.strategy}")
    
    return True
```

## Extension Points

### 1. Custom Strategies

Implement new selection strategies by:
1. Adding to `ContextSelectionStrategy` enum
2. Implementing selection logic in `_select_architectural_context`
3. Adding strategy-specific scoring in `_score_entities_for_query`

### 2. Custom Metadata Fields

Add new metadata fields by:
1. Extending IR slice structure in `_convert_hierarchical_to_semantic_context`
2. Updating package generation in `create_enhanced_llm_package`
3. Adding field extraction in entity processing

### 3. Custom Cluster Types

Define new cluster types by:
1. Adding to `ModuleType` enum
2. Updating cluster patterns in `_identify_module_clusters`
3. Implementing cluster-specific logic in selection strategies

This implementation provides a robust, extensible foundation for hierarchical context selection with comprehensive metadata enhancement.
