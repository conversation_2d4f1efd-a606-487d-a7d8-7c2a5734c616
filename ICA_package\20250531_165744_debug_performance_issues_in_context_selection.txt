# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-05-31 16:57:44
# Project: .
# User Query: Why is my context selection taking so long?
# Task Description: Debug performance issues in context selection
# Task Type: debugging
# Max Tokens: 5000
# Focus Entities: performance, selection, cache, extraction
# Package Size: 3,109 characters

================================================================================

# USER QUERY
Why is my context selection taking so long?

# INTELLIGENT CONTEXT ANALYSIS
## Task: debugging
## Focus: Debug performance issues in context selection

## CRITICAL ENTITIES (2 most important)

### 1. warm_cache (method)
- File: aider-main\aider\coders\base_coder.py
- **Belongs to Class**: `Coder`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get", "time", "sleep", "completion", "cacheable_messages", "..."] (total: 10)
- **Used by**: ["base_coder_old", "base_coder"] (total: 2)
- **Side Effects**: writes_log, modifies_container, modifies_state

### 2. _update_cache (method)
- File: aider-main\aider\models.py
- **Belongs to Class**: `ModelInfoManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `ModelInfoManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get", "json", "write_text", "dumps"] (total: 4)
- **Used by**: ["models", "openrouter", "surgical_file_extractor", "context_request_handler", "surgical_context_extractor"] (total: 5)
- **Side Effects**: writes_log, modifies_state, network_io

## KEY IMPLEMENTATIONS (2 functions)
Complete code available on request for any function.

### 1. warm_cache
```python
    def warm_cache(self, chunks):
        if not self.add_cache_headers:
            return
        if not self.num_cache_warming_pings:
            return
        if not self.ok_to_warm_cache:
            return

        delay = 5 * 60 - 5
        delay = float(os.environ.get("AIDER_CACHE_KEEPALIVE_DELAY", delay))
        self.next_cache_warm = time.time() + delay
        self.warming_pings_left = self.num_cache_warming_pings
        self.cache_warming_chunks = chunks

        if self.cache_warming_thread:
            return

```

### 2. _update_cache
```python
    def _update_cache(self):
        try:
            import requests

            # Respect the --no-verify-ssl switch
            response = requests.get(self.MODEL_INFO_URL, timeout=5, verify=self.verify_ssl)
            if response.status_code == 200:
                self.content = response.json()
                try:
                    self.cache_file.write_text(json.dumps(self.content, indent=4))
                except OSError:
                    pass
        except Exception as ex:
            print(str(ex))
            try:
                # Save empty dict to cache file on failure
                self.cache_file.write_text("{}")
            except OSError:
                pass

```

## ANALYSIS INSTRUCTIONS
Based on the 2 critical entities above:

1. **Focus on HIGH criticality components** - these are the most important
2. **Consider change risk** - high risk = be careful with modifications
3. **Understand dependencies** - see what calls what
4. **Note side effects** - potential impacts of changes

**Your task**: Why is my context selection taking so long?

Provide specific, actionable insights based on this focused context.

