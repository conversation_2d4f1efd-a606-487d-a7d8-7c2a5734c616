# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 16:14:10
# Project: .
# User Query: How does the context request system work?
# Task Description: Understand the context request implementation and architecture
# Task Type: general_analysis
# Max Tokens: 8000
# Focus Entities: ContextRequestHandler, process_context_request, extract_symbol
# Package Size: 4,384 characters

================================================================================

## CRITICAL ENTITIES (2 most important)

### 1. process_context_request (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["process_context_request", "get", "render_augmented_prompt"] (total: 3)
- **Used by**: ["base_coder", "test_context_request_class_extraction", "aider_context_request_integration", "base_coder_old"] (total: 4)
- **Side Effects**: writes_log, modifies_state, network_io

### 2. ContextRequestHandler (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium
- **Calls**: []
- **Used by**: ["base_coder", "test_context_request_class_extraction", "aider_context_request_integration", "test_simplified_ir_request", "test_ir_request_fix", "..."] (total: 6)
- **Side Effects**: none

⚠️ **Context Selection Notice**
The following code context was selected based on the user's query and initial relevance scoring.
It is not guaranteed to be the most relevant or complete subset of the codebase.

You, as the AI assistant, are responsible for validating assumptions, identifying potential missing dependencies, and requesting further context if needed.

A full index of related modules and functions is available below for reference.

## KEY IMPLEMENTATIONS (2 functions)
Complete code available on request for any function.

### 1. process_context_request
```python
    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
    # ... (implementation continues)
```

### 2. ContextRequestHandler
```python
class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

```

## AWARENESS INDEX (15 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 base_coder.py
- **Functions**: process_context_requests
- **Other**: context, request

### 📁 base_coder_old.py
- **Functions**: process_context_requests
- **Other**: context

### 📁 surgical_file_extractor.py
- **Functions**: extract_symbol_content, extract_symbol_range

### 📁 help.py
- **Other**: context

### 📁 linter.py
- **Other**: context

### 📁 main.py
- **Other**: context

### 📁 mid_level_ir_with_inheritance.py
- **Other**: context

### 📁 repomap.py
- **Other**: context

### 📁 scrape.py
- **Other**: context

### 📁 test_context_request_class_extraction.py
- **Other**: request

### 📁 test_ir_context_request.py
- **Other**: request

**Summary**: 4 functions, 0 classes across 11 files
*To request specific implementations, ask: "Show me the implementation of [function_name]"*


