# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 17:39:17
# Project: .
# User Query: How does user authentication work in the system?
# Task Description: Analyze authentication workflow and security components
# Task Type: security_analysis
# Max Tokens: 8000
# Focus Entities: authenticate, user, login, auth
# Package Size: 1,392 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does user authentication work in the system?

**Semantic Analysis**:
- **Intent**: security_analysis
- **Scope**: system_overview
- **Confidence**: 0.77
- **Domain Concepts**: 2
  - Technical: authentication (confidence: 0.90)
  - Business: user (confidence: 0.80)

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 0
**Selection Confidence**: 0.24

---

## Selected Components (Ranked by Semantic Relevance)

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

