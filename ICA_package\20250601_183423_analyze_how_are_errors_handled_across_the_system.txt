# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 18:34:23
# Project: .
# User Query: How are errors handled across the system?
# Task Description: Analyze: How are errors handled across the system?
# Task Type: general_analysis
# Max Tokens: 8000
# Focus Entities: None
# Package Size: 4,885 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How are errors handled across the system?

**Semantic Analysis**:
- **Intent**: debugging_assistance
- **Scope**: system_overview
- **Confidence**: 0.50
- **Domain Concepts**: 0

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 8
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. create
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['Model', 'summarize_all', 'tool_warning', 'clone', 'update']... (total: 7)
- **Used By**: ['capture_full_prompt', 'base_coder', 'commands', 'base_coder_old'] (total: 4)

### 2. get_announcements
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['append', 'get_thinking_tokens', 'get_reasoning_effort', 'get', 'get_rel_repo_dir']... (total: 9)
- **Used By**: ['commands', 'gui', 'base_coder', 'base_coder_old'] (total: 4)

### 3. __init__
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['Analytics', 'InputOutput', 'CoderPrompts', 'Commands', 'GitRepo']... (total: 20)
- **Used By**: ['capture_full_prompt', 'base_coder', 'utils', 'base_coder_old'] (total: 4)

### 4. get_repo_map
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['get_cur_message_text', 'get_file_mentions', 'get_ident_mentions', 'update', 'get_ident_filename_matches']... (total: 7)
- **Used By**: ['commands', 'base_coder', 'base_coder_old', 'debug_symbol_extraction', 'surgical_file_extractor'] (total: 5)

### 5. get_images_message
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['get', 'is_image_file', 'guess_type', 'open', 'decode']... (total: 9)
- **Used By**: ['base_coder', 'base_coder_old'] (total: 2)

### 6. run
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['user_input', 'run_one', 'copy_context', 'get_input', 'show_undo_hint']... (total: 6)
- **Used By**: ['commands', 'base_coder', 'base_coder_old', 'update-history', 'linter']... (total: 10)

### 7. format_chat_chunks
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['tool_output', 'choose_fence', 'fmt_system_prompt', 'upper', 'strip']... (total: 14)
- **Used By**: ['capture_full_prompt', 'base_coder', 'commands', 'base_coder_old'] (total: 4)

### 8. send_message
- **File**: aider-main\aider\coders\base_coder.py
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected workflow 'Use...
- **Calls**: ['event', 'llm_started', 'format_messages', 'all_messages', 'check_tokens']... (total: 20)
- **Used By**: ['base_coder', 'base_coder_old'] (total: 2)

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

