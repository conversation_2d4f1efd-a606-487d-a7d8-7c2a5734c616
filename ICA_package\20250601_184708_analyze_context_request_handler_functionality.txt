# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 18:47:08
# Project: .
# User Query: How does the context_request_handler work?
# Task Description: Analyze context request handler functionality
# Task Type: workflow_analysis
# Max Tokens: 8000
# Focus Entities: context_request_handler
# Package Size: 4,943 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does the context_request_handler work?

**Semantic Analysis**:
- **Intent**: workflow_analysis
- **Scope**: module_level
- **Confidence**: 0.80
- **Domain Concepts**: 0

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 6
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. ⚙️ run
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 1.300
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['user_input', 'run_one', 'copy_context']... (total: 6)
- **Used By**: ['recording_audio', 'base_coder_old', 'io']... (total: 10)
- **Side Effects**: ['network_io', 'modifies_state']

### 2. ⚙️ process_file_requests
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 1.300
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['findall', 'strip', 'loads']... (total: 20)
- **Side Effects**: ['network_io', 'modifies_state']...

### 3. ⚙️ process_context_request
- **Type**: Function
- **File**: aider_context_request_integration.py
- **Module**: aider_context_request_integration
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['process_context_request', 'get', 'render_augmented_prompt'] (total: 3)
- **Used By**: ['base_coder_old', 'base_coder', 'aider_context_request_integration']... (total: 4)
- **Side Effects**: ['writes_log', 'network_io']...

### 4. ⚙️ process_context_request
- **Type**: Function
- **File**: context_request_handler.py
- **Module**: context_request_handler
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['join', '_get_from_cache', '_extract_symbol_content']... (total: 9)
- **Used By**: ['base_coder_old', 'base_coder', 'aider_context_request_integration']... (total: 4)
- **Side Effects**: ['writes_log', 'network_io']...

### 5. ⚙️ get_files_that_import
- **Type**: Function
- **File**: aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: High
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['_get_repo_map', '_normalize_path', 'relpath']... (total: 10)
- **Used By**: ['aider_integration_service', 'surgical_context_extractor'] (total: 2)
- **Side Effects**: ['writes_log', 'network_io']...

### 6. ⚙️ get_files_imported_by
- **Type**: Function
- **File**: aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['_get_repo_map', '_normalize_path', 'relpath']... (total: 8)
- **Used By**: ['aider_integration_service', 'surgical_context_extractor'] (total: 2)
- **Side Effects**: ['writes_log', 'network_io']...

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

