# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 21:56:37
# Project: .
# User Query: How does <PERSON><PERSON> handle Git operations?
# Task Description: Test query 5: Git Integration
# Task Type: analysis
# Max Tokens: 8000
# Focus Entities: None
# Package Size: 4,697 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does Aid<PERSON> handle Git operations?

**Semantic Analysis**:
- **Intent**: component_discovery
- **Scope**: module_level
- **Confidence**: 0.50
- **Domain Concepts**: 0

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 6
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. ⚙️ show_exhausted_error
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['token_count', 'get', 'all_messages']... (total: 8)
- **Side Effects**: ['network_io', 'modifies_state']...

### 2. ⚙️ show_exhausted_error
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder_old.py
- **Module**: base_coder_old
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 1.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['token_count', 'get', 'all_messages']... (total: 8)
- **Side Effects**: ['network_io', 'modifies_state']...

### 3. ⚙️ get_files_that_import
- **Type**: Function
- **File**: aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: High
- **Relevance Score**: 1.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['_get_repo_map', '_normalize_path', 'relpath']... (total: 10)
- **Used By**: ['surgical_context_extractor', 'aider_integration_service'] (total: 2)
- **Side Effects**: ['network_io', 'modifies_state']...

### 4. ⚙️ get_files_imported_by
- **Type**: Function
- **File**: aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 1.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['_get_repo_map', '_normalize_path', 'relpath']... (total: 8)
- **Used By**: ['surgical_context_extractor', 'aider_integration_service'] (total: 2)
- **Side Effects**: ['network_io', 'modifies_state']...

### 5. ⚙️ find_common_root
- **Type**: Function
- **File**: aider-main\aider\utils.py
- **Module**: utils
- **Line**: N/A
- **Cluster**: utils
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 0.800
- **Semantic Rationale**: Selected from utils cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['safe_abs_path', 'dirname', 'commonpath']... (total: 4)
- **Used By**: ['base_coder_old', 'base_coder'] (total: 2)
- **Side Effects**: ['network_io']

### 6. ⚙️ find_non_self_methods
- **Type**: Function
- **File**: aider-main\benchmark\refactor_tools.py
- **Module**: refactor_tools
- **Line**: N/A
- **Cluster**: utils
- **Criticality**: Medium
- **Change Risk**: Low
- **Relevance Score**: 0.500
- **Semantic Rationale**: Selected from utils cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Cross Cutting
📋 Rationale: Selected utility and infr...
- **Calls**: ['find_python_files', 'open', 'parse']... (total: 7)
- **Used By**: ['refactor_tools'] (total: 1)
- **Side Effects**: ['modifies_file']

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

