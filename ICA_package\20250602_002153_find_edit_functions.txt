# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 00:21:53
# Project: .
# User Query: How does aider apply edits to files?
# Task Description: Find edit functions
# Task Type: general_analysis
# Max Tokens: 8000
# Focus Entities: None
# Package Size: 3,485 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does aider apply edits to files?

**Semantic Analysis**:
- **Intent**: workflow_analysis
- **Scope**: single_component
- **Confidence**: 0.64
- **Domain Concepts**: 1
  - Data: file (confidence: 0.70)

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 5
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. ⚙️ apply_edits
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 5000.000
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

### 2. ⚙️ apply_edits
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder_old.py
- **Module**: base_coder_old
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 5000.000
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

### 3. ⚙️ apply_edits_dry_run
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 4000.000
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

### 4. ⚙️ apply_edits_dry_run
- **Type**: Function
- **File**: aider-main\aider\coders\base_coder_old.py
- **Module**: base_coder_old
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 4000.000
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

### 5. ⚙️ _
- **Type**: Function
- **File**: aider-main\aider\io.py
- **Module**: io
- **Line**: N/A
- **Cluster**: io
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 2400.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['suspend_to_bg', 'add', 'Condition'] (total: 3)
- **Side Effects**: ['none']

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

