# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:01:27
# Project: .
# User Query: how does the system manage position?
# Task Description: Test class methods for: how does the system manage position?
# Task Type: debugging
# Max Tokens: 3000
# Focus Entities: None
# Package Size: 2,668 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: how does the system manage position?

**Semantic Analysis**:
- **Intent**: workflow_analysis
- **Scope**: system_overview
- **Confidence**: 0.50
- **Domain Concepts**: 0

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 3
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. 🏛️ AiderProjectManager
- **Type**: Class
- **File**: aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: all
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 1250.000
- **Semantic Rationale**: Selected from all cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Used By**: ['aider_integration_service'] (total: 1)
- **Side Effects**: ['none']

### 2. 🏛️ DependencyIntegrationManager
- **Type**: Class
- **File**: code_generation\dependency_integration_manager.py
- **Module**: dependency_integration_manager
- **Line**: N/A
- **Cluster**: all
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 1250.000
- **Semantic Rationale**: Selected from all cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

### 3. 🏛️ SystemWorkflow
- **Type**: Class
- **File**: intelligent_context_models.py
- **Module**: intelligent_context_models
- **Line**: N/A
- **Cluster**: all
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 1250.000
- **Semantic Rationale**: Selected from all cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Side Effects**: ['none']

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

