# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:09:56
# Project: .
# User Query: how does the system manage position?
# Task Description: Test higher limits: how does the system manage position?
# Task Type: debugging
# Max Tokens: 5000
# Focus Entities: None
# Package Size: 1,945 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. AiderProjectManager (class)
- File: aider_integration_service.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (15 methods)
- **__init__** (complexity: 0)
- **_extract_class_info_from_repomap** (complexity: 0)
- **_find_file_defining_symbol** (complexity: 0)
- **_get_module_path** (complexity: 0)
- **_get_repo_map** (complexity: 0)
- **_is_cache_valid** (complexity: 0)
- **_load_dependency_data** (complexity: 0)
- **_normalize_path** (complexity: 0)
- ... (+7 more methods)
- **Calls**: []
- **Used by**: ["aider_integration_service"] (total: 1)
- **Side Effects**: none

### 2. DependencyIntegrationManager (class)
- File: code_generation\dependency_integration_manager.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **_calculate_integration_complexity** (complexity: 0)
- **_detect_circular_dependencies** (complexity: 0)
- **_extract_imports** (complexity: 0)
- **_extract_module_name** (complexity: 0)
- **_find_missing_dependencies** (complexity: 0)
- **analyze_dependencies** (complexity: 0)
- **suggest_integration_strategy** (complexity: 0)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. SystemWorkflow (class)
- File: intelligent_context_models.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

## AWARENESS INDEX (2 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 models.py
- **Classes**: ModelInfoManager

### 📁 openrouter.py
- **Classes**: OpenRouterModelManager

**Summary**: 0 functions, 2 classes across 2 files
*To request specific implementations, use: "IR_REQUEST"*


