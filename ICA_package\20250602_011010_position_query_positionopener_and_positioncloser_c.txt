# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:10:10
# Project: .
# User Query: PositionOpener and PositionCloser classes
# Task Description: Position query: PositionOpener and PositionCloser classes
# Task Type: debugging
# Max Tokens: 20000
# Focus Entities: None
# Package Size: 2,146 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: writes_log, modifies_state, database_io

### 2. get_derived_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: writes_log, modifies_state, database_io

### 3. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderIntegrationService`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderIntegrationService` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get_base_classes_of"] (total: 1)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: modifies_state, network_io

## AWARENESS INDEX (2 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 inheritance_analyzer.py
- **Functions**: _get_all_parent_classes

### 📁 surgical_context_extractor.py
- **Other**: CLASS

**Summary**: 1 functions, 0 classes across 2 files
*To request specific implementations, use: "IR_REQUEST"*


