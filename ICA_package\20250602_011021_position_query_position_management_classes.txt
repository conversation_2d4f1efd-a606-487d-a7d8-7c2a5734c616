# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:10:21
# Project: .
# User Query: position management classes
# Task Description: Position query: position management classes
# Task Type: debugging
# Max Tokens: 20000
# Focus Entities: None
# Package Size: 2,717 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: writes_log, modifies_state, database_io

### 2. get_derived_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: writes_log, modifies_state, database_io

### 3. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderIntegrationService`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderIntegrationService` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get_base_classes_of"] (total: 1)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: modifies_state, network_io

## AWARENESS INDEX (11 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 debug_position_query.py
- **Functions**: debug_position_query

### 📁 fix_position_context_selection.py
- **Functions**: enhance_position_query_handling, test_position_query

### 📁 inheritance_analyzer.py
- **Functions**: _get_all_parent_classes

### 📁 simple_position_diagnostic.py
- **Functions**: check_position_entities_in_ir

### 📁 test_ai_centric_scoring.py
- **Functions**: test_ai_centric_position_scoring

### 📁 test_higher_token_limits.py
- **Functions**: test_position_specific_query

### 📁 test_position_query.py
- **Functions**: test_position_query_analysis, test_position_keyword_matching

### 📁 repomap.py
- **Other**: G

### 📁 surgical_context_extractor.py
- **Other**: CLASS

**Summary**: 9 functions, 0 classes across 9 files
*To request specific implementations, use: "IR_REQUEST"*


