# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:10:42
# Project: .
# User Query: position entry and exit system
# Task Description: Position query: position entry and exit system
# Task Type: debugging
# Max Tokens: 20000
# Focus Entities: None
# Package Size: 2,372 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. ComplexityAnalyzer (class)
- File: code_generation\quality_assurance_engine.py

- **Inherits From**: ast.NodeVisitor
- Criticality: low | Risk: low

#### 🔁 Class Inheritance
- **Inheritance chain**: ast.NodeVisitor

#### 🏛️ Class Methods (7 methods)
- **__init__** (complexity: 0)
- **visit_AsyncFunctionDef** (complexity: 0)
- **visit_ExceptHandler** (complexity: 0)
- **visit_For** (complexity: 0)
- **visit_FunctionDef** (complexity: 0)
- **visit_If** (complexity: 0)
- **visit_While** (complexity: 0)
- **Calls**: []
- **Used by**: ["quality_assurance_engine"] (total: 1)
- **Side Effects**: none

### 2. SystemWorkflow (class)
- File: intelligent_context_models.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. AiderSystemEvaluator (class)
- File: real_world_aider_test.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (4 methods)
- **__init__** (complexity: 0)
- **_build_aider_knowledge_base** (complexity: 0)
- **evaluate_package_relevance** (complexity: 0)
- **run_real_world_aider_test** (complexity: 0)
- **Calls**: []
- **Used by**: ["real_world_aider_test"] (total: 1)
- **Side Effects**: none

## AWARENESS INDEX (12 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 system_architecture_generator.py
- **Classes**: SystemArchitecture, SystemArchitectureGenerator

### 📁 analytics.py
- **Functions**: get_system_info

### 📁 architecture_diagram_generator.py
- **Functions**: generate_system_overview_diagram, generate_business_system_architecture

### 📁 base_coder.py
- **Functions**: fmt_system_prompt

### 📁 base_coder_old.py
- **Functions**: fmt_system_prompt

### 📁 commands.py
- **Functions**: cmd_exit

### 📁 component_purpose_analyzer.py
- **Functions**: calculate_complexity_score

### 📁 context_bundle_builder.py
- **Functions**: _calculate_complexity_score

### 📁 criticality_scorer.py
- **Functions**: _calculate_complexity_score

### 📁 debug_position_query.py
- **Functions**: debug_position_query

**Summary**: 10 functions, 2 classes across 10 files
*To request specific implementations, use: "IR_REQUEST"*


