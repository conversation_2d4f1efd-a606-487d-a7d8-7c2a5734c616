# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:11:52
# Project: .
# User Query: how does the system manage dependencies and integration?
# Task Description: No cache test 2: how does the system manage dependencies and integration?
# Task Type: debugging
# Max Tokens: 10000
# Focus Entities: None
# Package Size: 2,762 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. DependencyIntegrationManager (class)
- File: code_generation\dependency_integration_manager.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **_calculate_integration_complexity** (complexity: 0)
- **_detect_circular_dependencies** (complexity: 0)
- **_extract_imports** (complexity: 0)
- **_extract_module_name** (complexity: 0)
- **_find_missing_dependencies** (complexity: 0)
- **analyze_dependencies** (complexity: 0)
- **suggest_integration_strategy** (complexity: 0)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 2. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder_old", "base_coder"] (total: 2)
- **Side Effects**: none

### 3. AiderIntegrationService (class)
- File: aider_integration_service.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🏛️ Class Methods (39 methods)
- **__init__** (complexity: 0)
- **_determine_relationship** (complexity: 0)
- **_explain_risk** (complexity: 0)
- **_explain_safety** (complexity: 0)
- **_extract_critical_entities** (complexity: 0)
- **_extract_dependency_map** (complexity: 0)
- **_extract_related_entities** (complexity: 0)
- **_extract_safe_entities** (complexity: 0)
- ... (+31 more methods)
- **Calls**: []
- **Used by**: ["test_direct_extraction", "surgical_extraction_demo", "intelligent_context_selector", "trace_position_discovery", "context_request_handler", "..."] (total: 7)
- **Side Effects**: none

## AWARENESS INDEX (5 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 semantic_context_integration.py
- **Classes**: SemanticContextIntegration

### 📁 aider_integration_service.py
- **Classes**: AiderProjectManager

### 📁 intelligent_context_models.py
- **Classes**: SystemWorkflow

### 📁 models.py
- **Classes**: ModelInfoManager

### 📁 openrouter.py
- **Classes**: OpenRouterModelManager

**Summary**: 0 functions, 5 classes across 5 files
*To request specific implementations, use: "IR_REQUEST"*


