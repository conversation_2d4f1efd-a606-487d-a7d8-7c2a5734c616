# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:12:24
# Project: .
# User Query: context request handling and IR generation system
# Task Description: Realistic test: context request handling and IR generation system
# Task Type: debugging
# Max Tokens: 30000
# Focus Entities: None
# Package Size: 2,176 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder_old", "base_coder"] (total: 2)
- **Side Effects**: none

### 2. GenerationRequest (class)
- File: code_generation\code_template_engine.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. ContextRequest (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["test_context_request_class_extraction", "context_request_handler"] (total: 2)
- **Side Effects**: none

## AWARENESS INDEX (15 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 context_request_handler.py
- **Functions**: parse_context_request, process_context_request
- **Classes**: ContextRequestHandler, SymbolRequest

### 📁 aider_context_request_integration.py
- **Functions**: detect_context_request, process_context_request, get_context_request_summary

### 📁 base_coder.py
- **Functions**: process_context_requests, process_ir_context_requests

### 📁 base_coder_old.py
- **Functions**: process_context_requests

### 📁 code_generation_pipeline.py
- **Classes**: CodeGenerationResult, CodeGenerationPipeline

### 📁 context_bundle_builder.py
- **Classes**: EnhancedContextEntity, EnhancedContextBundle, ContextBundleBuilder

**Summary**: 8 functions, 7 classes across 6 files
*To request specific implementations, use: "IR_REQUEST"*


