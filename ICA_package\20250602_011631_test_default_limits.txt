# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:16:31
# Project: .
# User Query: how does the context system work?
# Task Description: Test default limits
# Task Type: general_analysis
# Max Tokens: 20000
# Focus Entities: None
# Package Size: 2,020 characters

================================================================================

## CRITICAL ENTITIES (3 most important)

### 1. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder_old", "base_coder"] (total: 2)
- **Side Effects**: none

### 2. EnhancedContextEntity (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

### 3. EnhancedContextBundle (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

## AWARENESS INDEX (12 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 context_bundle_builder.py
- **Classes**: ContextBundleBuilder

### 📁 context_request_handler.py
- **Classes**: ContextRequest, ContextRequestHandler

### 📁 hierarchical_context_selector.py
- **Classes**: ContextSelectionStrategy, ArchitecturalContext, HierarchicalContextSelector

### 📁 intelligent_context_models.py
- **Classes**: QueryContext, OptimizedContextSelection, ContextCompletenessReport, ContextExplanation

### 📁 intelligent_context_selector.py
- **Classes**: ContextPriority, ContextEntity

**Summary**: 0 functions, 12 classes across 5 files
*To request specific implementations, use: "IR_REQUEST"*


