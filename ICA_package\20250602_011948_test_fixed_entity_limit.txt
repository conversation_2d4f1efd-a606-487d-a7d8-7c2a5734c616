# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:19:48
# Project: .
# User Query: how does the context system work with integration?
# Task Description: Test fixed entity limit
# Task Type: general_analysis
# Max Tokens: 25000
# Focus Entities: None
# Package Size: 5,340 characters

================================================================================

## CRITICAL ENTITIES (9 most important)

### 1. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: none

### 2. SemanticContextIntegration (class)
- File: semantic_context_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **_calculate_selection_confidence** (complexity: 0)
- **_convert_to_ir_context_format** (complexity: 0)
- **_map_score_to_criticality** (complexity: 0)
- **create_enhanced_llm_package** (complexity: 0)
- **enhance_ir_context_selection** (complexity: 0)
- **integrate_with_existing_system** (complexity: 0)
- **test_semantic_integration** (complexity: 0)
- **Calls**: []
- **Used by**: ["test_semantic_vs_keyword", "semantic_context_integration"] (total: 2)
- **Side Effects**: none

### 3. AiderIntegrationService (class)
- File: aider_integration_service.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🏛️ Class Methods (39 methods)
- **__init__** (complexity: 0)
- **_determine_relationship** (complexity: 0)
- **_explain_risk** (complexity: 0)
- **_explain_safety** (complexity: 0)
- **_extract_critical_entities** (complexity: 0)
- **_extract_dependency_map** (complexity: 0)
- **_extract_related_entities** (complexity: 0)
- **_extract_safe_entities** (complexity: 0)
- ... (+31 more methods)
- **Calls**: []
- **Used by**: ["aider_context_request_integration", "context_request_handler", "debug_symbol_extraction", "surgical_extraction_demo", "intelligent_context_selector", "..."] (total: 7)
- **Side Effects**: none

### 4. EnhancedContextEntity (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

### 5. EnhancedContextBundle (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

### 6. ContextBundleBuilder (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (20 methods)
- **__init__** (complexity: 0)
- **_build_confidence_analysis** (complexity: 0)
- **_build_dependency_graph** (complexity: 0)
- **_build_enhanced_bundle** (complexity: 0)
- **_build_entity_map** (complexity: 0)
- **_calculate_complexity_score** (complexity: 0)
- **_calculate_confidence_gap_score** (complexity: 0)
- **_calculate_dependency_proximity** (complexity: 0)
- ... (+12 more methods)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 7. ContextRequest (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["test_context_request_class_extraction", "context_request_handler"] (total: 2)
- **Side Effects**: none

### 8. ContextRequestHandler (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🏛️ Class Methods (7 methods)
- **__init__** (complexity: 0)
- **_extract_symbol_content** (complexity: 0)
- **_find_file_for_symbol** (complexity: 0)
- **_get_from_cache** (complexity: 0)
- **_update_cache** (complexity: 0)
- **parse_context_request** (complexity: 0)
- **process_context_request** (complexity: 0)
- **Calls**: []
- **Used by**: ["debug_class_methods_ir", "base_coder", "test_semantic_integration", "fix_position_context_selection", "test_no_cache_final", "..."] (total: 10)
- **Side Effects**: none

### 9. DependencyIntegrationManager (class)
- File: code_generation\dependency_integration_manager.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **_calculate_integration_complexity** (complexity: 0)
- **_detect_circular_dependencies** (complexity: 0)
- **_extract_imports** (complexity: 0)
- **_extract_module_name** (complexity: 0)
- **_find_missing_dependencies** (complexity: 0)
- **analyze_dependencies** (complexity: 0)
- **suggest_integration_strategy** (complexity: 0)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

## AWARENESS INDEX (1 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 test_ai_centric_scoring.py
- **Functions**: test_with_real_context_system

**Summary**: 1 functions, 0 classes across 1 files
*To request specific implementations, use: "IR_REQUEST"*


