# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:20:38
# Project: .
# User Query: context request handling system
# Task Description: Test 8 entities
# Task Type: general_analysis
# Max Tokens: 30000
# Focus Entities: None
# Package Size: 4,334 characters

================================================================================

## CRITICAL ENTITIES (8 most important)

### 1. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: none

### 2. ContextRequest (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["test_context_request_class_extraction", "context_request_handler"] (total: 2)
- **Side Effects**: none

### 3. ContextRequestHandler (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🏛️ Class Methods (7 methods)
- **__init__** (complexity: 0)
- **_extract_symbol_content** (complexity: 0)
- **_find_file_for_symbol** (complexity: 0)
- **_get_from_cache** (complexity: 0)
- **_update_cache** (complexity: 0)
- **parse_context_request** (complexity: 0)
- **process_context_request** (complexity: 0)
- **Calls**: []
- **Used by**: ["debug_class_methods_ir", "base_coder", "test_semantic_integration", "fix_position_context_selection", "test_no_cache_final", "..."] (total: 10)
- **Side Effects**: none

### 4. detect_context_request (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["parse_context_request", "join"] (total: 2)
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: modifies_state, network_io, writes_log

### 5. process_context_request (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["process_context_request", "get", "render_augmented_prompt"] (total: 3)
- **Used by**: ["fix_position_context_selection", "test_context_request_class_extraction", "base_coder_old", "aider_context_request_integration", "base_coder", "..."] (total: 8)
- **Side Effects**: modifies_state, network_io, writes_log

### 6. get_context_request_summary (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["join"] (total: 1)
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: none

### 7. process_context_requests (method)
- File: aider-main\aider\coders\base_coder.py
- **Belongs to Class**: `Coder`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["tool_warning", "getcwd", "basename", "exists", "join", "..."] (total: 20)
- **Used by**: []
- **Side Effects**: network_io, modifies_state, database_io

### 8. process_ir_context_requests (method)
- File: aider-main\aider\coders\base_coder.py
- **Belongs to Class**: `Coder`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["tool_warning", "search", "strip", "group", "loads", "..."] (total: 19)
- **Used by**: []
- **Side Effects**: modifies_state, network_io, writes_log

## AWARENESS INDEX
No additional entities available for awareness context.

