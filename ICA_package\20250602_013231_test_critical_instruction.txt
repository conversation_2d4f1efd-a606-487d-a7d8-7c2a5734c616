# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:32:31
# Project: .
# User Query: how does the context system work?
# Task Description: Test critical instruction
# Task Type: general_analysis
# Max Tokens: 15000
# Focus Entities: None
# Package Size: 3,751 characters

================================================================================

Critical Instruction:
Before answering the user query, identify the most relevant entities and their associated methods based on the provided context. This ensures your reasoning targets the core components rather than surface-level details.

## CRITICAL ENTITIES (8 most important)

### 1. AiderContextRequestIntegration (class)
- File: aider_context_request_integration.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **detect_context_request** (complexity: 0)
- **get_context_request_summary** (complexity: 0)
- **get_llm_instructions** (complexity: 0)
- **has_reached_max_iterations** (complexity: 0)
- **process_context_request** (complexity: 0)
- **reset_iteration_counter** (complexity: 0)
- **update_conversation_history** (complexity: 0)
- **Calls**: []
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: none

### 2. EnhancedContextEntity (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

### 3. EnhancedContextBundle (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["context_bundle_builder"] (total: 1)
- **Side Effects**: none

### 4. ContextBundleBuilder (class)
- File: context_bundle_builder.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (20 methods)
- **__init__** (complexity: 0)
- **_build_confidence_analysis** (complexity: 0)
- **_build_dependency_graph** (complexity: 0)
- **_build_enhanced_bundle** (complexity: 0)
- **_build_entity_map** (complexity: 0)
- **_calculate_complexity_score** (complexity: 0)
- **_calculate_confidence_gap_score** (complexity: 0)
- **_calculate_dependency_proximity** (complexity: 0)
- ... (+12 more methods)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 5. ContextRequest (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["test_context_request_class_extraction", "context_request_handler"] (total: 2)
- **Side Effects**: none

### 6. ContextRequestHandler (class)
- File: context_request_handler.py

- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🏛️ Class Methods (7 methods)
- **__init__** (complexity: 0)
- **_extract_symbol_content** (complexity: 0)
- **_find_file_for_symbol** (complexity: 0)
- **_get_from_cache** (complexity: 0)
- **_update_cache** (complexity: 0)
- **parse_context_request** (complexity: 0)
- **process_context_request** (complexity: 0)
- **Calls**: []
- **Used by**: ["test_semantic_integration", "test_llm_template_fix", "enhanced_metadata_comprehensive_test", "fix_position_context_selection", "test_context_request_class_extraction", "..."] (total: 10)
- **Side Effects**: none

### 7. ContextSelectionStrategy (class)
- File: aider-main\aider\context_request\hierarchical_context_selector.py

- **Inherits From**: Enum
- Criticality: low | Risk: low

#### 🔁 Class Inheritance
- **Inheritance chain**: Enum
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 8. ArchitecturalContext (class)
- File: aider-main\aider\context_request\hierarchical_context_selector.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["hierarchical_context_selector"] (total: 1)
- **Side Effects**: none

## AWARENESS INDEX
No additional entities available for awareness context.

