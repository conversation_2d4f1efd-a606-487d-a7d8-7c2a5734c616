# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:32:41
# Project: .
# User Query: show me the integration classes
# Task Description: Test query 1
# Task Type: general_analysis
# Max Tokens: 10000
# Focus Entities: None
# Package Size: 2,685 characters

================================================================================

Critical Instruction:
Before answering the user query, identify the most relevant entities and their associated methods based on the provided context. This ensures your reasoning targets the core components rather than surface-level details.

## CRITICAL ENTITIES (5 most important)

### 1. show (function)
- File: aider-main\benchmark\benchmark.py


- Criticality: medium | Risk: low
- **Calls**: []
- **Used by**: ["benchmark"] (total: 1)
- **Side Effects**: writes_log, network_io

### 2. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: modifies_container, network_io, modifies_state

### 3. get_derived_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderProjectManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderProjectManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["_is_cache_valid", "_extract_class_info_from_repomap", "append", "join", "_get_module_path", "..."] (total: 6)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: modifies_container, network_io, modifies_state

### 4. _suggest_integration (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderIntegrationService`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `AiderIntegrationService` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get"] (total: 1)
- **Used by**: ["aider_integration_service"] (total: 1)
- **Side Effects**: network_io

### 5. get_base_classes_of (method)
- File: aider_integration_service.py
- **Belongs to Class**: `AiderIntegrationService`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderIntegrationService` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get_base_classes_of"] (total: 1)
- **Used by**: ["aider_integration_service", "surgical_context_extractor"] (total: 2)
- **Side Effects**: modifies_state, network_io

## AWARENESS INDEX
No additional entities available for awareness context.

