# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:32:51
# Project: .
# User Query: how does dependency management work?
# Task Description: Test query 2
# Task Type: general_analysis
# Max Tokens: 10000
# Focus Entities: None
# Package Size: 2,372 characters

================================================================================

Critical Instruction:
Before answering the user query, identify the most relevant entities and their associated methods based on the provided context. This ensures your reasoning targets the core components rather than surface-level details.

## CRITICAL ENTITIES (5 most important)

### 1. DependencyAnalyzer (class)
- File: mid_level_ir\dependency_analyzer.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (7 methods)
- **__init__** (complexity: 0)
- **_analyze_module_dependencies** (complexity: 0)
- **_calculate_dependency_strengths** (complexity: 0)
- **_process_from_import** (complexity: 0)
- **_process_import** (complexity: 0)
- **_should_include_dependency** (complexity: 0)
- **analyze** (complexity: 0)
- **Calls**: []
- **Used by**: ["main", "mid_level_ir_with_inheritance"] (total: 2)
- **Side Effects**: none

### 2. DependencyAnalysis (class)
- File: code_generation\dependency_integration_manager.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["dependency_integration_manager"] (total: 1)
- **Side Effects**: none

### 3. DependencyIntegrationManager (class)
- File: code_generation\dependency_integration_manager.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low

#### 🏛️ Class Methods (8 methods)
- **__init__** (complexity: 0)
- **_calculate_integration_complexity** (complexity: 0)
- **_detect_circular_dependencies** (complexity: 0)
- **_extract_imports** (complexity: 0)
- **_extract_module_name** (complexity: 0)
- **_find_missing_dependencies** (complexity: 0)
- **analyze_dependencies** (complexity: 0)
- **suggest_integration_strategy** (complexity: 0)
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 4. DependencyInfo (class)
- File: mid_level_ir\ir_context.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["dependency_analyzer"] (total: 1)
- **Side Effects**: none

### 5. ContextualDependencyMap (class)
- File: surgical_context_extractor.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["surgical_context_extractor"] (total: 1)
- **Side Effects**: none

## AWARENESS INDEX
No additional entities available for awareness context.

