# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-02 01:33:01
# Project: .
# User Query: what are the context selection algorithms?
# Task Description: Test query 3
# Task Type: general_analysis
# Max Tokens: 10000
# Focus Entities: None
# Package Size: 2,356 characters

================================================================================

Critical Instruction:
Before answering the user query, identify the most relevant entities and their associated methods based on the provided context. This ensures your reasoning targets the core components rather than surface-level details.

## CRITICAL ENTITIES (4 most important)

### 1. enhance_ir_context_selection (method)
- File: semantic_context_integration.py
- **Belongs to Class**: `SemanticContextIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: low

#### 🔁 Class Context
- Part of `SemanticContextIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["select_intelligent_context", "_convert_to_ir_context_format", "analyze_complete_query", "_calculate_selection_confidence"] (total: 4)
- **Used by**: ["semantic_context_integration", "test_semantic_vs_keyword"] (total: 2)
- **Side Effects**: modifies_container, modifies_state, writes_log

### 2. ContextSelectionStrategy (class)
- File: aider-main\aider\context_request\hierarchical_context_selector.py

- **Inherits From**: Enum
- Criticality: low | Risk: low

#### 🔁 Class Inheritance
- **Inheritance chain**: Enum
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 3. OptimizedContextSelection (class)
- File: intelligent_context_models.py

- **Inherits From**: No inheritance (base class)
- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: []
- **Side Effects**: none

### 4. detect_context_request (method)
- File: aider_context_request_integration.py
- **Belongs to Class**: `AiderContextRequestIntegration`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `AiderContextRequestIntegration` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["parse_context_request", "join"] (total: 2)
- **Used by**: ["base_coder", "base_coder_old"] (total: 2)
- **Side Effects**: writes_log, modifies_state, network_io

## AWARENESS INDEX (1 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 test_hierarchical_context_selection.py
- **Functions**: test_hierarchical_context_selection

**Summary**: 1 functions, 0 classes across 1 files
*To request specific implementations, use: "IR_REQUEST"*


