# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 22:10:31
# Project: aider-main
# User Query: How does <PERSON><PERSON> handle errors and exceptions?
# Task Description: Test query 7: Error Handling
# Task Type: analysis
# Max Tokens: 8000
# Focus Entities: None
# Package Size: 4,594 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does Aid<PERSON> handle errors and exceptions?

**Semantic Analysis**:
- **Intent**: debugging_assistance
- **Scope**: module_level
- **Confidence**: 0.50
- **Domain Concepts**: 0

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 6
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. ⚙️ show_send_output
- **Type**: Function
- **File**: aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 14.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['_stop_waiting_spinner', 'tool_error', 'sha1']... (total: 14)
- **Used By**: ['base_coder', 'base_coder_old'] (total: 2)
- **Side Effects**: ['writes_log', 'network_io']...

### 2. ⚙️ show_send_output
- **Type**: Function
- **File**: aider\coders\base_coder_old.py
- **Module**: base_coder_old
- **Line**: N/A
- **Cluster**: core
- **Criticality**: High
- **Change Risk**: Medium
- **Relevance Score**: 14.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['_stop_waiting_spinner', 'tool_error', 'sha1']... (total: 14)
- **Used By**: ['base_coder', 'base_coder_old'] (total: 2)
- **Side Effects**: ['writes_log', 'network_io']...

### 3. 🏛️ ContextRequestHandler
- **Type**: Class
- **File**: aider\context_request\context_request_handler.py
- **Module**: context_request_handler
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 12.000
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Used By**: ['base_coder', 'aider_context_request_integration'] (total: 2)
- **Side Effects**: ['none']

### 4. 🏛️ SmartMapRequestHandler
- **Type**: Class
- **File**: aider\smart_map_request_handler.py
- **Module**: smart_map_request_handler
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 12.000
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Used By**: ['search_repo', 'base_coder'] (total: 2)
- **Side Effects**: ['none']

### 5. ⚙️ get_input
- **Type**: Function
- **File**: aider\io.py
- **Module**: io
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: High
- **Relevance Score**: 9.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['rule', 'ring_bell', 'get_rel_fname']... (total: 20)
- **Used By**: ['base_coder', 'base_coder_old'] (total: 2)
- **Side Effects**: ['modifies_file', 'writes_log']...

### 6. 🏛️ AiderIntegrationService
- **Type**: Class
- **File**: aider\context_request\aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 7.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Used By**: ['intelligent_context_selector', 'aider_context_request_integration', 'context_request_handler'] (total: 3)
- **Side Effects**: ['none']

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

