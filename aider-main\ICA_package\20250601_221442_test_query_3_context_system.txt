# IR CONTEXT PACKAGE - AUTOMATICALLY SAVED
# Generated: 2025-06-01 22:14:42
# Project: aider-main
# User Query: How does <PERSON><PERSON> gather context from the repository?
# Task Description: Test query 3: Context System
# Task Type: analysis
# Max Tokens: 8000
# Focus Entities: None
# Package Size: 4,823 characters

================================================================================

# Intelligent Context Analysis

## Query Analysis
**User Query**: How does Aid<PERSON> gather context from the repository?

**Semantic Analysis**:
- **Intent**: workflow_analysis
- **Scope**: single_component
- **Confidence**: 0.68
- **Domain Concepts**: 1
  - Technical: repository (confidence: 0.90)

**Selection Method**: Intelligent Semantic Analysis (not keyword matching)
**Components Selected**: 6
**Selection Confidence**: 0.85

---

## Selected Components (Ranked by Semantic Relevance)

### 1. ⚙️ get_files_content
- **Type**: Function
- **File**: aider\coders\base_coder.py
- **Module**: base_coder
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 25.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['get_abs_fnames_content', 'is_image_file', 'get_rel_fname'] (total: 3)
- **Used By**: ['base_coder_old', 'base_coder'] (total: 2)
- **Side Effects**: ['network_io', 'modifies_state']

### 2. ⚙️ get_files_content
- **Type**: Function
- **File**: aider\coders\base_coder_old.py
- **Module**: base_coder_old
- **Line**: N/A
- **Cluster**: core
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 25.100
- **Semantic Rationale**: Selected from core cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['get_abs_fnames_content', 'is_image_file', 'get_rel_fname'] (total: 3)
- **Used By**: ['base_coder_old', 'base_coder'] (total: 2)
- **Side Effects**: ['network_io', 'modifies_state']

### 3. ⚙️ _discover_file_like_repomap
- **Type**: Function
- **File**: aider\context_request\context_request_handler.py
- **Module**: context_request_handler
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 17.000
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['strip', 'replace', 'split']... (total: 8)
- **Used By**: ['context_request_handler'] (total: 1)
- **Side Effects**: ['modifies_container', 'modifies_state']...

### 4. ⚙️ get_repo_map
- **Type**: Function
- **File**: aider\context_request\aider_integration_service.py
- **Module**: aider_integration_service
- **Line**: N/A
- **Cluster**: io
- **Criticality**: Medium
- **Change Risk**: Medium
- **Relevance Score**: 17.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['RepoMap'] (total: 1)
- **Used By**: ['base_coder_old', 'aider_integration_service', 'base_coder']... (total: 4)
- **Side Effects**: ['modifies_state', 'writes_log']

### 5. 🏛️ AiderContextRequestIntegration
- **Type**: Class
- **File**: aider\context_request\aider_context_request_integration.py
- **Module**: aider_context_request_integration
- **Line**: N/A
- **Cluster**: context
- **Criticality**: Low
- **Change Risk**: Low
- **Relevance Score**: 12.000
- **Semantic Rationale**: Selected from context cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Used By**: ['base_coder_old', 'base_coder'] (total: 2)
- **Side Effects**: ['none']

### 6. ⚙️ get_input
- **Type**: Function
- **File**: aider\io.py
- **Module**: io
- **Line**: N/A
- **Cluster**: io
- **Criticality**: High
- **Change Risk**: High
- **Relevance Score**: 9.000
- **Semantic Rationale**: Selected from io cluster. 🏗️ Architectural Context Selection

🎯 Strategy: Workflow Focused
📋 Rationale: Selected core business...
- **Calls**: ['rule', 'ring_bell', 'get_rel_fname']... (total: 20)
- **Used By**: ['base_coder_old', 'base_coder'] (total: 2)
- **Side Effects**: ['database_io', 'network_io']...

---

## Selection Methodology

This context was selected using **Intelligent Semantic Analysis** instead of simple keyword matching:

1. **Query Intent Classification**: Analyzed the semantic intent of your query
2. **Domain Concept Extraction**: Identified business and technical concepts
3. **Component Purpose Analysis**: Understood the role and purpose of each component
4. **Semantic Matching**: Matched query intent to component purposes and roles
5. **Architectural Awareness**: Considered component significance and relationships

This approach provides more relevant and architecturally coherent context than keyword-based selection.

⚠️ **Context Completeness Notice**: This context was intelligently selected based on semantic relevance. If you need additional context or different components, please refine your query or request specific components.

