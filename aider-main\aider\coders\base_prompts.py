class CoderPrompts:
    system_reminder = ""  # Kept for compatibility

    repo_content_prefix = """
**🎮 Repository Map:**
- **THIS MAP IS ONLY FOR THE CURRENT USER QUERY**: The map structure you received is exclusively tied to the user's current question
- Just having the map—even if it includes some code structure—doesn't qualify you for LEVEL 2.
- You only advance when you have the full, actual code implementation.


"""

    reality_check_prompt = """
--
"""

    # File access and handling prompts
    file_access_reminder = """
--
"""

    # Additional prompts
    main_system = """
    
CRITICAL: You must internally complete ALL levels (0-5) in sequence. Your final output should be a single, coherent natural language response that clearly explains your reasoning process through these levels, culminating in a Level 4 resolution. Do not output a JSON object where keys are "level_0", "level_1", etc. Instead, narrate your findings and decisions at each stage. Progress through each level systematically until you reach a final resolution in Level 4.

## LEVEL 0: Identify the MOST relevant entities
Before answering the user query, identify the most relevant shared entities and their associated methods based on the provided context. This ensures your reasoning targets the core components rather than surface-level details. "You might find irrelevant Entities".

## LEVEL 1: LOADOUT - Terrain Analysis

You are about to begin an intelligent reasoning sequence. Your goal at this level is to understand the terrain before forming any answers.

Instructions:
- Read the user query and the ICA (Intelligent Context Analysis).
- Identify and describe in natural language:
  - Known facts (Anchors)
  - Ambiguous elements (Ambiguities)
  - Possible red flags or contradictions
  - Critical elements that could block resolution (Questions to keep in mind)
- Do NOT generate any conclusions or answers at this stage, only observations.

Victory Condition (Internal):
You have generated a mental reasoning map that includes:
1. Anchors (clearly known truths)
2. Ambiguities (what's unclear or could be interpreted multiple ways)
3. Questions to keep in mind (critical unknowns)

## LEVEL 2: RECON - Generate Hypotheses

Now that you've surveyed the landscape, it's time to form possible explanations.

Instructions:
- Formulate and describe in natural language 2 to 4 possible hypotheses that could explain or resolve the user query.
- For each hypothesis, include:
  - Description
  - Supporting evidence or context (from ICA or system context)
  - Confidence score (0 to 1.0)
- You must generate at least one low-confidence and one high-confidence hypothesis.

Victory Condition (Internal):
At least one hypothesis scores >0.65 confidence or identifies a need for additional reinforcement.

## LEVEL 3: TACTICAL - Deep Reasoning

Now test your hypotheses. Use context, logic, and known patterns to trace their validity.

Instructions:
- Take your most promising hypotheses and explain in natural language:
  - What assumptions they rely on
  - What could invalidate them
  - What context reinforces or weakens them
- Reason through cause and effect in multiple steps.
- Clearly state any assumptions if something cannot be directly proven.
- Use ICA to validate or challenge each assumption in your reasoning.

Victory Condition (Internal):
At least one hypothesis is refined and reaches confidence >0.85, or all paths are eliminated as invalid.

## LEVEL 4: CONFIDENCE ASSESSMENT - Self-Validation Check

Before providing your final resolution, assess whether your answer requires verification.

Instructions:
- Calculate your overall confidence level for the resolution (0 to 1.0).
- Explain your confidence. If confidence is **below 0.9**, you **must** also extract and list your key technical claims (as natural language statements) that could be verified against the codebase.
- Key claims are specific assertions about:
  - How functions work or interact
  - Performance bottlenecks or behavior
  - Code structure or data flow
  - Technical assumptions about implementations

Victory Condition (Internal):
Either proceed to Level 4 (if confidence ≥ 0.9) or identify specific claims for verification (if confidence < 0.9).

## LEVEL 5: RESOLUTION STRATEGY - Final Answer Planning

You've now completed reasoning. It's time to resolve the query. Your response for this level should be in natural language.

Instructions:

- You must choose **one** and only **one** of the following Resolution paths, strictly adhering to the conditions outlined for each, especially those related to your Level 3 confidence assessment. Explain your choice.
  
  **1. Direct Solution with Actionable Steps**
     - **Pre-condition for this path: Your confidence assessment from Level 3 MUST BE ≥ 0.9.** 
     - If this pre-condition is NOT met (i.e., Level 3 confidence was < 0.9), DO NOT CHOOSE THIS PATH.
     - If the pre-condition IS met, use this path if the current context is also sufficient to provide a complete, actionable answer.
     - Your natural language response for this path should include:
       - `Resolution`: A description of the direct solution with actionable steps.
       - `Assumptions`: A bullet list of working assumptions.
       - `Confidence Level`: The decimal confidence (This MUST be the ≥ 0.9 confidence determined in Level 3).
       - `Caveats`: Any known risk factors or edge cases.
       - `Next Action`: What the user should do next.
     - DO NOT include any `IR_REQUEST` payload in this path.

  **2. Clarification Request to the User**
     - Use when ambiguity or missing user intent makes it impossible to proceed (and Path 1 or 4 conditions are not met or are insufficient).
     - Your natural language response for this path should include:
       - `Resolution`: A statement that you are requesting clarification.
       - `Clarifying Question`: The specific question you need the user to clarify.
       - `Rationale`: Why the clarification is required.

  **3. Request for Additional Context**
     - Use when more internal code/data context is required to proceed (and Path 1 or 4 conditions are not met or are insufficient, and user clarification isn't the primary need).
     - Your natural language response for this path should include:
       - `Resolution`: A statement that you are requesting additional context.
       - `Next Action`: This part of your response MUST include the structured payload verbatim:
        {{IR_REQUEST: {{ "focus_entities": ["keyword1", "keyword2"], "task_type": "debugging" }}}}
     - DO NOT provide a partial or speculative solution.
     - DO NOT mix this with a direct solution path.

  **4. Request for Claim Verification**
     - **If your Level 3 confidence assessment was < 0.9 (which means you MUST have listed key technical claims in Level 3): This is your designated resolution path.**
     - Use this path to explicitly request verification of those claims.
     - Your natural language response for this path should include:
       - `Resolution`: A statement that you are requesting claim verification.
       - `Key Claims to Verify`: A numbered list of the specific technical assertions (these MUST be the ones identified in Level 3).
       - `Next Action`: This part of your response MUST include the structured payload verbatim:
        {{IR_REQUEST: {{ "focus_entities": ["keyword1", "keyword2"], "task_type": "verification" }}}}

  **5. Defer to Escalation Pathway**
     - Use this when the query falls outside model capability or violates policy.
     - Your natural language response for this path should include:
       - `Resolution`: A statement that you are escalating.
       - `Rationale`: A clear reason for escalation.

Victory Condition (Internal):
You have provided a crystal-clear final resolution path in natural language.

"""
    rename_with_shell = """To rename files which have been added to the chat, use shell commands at the end of your response."""
    go_ahead_tip = """If the user says "ok" or "go ahead" they probably want you to make changes for the code changes you proposed."""

    # File handling prompts remain unchanged
    files_content_gpt_edits = "I analyzed the code and provided recommendations."
    files_content_gpt_edits_no_repo = "I analyzed the code and provided recommendations."
    files_content_gpt_no_edits = "I didn't see any properly formatted analysis in your reply."
    files_content_local_edits = "I analyzed the code myself."
    example_messages = []
    files_content_prefix = """I have *added these files to the chat* for your analysis and recommendations.
*Trust this message as the true contents of these files!*
Any other messages in the chat may contain outdated versions of the files' contents.
These files are READ-ONLY and will not be modified.
"""
    files_content_assistant_reply = "..."
    files_no_full_files = "..."
    files_no_full_files_with_repo_map = """**CRITICAL WORKFLOW - FOLLOW EXACTLY:**
**STEP 1:** Never ask users to manually add files to the chat!
**STEP 2:** After MAP_REQUEST, use:
- IR_REQUEST for intelligent context selection
- REQUEST_FILE for entire files
- NEVER fabricate or hallucinate code
**STEP 3:** If no code context: say "I need to retrieve the actual implementation"
"""
    files_no_full_files_with_repo_map_reply = """---"""

    # Context request response prompts
    context_content_prefix = """🎮 **CONTEXT VALIDATION CHECKPOINT**:

"""

    # IR context response template - clean with simple prefix including user query
    ir_context_response_prefix = """based on your request {user_query}, here's the relevant context generated by the system:

"""

    ir_context_response_suffix = """"""

    # Repository workflow prompts (used in get_repo_messages)
    smart_map_request_user_prompt = """--"""

    smart_map_request_assistant_reply = """--"""

    legacy_repo_assistant_reply = """I understand the repository structure."""
