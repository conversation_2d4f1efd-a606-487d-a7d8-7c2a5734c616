"""
Component Purpose Analyzer
Analyzes code components to understand their purpose, role, and architectural significance.
"""

import re
from typing import List, Dict, Optional, Set
from .intelligent_context_models import (
    ComponentPurpose, DataFlowRole, ArchitecturalPattern, ComponentAnalysis,
    COMPONENT_PURPOSE_PATTERNS
)


class ComponentPurposeAnalyzer:
    """Analyze code components to understand their purpose and role."""
    
    def __init__(self):
        self.purpose_patterns = COMPONENT_PURPOSE_PATTERNS
        
        # Architectural pattern indicators
        self.pattern_indicators = {
            ArchitecturalPattern.FACTORY: ['factory', 'create', 'build', 'make', 'new'],
            ArchitecturalPattern.STRATEGY: ['strategy', 'algorithm', 'method', 'approach'],
            ArchitecturalPattern.OBSERVER: ['observer', 'listener', 'notify', 'subscribe', 'event'],
            ArchitecturalPattern.FACADE: ['facade', 'interface', 'wrapper', 'proxy'],
            ArchitecturalPattern.ADAPTER: ['adapter', 'convert', 'translate', 'bridge'],
            ArchitecturalPattern.DECORATOR: ['decorator', 'enhance', 'extend', 'wrap'],
            ArchitecturalPattern.SINGLETON: ['singleton', 'instance', 'global'],
            ArchitecturalPattern.BUILDER: ['builder', 'construct', 'assemble'],
            ArchitecturalPattern.COMMAND: ['command', 'execute', 'invoke', 'action'],
            ArchitecturalPattern.TEMPLATE_METHOD: ['template', 'abstract', 'base', 'skeleton']
        }
        
        # Data flow role indicators
        self.data_flow_indicators = {
            DataFlowRole.DATA_SOURCE: ['load', 'fetch', 'get', 'read', 'retrieve', 'source', 'input'],
            DataFlowRole.DATA_TRANSFORMER: ['transform', 'convert', 'process', 'modify', 'change', 'map'],
            DataFlowRole.DATA_SINK: ['save', 'store', 'write', 'persist', 'output', 'sink'],
            DataFlowRole.DATA_VALIDATOR: ['validate', 'check', 'verify', 'ensure', 'assert'],
            DataFlowRole.DATA_ROUTER: ['route', 'dispatch', 'forward', 'redirect', 'channel'],
            DataFlowRole.DATA_AGGREGATOR: ['aggregate', 'collect', 'combine', 'merge', 'sum'],
            DataFlowRole.DATA_FILTER: ['filter', 'select', 'exclude', 'include', 'screen']
        }
        
        # Business domain indicators
        self.business_domains = {
            'authentication': ['auth', 'login', 'password', 'token', 'session', 'credential'],
            'user_management': ['user', 'account', 'profile', 'member', 'customer'],
            'payment': ['payment', 'billing', 'invoice', 'charge', 'transaction'],
            'content': ['content', 'article', 'post', 'document', 'media'],
            'communication': ['email', 'message', 'notification', 'alert', 'sms'],
            'analytics': ['analytics', 'metrics', 'stats', 'report', 'dashboard'],
            'search': ['search', 'query', 'index', 'find', 'lookup'],
            'workflow': ['workflow', 'process', 'step', 'stage', 'pipeline']
        }
        
        # Technical domain indicators
        self.technical_domains = {
            'database': ['db', 'database', 'sql', 'query', 'table', 'schema'],
            'caching': ['cache', 'redis', 'memcache', 'store', 'temporary'],
            'api': ['api', 'rest', 'endpoint', 'service', 'client'],
            'parsing': ['parse', 'parser', 'syntax', 'grammar', 'lexer'],
            'serialization': ['serialize', 'json', 'xml', 'yaml', 'marshal'],
            'networking': ['http', 'tcp', 'socket', 'connection', 'network'],
            'security': ['security', 'encrypt', 'decrypt', 'hash', 'crypto'],
            'logging': ['log', 'logger', 'debug', 'trace', 'monitor'],
            'configuration': ['config', 'setting', 'option', 'parameter', 'env'],
            'testing': ['test', 'mock', 'stub', 'assert', 'verify']
        }
    
    def analyze_component_purpose(self, entity_name: str, entity_type: str, 
                                 file_path: str, source_code: Optional[str] = None,
                                 calls: Optional[List[str]] = None,
                                 used_by: Optional[List[str]] = None) -> ComponentPurpose:
        """
        Determine what role this component plays in the system.
        
        Args:
            entity_name: Name of the entity
            entity_type: Type of entity (function, class, etc.)
            file_path: Path to the file containing the entity
            source_code: Optional source code of the entity
            calls: Optional list of functions this entity calls
            used_by: Optional list of functions that use this entity
            
        Returns:
            ComponentPurpose classification
        """
        entity_lower = entity_name.lower()
        file_lower = file_path.lower()
        
        purpose_scores = {}
        
        # Score based on name patterns
        for purpose, patterns in self.purpose_patterns.items():
            score = 0
            for pattern in patterns:
                if pattern in entity_lower or pattern in file_lower:
                    score += 1
            purpose_scores[purpose] = score
        
        # Additional heuristics based on context
        
        # Entry point detection
        if (entity_name == 'main' or
            'main' in file_lower or
            'cli' in file_lower or
            'app' in file_lower or
            entity_type == 'class' and any(word in entity_lower for word in ['app', 'application', 'server'])):
            purpose_scores[ComponentPurpose.ENTRY_POINT] = purpose_scores.get(ComponentPurpose.ENTRY_POINT, 0) + 3

        # Business logic detection
        if any(word in entity_lower for word in ['process', 'calculate', 'compute', 'analyze', 'business']):
            purpose_scores[ComponentPurpose.BUSINESS_LOGIC] = purpose_scores.get(ComponentPurpose.BUSINESS_LOGIC, 0) + 2

        # Data access detection
        if any(word in entity_lower for word in ['repository', 'dao', 'model', 'database', 'db']):
            purpose_scores[ComponentPurpose.DATA_ACCESS] = purpose_scores.get(ComponentPurpose.DATA_ACCESS, 0) + 3

        # Utility detection
        if (any(word in entity_lower for word in ['util', 'helper', 'tool', 'common']) or
            'utils' in file_lower or 'helpers' in file_lower):
            purpose_scores[ComponentPurpose.UTILITY] = purpose_scores.get(ComponentPurpose.UTILITY, 0) + 3

        # Coordination detection
        if (any(word in entity_lower for word in ['manager', 'coordinator', 'orchestrator', 'controller']) or
            entity_type == 'class' and any(word in entity_lower for word in ['service', 'handler'])):
            purpose_scores[ComponentPurpose.COORDINATION] = purpose_scores.get(ComponentPurpose.COORDINATION, 0) + 2

        # Error handling detection
        if any(word in entity_lower for word in ['error', 'exception', 'handle', 'catch']):
            purpose_scores[ComponentPurpose.ERROR_HANDLING] = purpose_scores.get(ComponentPurpose.ERROR_HANDLING, 0) + 3

        # Configuration detection
        if any(word in entity_lower for word in ['config', 'setting', 'option', 'parameter']):
            purpose_scores[ComponentPurpose.CONFIGURATION] = purpose_scores.get(ComponentPurpose.CONFIGURATION, 0) + 3
        
        # Authentication detection
        if any(word in entity_lower for word in ['auth', 'login', 'password', 'token', 'session']):
            purpose_scores[ComponentPurpose.AUTHENTICATION] = purpose_scores.get(ComponentPurpose.AUTHENTICATION, 0) + 3

        # Parsing detection
        if any(word in entity_lower for word in ['parse', 'parser', 'lexer', 'grammar']):
            purpose_scores[ComponentPurpose.PARSING] = purpose_scores.get(ComponentPurpose.PARSING, 0) + 3
        
        # Analysis based on usage patterns
        if calls and used_by:
            # High fan-out suggests utility or coordination
            if len(calls) > 5:
                purpose_scores[ComponentPurpose.COORDINATION] = purpose_scores.get(ComponentPurpose.COORDINATION, 0) + 1

            # High fan-in suggests utility or business logic
            if len(used_by) > 5:
                purpose_scores[ComponentPurpose.UTILITY] = purpose_scores.get(ComponentPurpose.UTILITY, 0) + 1
                purpose_scores[ComponentPurpose.BUSINESS_LOGIC] = purpose_scores.get(ComponentPurpose.BUSINESS_LOGIC, 0) + 1
        
        # Return the purpose with the highest score
        if not purpose_scores or max(purpose_scores.values()) == 0:
            return ComponentPurpose.UTILITY  # Default
            
        return max(purpose_scores, key=purpose_scores.get)
    
    def analyze_data_flow_role(self, entity_name: str, calls: Optional[List[str]] = None,
                              used_by: Optional[List[str]] = None,
                              source_code: Optional[str] = None) -> DataFlowRole:
        """
        Understand component's role in data flow.
        
        Args:
            entity_name: Name of the entity
            calls: Optional list of functions this entity calls
            used_by: Optional list of functions that use this entity
            source_code: Optional source code of the entity
            
        Returns:
            DataFlowRole classification
        """
        entity_lower = entity_name.lower()
        
        role_scores = {}
        
        # Score based on name patterns
        for role, patterns in self.data_flow_indicators.items():
            score = 0
            for pattern in patterns:
                if pattern in entity_lower:
                    score += 1
            role_scores[role] = score
        
        # Analysis based on usage patterns
        if calls and used_by:
            calls_count = len(calls)
            used_by_count = len(used_by)
            
            # Data source: high fan-out, low fan-in
            if calls_count > used_by_count and used_by_count <= 2:
                role_scores[DataFlowRole.DATA_SOURCE] += 2
            
            # Data sink: low fan-out, high fan-in
            elif used_by_count > calls_count and calls_count <= 2:
                role_scores[DataFlowRole.DATA_SINK] += 2
            
            # Data transformer: balanced or high both
            elif calls_count > 2 and used_by_count > 2:
                role_scores[DataFlowRole.DATA_TRANSFORMER] += 2
        
        # Source code analysis
        if source_code:
            code_lower = source_code.lower()
            
            # Look for I/O operations
            if any(word in code_lower for word in ['open(', 'read(', 'load(', 'fetch(']):
                role_scores[DataFlowRole.DATA_SOURCE] += 1
            
            if any(word in code_lower for word in ['write(', 'save(', 'store(', 'persist(']):
                role_scores[DataFlowRole.DATA_SINK] += 1
            
            # Look for transformation operations
            if any(word in code_lower for word in ['transform', 'convert', 'map(', 'filter(']):
                role_scores[DataFlowRole.DATA_TRANSFORMER] += 1
            
            # Look for validation operations
            if any(word in code_lower for word in ['validate', 'check', 'verify', 'assert']):
                role_scores[DataFlowRole.DATA_VALIDATOR] += 1
        
        # Return the role with the highest score
        if not role_scores or max(role_scores.values()) == 0:
            return DataFlowRole.DATA_TRANSFORMER  # Default
            
        return max(role_scores, key=role_scores.get)
    
    def identify_architectural_patterns(self, entity_name: str, entity_type: str,
                                      source_code: Optional[str] = None) -> List[ArchitecturalPattern]:
        """
        Identify architectural patterns in the component.
        
        Args:
            entity_name: Name of the entity
            entity_type: Type of entity (function, class, etc.)
            source_code: Optional source code of the entity
            
        Returns:
            List of identified architectural patterns
        """
        patterns = []
        entity_lower = entity_name.lower()
        
        # Pattern detection based on naming
        for pattern, indicators in self.pattern_indicators.items():
            if any(indicator in entity_lower for indicator in indicators):
                patterns.append(pattern)
        
        # Source code analysis for patterns
        if source_code:
            code_lower = source_code.lower()
            
            # Factory pattern detection
            if ('def create' in code_lower or 'def make' in code_lower or 
                'def build' in code_lower or 'def new' in code_lower):
                if ArchitecturalPattern.FACTORY not in patterns:
                    patterns.append(ArchitecturalPattern.FACTORY)
            
            # Observer pattern detection
            if ('subscribe' in code_lower or 'notify' in code_lower or 
                'listener' in code_lower or 'event' in code_lower):
                if ArchitecturalPattern.OBSERVER not in patterns:
                    patterns.append(ArchitecturalPattern.OBSERVER)
            
            # Decorator pattern detection
            if ('decorator' in code_lower or '@' in source_code):
                if ArchitecturalPattern.DECORATOR not in patterns:
                    patterns.append(ArchitecturalPattern.DECORATOR)
            
            # Strategy pattern detection
            if ('strategy' in code_lower or 'algorithm' in code_lower):
                if ArchitecturalPattern.STRATEGY not in patterns:
                    patterns.append(ArchitecturalPattern.STRATEGY)
        
        return patterns
    
    def identify_business_domain(self, entity_name: str, file_path: str,
                               source_code: Optional[str] = None) -> Optional[str]:
        """Identify the business domain of the component."""
        entity_lower = entity_name.lower()
        file_lower = file_path.lower()
        
        domain_scores = {}
        
        for domain, indicators in self.business_domains.items():
            score = 0
            for indicator in indicators:
                if indicator in entity_lower or indicator in file_lower:
                    score += 1
            if source_code and any(indicator in source_code.lower() for indicator in indicators):
                score += 1
            domain_scores[domain] = score
        
        if domain_scores and max(domain_scores.values()) > 0:
            return max(domain_scores, key=domain_scores.get)
        return None
    
    def identify_technical_domain(self, entity_name: str, file_path: str,
                                source_code: Optional[str] = None) -> Optional[str]:
        """Identify the technical domain of the component."""
        entity_lower = entity_name.lower()
        file_lower = file_path.lower()
        
        domain_scores = {}
        
        for domain, indicators in self.technical_domains.items():
            score = 0
            for indicator in indicators:
                if indicator in entity_lower or indicator in file_lower:
                    score += 1
            if source_code and any(indicator in source_code.lower() for indicator in indicators):
                score += 1
            domain_scores[domain] = score
        
        if domain_scores and max(domain_scores.values()) > 0:
            return max(domain_scores, key=domain_scores.get)
        return None
    
    def calculate_complexity_score(self, source_code: Optional[str] = None,
                                 calls: Optional[List[str]] = None) -> float:
        """Calculate a complexity score for the component."""
        if not source_code:
            return 0.5  # Default medium complexity
        
        # Simple complexity metrics
        lines = len(source_code.split('\n'))
        
        # Cyclomatic complexity indicators
        complexity_indicators = ['if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except:', 'with ']
        complexity_count = sum(source_code.lower().count(indicator) for indicator in complexity_indicators)
        
        # Function calls complexity
        call_complexity = len(calls) if calls else 0
        
        # Normalize to 0-1 scale
        line_score = min(lines / 100.0, 1.0)  # 100+ lines = max complexity
        complexity_score = min(complexity_count / 10.0, 1.0)  # 10+ branches = max complexity
        call_score = min(call_complexity / 20.0, 1.0)  # 20+ calls = max complexity
        
        return (line_score + complexity_score + call_score) / 3.0
    
    def calculate_architectural_significance(self, purpose: ComponentPurpose,
                                           data_flow_role: DataFlowRole,
                                           patterns: List[ArchitecturalPattern],
                                           used_by_count: int = 0) -> float:
        """Calculate architectural significance score."""
        significance = 0.0
        
        # Purpose-based significance
        purpose_weights = {
            ComponentPurpose.ENTRY_POINT: 1.0,
            ComponentPurpose.COORDINATION: 0.9,
            ComponentPurpose.BUSINESS_LOGIC: 0.8,
            ComponentPurpose.AUTHENTICATION: 0.8,
            ComponentPurpose.DATA_ACCESS: 0.7,
            ComponentPurpose.ERROR_HANDLING: 0.6,
            ComponentPurpose.VALIDATION: 0.5,
            ComponentPurpose.CONFIGURATION: 0.5,
            ComponentPurpose.UTILITY: 0.3,
        }
        significance += purpose_weights.get(purpose, 0.4)
        
        # Data flow role significance
        role_weights = {
            DataFlowRole.DATA_SOURCE: 0.7,
            DataFlowRole.DATA_SINK: 0.7,
            DataFlowRole.DATA_ROUTER: 0.8,
            DataFlowRole.DATA_TRANSFORMER: 0.6,
            DataFlowRole.DATA_VALIDATOR: 0.5,
            DataFlowRole.DATA_AGGREGATOR: 0.6,
            DataFlowRole.DATA_FILTER: 0.4,
        }
        significance += role_weights.get(data_flow_role, 0.4) * 0.5
        
        # Pattern-based significance
        if patterns:
            pattern_bonus = min(len(patterns) * 0.1, 0.3)
            significance += pattern_bonus
        
        # Usage-based significance
        if used_by_count > 0:
            usage_bonus = min(used_by_count / 10.0, 0.3)
            significance += usage_bonus
        
        return min(significance, 1.0)
    
    def analyze_complete_component(self, entity_name: str, entity_type: str,
                                 file_path: str, source_code: Optional[str] = None,
                                 calls: Optional[List[str]] = None,
                                 used_by: Optional[List[str]] = None) -> ComponentAnalysis:
        """
        Perform complete component analysis.
        
        Returns:
            ComponentAnalysis with all analysis results
        """
        purpose = self.analyze_component_purpose(entity_name, entity_type, file_path, source_code, calls, used_by)
        data_flow_role = self.analyze_data_flow_role(entity_name, calls, used_by, source_code)
        patterns = self.identify_architectural_patterns(entity_name, entity_type, source_code)
        business_domain = self.identify_business_domain(entity_name, file_path, source_code)
        technical_domain = self.identify_technical_domain(entity_name, file_path, source_code)
        complexity_score = self.calculate_complexity_score(source_code, calls)
        architectural_significance = self.calculate_architectural_significance(
            purpose, data_flow_role, patterns, len(used_by) if used_by else 0
        )
        
        return ComponentAnalysis(
            entity_name=entity_name,
            purpose=purpose,
            data_flow_role=data_flow_role,
            architectural_patterns=patterns,
            business_domain=business_domain,
            technical_domain=technical_domain,
            complexity_score=complexity_score,
            architectural_significance=architectural_significance
        )
