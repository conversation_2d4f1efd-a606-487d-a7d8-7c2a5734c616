"""
Intelligent Context Analysis Models
Data models for semantic query analysis and component understanding.
"""

from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Optional, Set
import re


class QueryIntent(Enum):
    """Classification of user query intents."""
    WORKFLOW_ANALYSIS = "workflow_analysis"
    COMPONENT_DISCOVERY = "component_discovery" 
    DEBUGGING_ASSISTANCE = "debugging_assistance"
    FEATURE_IMPLEMENTATION = "feature_implementation"
    ARCHITECTURE_UNDERSTANDING = "architecture_understanding"
    PERFORMANCE_ANALYSIS = "performance_analysis"
    SECURITY_ANALYSIS = "security_analysis"
    CODE_REVIEW = "code_review"
    REFACTORING = "refactoring"


class ComponentPurpose(Enum):
    """Classification of component purposes in the system."""
    ENTRY_POINT = "entry_point"
    BUSINESS_LOGIC = "business_logic"
    DATA_ACCESS = "data_access"
    UTILITY = "utility"
    COORDINATION = "coordination"
    VALIDATION = "validation"
    ERROR_HANDLING = "error_handling"
    CONFIGURATION = "configuration"
    AUTHENTICATION = "authentication"
    CACHING = "caching"
    PARSING = "parsing"
    TRANSFORMATION = "transformation"
    COMMUNICATION = "communication"
    MONITORING = "monitoring"


class DataFlowRole(Enum):
    """Role of component in data flow."""
    DATA_SOURCE = "data_source"
    DATA_TRANSFORMER = "data_transformer"
    DATA_SINK = "data_sink"
    DATA_VALIDATOR = "data_validator"
    DATA_ROUTER = "data_router"
    DATA_AGGREGATOR = "data_aggregator"
    DATA_FILTER = "data_filter"


class ArchitecturalPattern(Enum):
    """Architectural patterns identified in components."""
    FACTORY = "factory"
    STRATEGY = "strategy"
    OBSERVER = "observer"
    FACADE = "facade"
    ADAPTER = "adapter"
    DECORATOR = "decorator"
    SINGLETON = "singleton"
    BUILDER = "builder"
    COMMAND = "command"
    TEMPLATE_METHOD = "template_method"


class QueryScope(Enum):
    """Scope of analysis needed for the query."""
    SINGLE_COMPONENT = "single_component"
    WORKFLOW_CHAIN = "workflow_chain"
    SYSTEM_OVERVIEW = "system_overview"
    CROSS_CUTTING = "cross_cutting"
    MODULE_LEVEL = "module_level"


@dataclass
class DomainConcept:
    """A domain concept extracted from user query."""
    concept: str
    category: str  # business, technical, data
    confidence: float
    related_terms: List[str]
    context_clues: List[str]


@dataclass
class ComponentAnalysis:
    """Analysis results for a component."""
    entity_name: str
    purpose: ComponentPurpose
    data_flow_role: DataFlowRole
    architectural_patterns: List[ArchitecturalPattern]
    business_domain: Optional[str]
    technical_domain: Optional[str]
    complexity_score: float
    architectural_significance: float


@dataclass
class QueryContext:
    """Complete context analysis for a user query."""
    intent: QueryIntent
    domain_concepts: List[DomainConcept]
    scope: QueryScope
    focus_entities: List[str]
    original_query: str
    confidence: float
    query_patterns: List[str]


@dataclass
class ComponentRelevanceScore:
    """Relevance score for a component given a query context."""
    component_name: str
    total_score: float
    semantic_alignment: float
    workflow_relevance: float
    architectural_significance: float
    data_flow_relevance: float
    error_handling_relevance: float
    explanation: str


@dataclass
class SystemWorkflow:
    """A discovered workflow in the system."""
    name: str
    description: str
    entry_points: List[str]
    primary_components: List[str]
    supporting_components: List[str]
    data_flow: List[str]
    error_handling_components: List[str]
    workflow_type: str  # request_processing, data_pipeline, error_handling, etc.


@dataclass
class ComponentCollaborationMap:
    """Map of how components collaborate in workflows."""
    workflow_name: str
    primary_actors: List[str]
    supporting_actors: List[str]
    data_flow_edges: List[tuple]  # (from_component, to_component, data_type)
    control_flow_edges: List[tuple]  # (from_component, to_component, condition)
    collaboration_patterns: List[str]


@dataclass
class FunctionalRelationship:
    """Functional relationship between components."""
    source_component: str
    target_component: str
    relationship_type: str  # orchestrates, delegates_to, validates, transforms_for, monitors
    strength: float
    context: str


@dataclass
class ComponentCluster:
    """A cluster of functionally related components."""
    name: str
    description: str
    components: List[str]
    cluster_type: str  # authentication, data_processing, error_handling, etc.
    cohesion_score: float
    external_dependencies: List[str]


@dataclass
class CrossCuttingConcern:
    """A concern that spans multiple components."""
    name: str
    description: str
    affected_components: List[str]
    concern_type: str  # logging, security, caching, error_handling
    implementation_pattern: str


@dataclass
class ComponentRelevanceMap:
    """Map of component relevance for a query."""
    query_context: QueryContext
    primary_components: List[ComponentRelevanceScore]
    supporting_components: List[ComponentRelevanceScore]
    workflow_coverage: Dict[str, float]
    architectural_coverage: Dict[str, float]


@dataclass
class OptimizedContextSelection:
    """Optimized context selection result."""
    selected_components: List[str]
    workflow_completeness: float
    architectural_coherence: float
    token_efficiency: float
    explanation: str
    missing_context_warnings: List[str]


@dataclass
class ContextCompletenessReport:
    """Report on context completeness for answering a query."""
    can_answer_query: bool
    workflow_coverage: float
    dependency_coverage: float
    error_handling_coverage: float
    architectural_coverage: float
    missing_components: List[str]
    recommendations: List[str]


@dataclass
class ContextExplanation:
    """Explanation of why components were selected."""
    primary_components_rationale: Dict[str, str]
    supporting_components_rationale: Dict[str, str]
    workflow_coverage_explanation: str
    architectural_context_explanation: str
    potential_limitations: List[str]


# Pattern definitions for query analysis
QUERY_INTENT_PATTERNS = {
    QueryIntent.WORKFLOW_ANALYSIS: [
        r"how does .* work",
        r"what happens when",
        r"walk me through",
        r"explain the process",
        r"flow of",
        r"sequence of"
    ],
    QueryIntent.COMPONENT_DISCOVERY: [
        r"what handles",
        r"which component",
        r"what is responsible for",
        r"where is .* implemented",
        r"find the .* function",
        r"locate the"
    ],
    QueryIntent.DEBUGGING_ASSISTANCE: [
        r"why is .* failing",
        r"error in",
        r"bug in",
        r"not working",
        r"issue with",
        r"problem with"
    ],
    QueryIntent.FEATURE_IMPLEMENTATION: [
        r"how to add",
        r"implement",
        r"create a new",
        r"extend",
        r"modify to",
        r"add support for"
    ],
    QueryIntent.ARCHITECTURE_UNDERSTANDING: [
        r"structure of",
        r"architecture",
        r"design of",
        r"organization of",
        r"overview of",
        r"high level"
    ]
}

COMPONENT_PURPOSE_PATTERNS = {
    ComponentPurpose.ENTRY_POINT: [
        r"main", r"cli", r"api", r"endpoint", r"handler", r"controller",
        r"interface", r"facade", r"gateway"
    ],
    ComponentPurpose.BUSINESS_LOGIC: [
        r"process", r"calculate", r"compute", r"analyze", r"transform",
        r"business", r"logic", r"algorithm", r"strategy"
    ],
    ComponentPurpose.DATA_ACCESS: [
        r"repository", r"dao", r"database", r"storage", r"persist",
        r"load", r"save", r"fetch", r"retrieve"
    ],
    ComponentPurpose.UTILITY: [
        r"util", r"helper", r"tool", r"common", r"shared",
        r"format", r"convert", r"parse"
    ],
    ComponentPurpose.VALIDATION: [
        r"validate", r"check", r"verify", r"ensure", r"assert",
        r"sanitize", r"clean"
    ],
    ComponentPurpose.ERROR_HANDLING: [
        r"error", r"exception", r"handle", r"catch", r"recover",
        r"fallback", r"retry"
    ],
    ComponentPurpose.CONFIGURATION: [
        r"config", r"setting", r"option", r"parameter", r"preference",
        r"setup", r"initialize"
    ]
}

DOMAIN_CONCEPT_PATTERNS = {
    "business": [
        r"user", r"customer", r"account", r"order", r"payment",
        r"product", r"service", r"transaction", r"workflow"
    ],
    "technical": [
        r"database", r"cache", r"api", r"service", r"component",
        r"module", r"function", r"class", r"method", r"algorithm"
    ],
    "data": [
        r"file", r"document", r"record", r"entity", r"model",
        r"schema", r"format", r"structure", r"field", r"attribute"
    ]
}
