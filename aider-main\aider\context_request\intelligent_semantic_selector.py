"""
Intelligent Semantic Context Selector
Replaces keyword matching with semantic understanding of system components.
"""

from typing import List, Dict, Optional, Tuple
from .intelligent_context_models import (
    QueryContext, ComponentAnalysis, ComponentRelevanceScore, 
    QueryIntent, ComponentPurpose, DataFlowRole
)
from .query_intent_analyzer import QueryIntent<PERSON><PERSON>yzer
from .component_purpose_analyzer import ComponentPurposeAnalyzer


class IntelligentSemanticSelector:
    """
    Intelligent context selector that uses semantic understanding instead of keyword matching.
    """
    
    def __init__(self):
        self.query_analyzer = QueryIntentAnalyzer()
        self.component_analyzer = ComponentPurposeAnalyzer()
        
        # Intent-to-purpose mapping weights
        self.intent_purpose_weights = {
            QueryIntent.WORKFLOW_ANALYSIS: {
                ComponentPurpose.COORDINATION: 1.0,
                ComponentPurpose.BUSINESS_LOGIC: 0.9,
                ComponentPurpose.ENTRY_POINT: 0.8,
                ComponentPurpose.DATA_ACCESS: 0.7,
                ComponentPurpose.VALIDATION: 0.6,
                ComponentPurpose.ERROR_HANDLING: 0.5,
            },
            QueryIntent.COMPONENT_DISCOVERY: {
                ComponentPurpose.BUSINESS_LOGIC: 1.0,
                ComponentPurpose.COORDINATION: 0.9,
                ComponentPurpose.DATA_ACCESS: 0.8,
                ComponentPurpose.UTILITY: 0.7,
                ComponentPurpose.VALIDATION: 0.6,
            },
            QueryIntent.DEBUGGING_ASSISTANCE: {
                ComponentPurpose.ERROR_HANDLING: 1.0,
                ComponentPurpose.VALIDATION: 0.9,
                ComponentPurpose.BUSINESS_LOGIC: 0.8,
                ComponentPurpose.COORDINATION: 0.7,
                ComponentPurpose.DATA_ACCESS: 0.6,
            },
            QueryIntent.FEATURE_IMPLEMENTATION: {
                ComponentPurpose.BUSINESS_LOGIC: 1.0,
                ComponentPurpose.COORDINATION: 0.9,
                ComponentPurpose.ENTRY_POINT: 0.8,
                ComponentPurpose.DATA_ACCESS: 0.7,
                ComponentPurpose.VALIDATION: 0.6,
                ComponentPurpose.UTILITY: 0.5,
            },
            QueryIntent.ARCHITECTURE_UNDERSTANDING: {
                ComponentPurpose.ENTRY_POINT: 1.0,
                ComponentPurpose.COORDINATION: 0.9,
                ComponentPurpose.BUSINESS_LOGIC: 0.8,
                ComponentPurpose.DATA_ACCESS: 0.7,
                ComponentPurpose.CONFIGURATION: 0.6,
            },
            QueryIntent.PERFORMANCE_ANALYSIS: {
                ComponentPurpose.BUSINESS_LOGIC: 1.0,
                ComponentPurpose.DATA_ACCESS: 0.9,
                ComponentPurpose.CACHING: 0.8,
                ComponentPurpose.COORDINATION: 0.7,
                ComponentPurpose.UTILITY: 0.6,
            },
            QueryIntent.SECURITY_ANALYSIS: {
                ComponentPurpose.AUTHENTICATION: 1.0,
                ComponentPurpose.VALIDATION: 0.9,
                ComponentPurpose.ENTRY_POINT: 0.8,
                ComponentPurpose.BUSINESS_LOGIC: 0.7,
                ComponentPurpose.DATA_ACCESS: 0.6,
            }
        }
        
        # Data flow role weights for different intents
        self.intent_dataflow_weights = {
            QueryIntent.WORKFLOW_ANALYSIS: {
                DataFlowRole.DATA_ROUTER: 1.0,
                DataFlowRole.DATA_TRANSFORMER: 0.9,
                DataFlowRole.DATA_SOURCE: 0.8,
                DataFlowRole.DATA_SINK: 0.7,
            },
            QueryIntent.DEBUGGING_ASSISTANCE: {
                DataFlowRole.DATA_VALIDATOR: 1.0,
                DataFlowRole.DATA_TRANSFORMER: 0.8,
                DataFlowRole.DATA_SOURCE: 0.7,
                DataFlowRole.DATA_SINK: 0.6,
            }
        }
    
    def analyze_ir_entities(self, ir_data: Dict) -> Dict[str, ComponentAnalysis]:
        """
        Analyze all entities in IR data to understand their purposes and roles.
        
        Args:
            ir_data: Mid-level IR data
            
        Returns:
            Dictionary mapping entity names to ComponentAnalysis
        """
        component_analyses = {}
        
        print("🔍 Analyzing component purposes and roles...")

        # Debug: Check IR data structure
        print(f"   IR data keys: {list(ir_data.keys())}")
        modules = ir_data.get('modules', [])
        print(f"   Found {len(modules)} modules in IR data")

        if modules and len(modules) > 0:
            sample_module = modules[0]
            print(f"   Sample module keys: {list(sample_module.keys())}")
            if 'functions' in sample_module:
                print(f"   Sample module has {len(sample_module['functions'])} functions")
            if 'classes' in sample_module:
                print(f"   Sample module has {len(sample_module['classes'])} classes")

        # Process each module
        for module in modules:
            module_name = module.get('name', '')  # Changed from 'module_name' to 'name'
            file_path = module.get('file', '')    # Changed from 'file_path' to 'file'

            # Process entities (which contains both functions and classes)
            entities = module.get('entities', [])
            print(f"   Processing module {module_name} with {len(entities)} entities")

            for entity in entities:
                entity_name = entity.get('name', '')
                entity_type = entity.get('type', 'function')
                full_name = f"{module_name}.{entity_name}"

                # Get additional context
                calls = entity.get('calls', [])
                used_by = entity.get('used_by', [])
                source_code = entity.get('source_code', '')
                entity_file_path = entity.get('file_path', file_path)

                # Analyze the component
                analysis = self.component_analyzer.analyze_complete_component(
                    entity_name=entity_name,
                    entity_type=entity_type,
                    file_path=entity_file_path,
                    source_code=source_code,
                    calls=calls,
                    used_by=used_by
                )

                component_analyses[full_name] = analysis
        
        print(f"✅ Analyzed {len(component_analyses)} components")
        return component_analyses
    
    def score_component_semantic_relevance(self, component_analysis: ComponentAnalysis,
                                         query_context: QueryContext) -> ComponentRelevanceScore:
        """
        Score component relevance based on semantic understanding.
        
        Args:
            component_analysis: Analysis of the component
            query_context: Context of the user query
            
        Returns:
            ComponentRelevanceScore with detailed scoring
        """
        total_score = 0.0
        explanations = []
        
        # 1. Semantic alignment with query intent (40% weight)
        intent_weights = self.intent_purpose_weights.get(query_context.intent, {})
        purpose_score = intent_weights.get(component_analysis.purpose, 0.0)
        semantic_alignment = purpose_score * 0.4
        total_score += semantic_alignment
        
        if purpose_score > 0.5:
            explanations.append(f"Purpose {component_analysis.purpose.value} aligns with {query_context.intent.value}")
        
        # 2. Data flow relevance (20% weight)
        dataflow_weights = self.intent_dataflow_weights.get(query_context.intent, {})
        dataflow_score = dataflow_weights.get(component_analysis.data_flow_role, 0.0)
        data_flow_relevance = dataflow_score * 0.2
        total_score += data_flow_relevance
        
        if dataflow_score > 0.5:
            explanations.append(f"Data flow role {component_analysis.data_flow_role.value} is relevant")
        
        # 3. Domain concept matching (25% weight)
        domain_relevance = self._calculate_domain_relevance(component_analysis, query_context)
        total_score += domain_relevance * 0.25
        
        if domain_relevance > 0.5:
            explanations.append(f"Domain concepts match query focus")
        
        # 4. Architectural significance (10% weight)
        arch_relevance = component_analysis.architectural_significance * 0.1
        total_score += arch_relevance
        
        if component_analysis.architectural_significance > 0.7:
            explanations.append(f"High architectural significance ({component_analysis.architectural_significance:.2f})")
        
        # 5. Focus entity matching (5% weight)
        focus_relevance = self._calculate_focus_entity_relevance(component_analysis, query_context)
        total_score += focus_relevance * 0.05
        
        if focus_relevance > 0.5:
            explanations.append(f"Matches focus entities")
        
        explanation = "; ".join(explanations) if explanations else "Low relevance to query"
        
        return ComponentRelevanceScore(
            component_name=component_analysis.entity_name,
            total_score=total_score,
            semantic_alignment=semantic_alignment,
            workflow_relevance=0.0,  # Will be calculated in workflow analysis
            architectural_significance=arch_relevance,
            data_flow_relevance=data_flow_relevance,
            error_handling_relevance=0.0,  # Will be calculated based on intent
            explanation=explanation
        )
    
    def _calculate_domain_relevance(self, component_analysis: ComponentAnalysis,
                                  query_context: QueryContext) -> float:
        """Calculate relevance based on domain concept matching."""
        relevance = 0.0
        
        # Check business domain alignment
        if component_analysis.business_domain:
            for concept in query_context.domain_concepts:
                if concept.category == 'business':
                    concept_terms = concept.related_terms
                    if any(term in component_analysis.business_domain for term in concept_terms):
                        relevance += concept.confidence * 0.5
        
        # Check technical domain alignment
        if component_analysis.technical_domain:
            for concept in query_context.domain_concepts:
                if concept.category == 'technical':
                    concept_terms = concept.related_terms
                    if any(term in component_analysis.technical_domain for term in concept_terms):
                        relevance += concept.confidence * 0.5
        
        return min(relevance, 1.0)
    
    def _calculate_focus_entity_relevance(self, component_analysis: ComponentAnalysis,
                                        query_context: QueryContext) -> float:
        """Calculate relevance based on focus entity matching."""
        if not query_context.focus_entities:
            return 0.0
        
        entity_name_lower = component_analysis.entity_name.lower()
        
        for focus_entity in query_context.focus_entities:
            focus_lower = focus_entity.lower()
            
            # Exact match
            if focus_lower == entity_name_lower:
                return 1.0
            
            # Partial match
            if focus_lower in entity_name_lower or entity_name_lower in focus_lower:
                return 0.7
        
        return 0.0
    
    def select_intelligent_context(self, ir_data: Dict, query: str,
                                 focus_entities: Optional[List[str]] = None,
                                 max_components: int = 8) -> List[ComponentRelevanceScore]:
        """
        Select context using intelligent semantic analysis.
        
        Args:
            ir_data: Mid-level IR data
            query: User query string
            focus_entities: Optional focus entities
            max_components: Maximum number of components to select
            
        Returns:
            List of selected components with relevance scores
        """
        print(f"🧠 Performing intelligent semantic context selection for: {query}")
        
        # 1. Analyze query intent and extract concepts
        query_context = self.query_analyzer.analyze_complete_query(query, focus_entities)
        print(f"   Intent: {query_context.intent.value}")
        print(f"   Scope: {query_context.scope.value}")
        print(f"   Confidence: {query_context.confidence:.2f}")
        print(f"   Domain concepts: {len(query_context.domain_concepts)}")
        
        # 2. Analyze all components in the IR
        component_analyses = self.analyze_ir_entities(ir_data)
        
        # 3. Score each component for relevance
        print(f"🎯 Scoring {len(component_analyses)} components for relevance...")
        relevance_scores = []
        
        for entity_name, analysis in component_analyses.items():
            score = self.score_component_semantic_relevance(analysis, query_context)
            relevance_scores.append(score)
        
        # 4. Sort by relevance and select top components
        relevance_scores.sort(key=lambda x: x.total_score, reverse=True)
        selected_components = relevance_scores[:max_components]
        
        print(f"✅ Selected {len(selected_components)} most relevant components:")
        for i, component in enumerate(selected_components, 1):
            print(f"   {i}. {component.component_name} (score: {component.total_score:.3f}) - {component.explanation}")
        
        return selected_components

    def analyze_query(self, query: str, focus_entities: Optional[List[str]] = None) -> QueryContext:
        """
        Analyze a query to understand its intent and context.

        Args:
            query: User query string
            focus_entities: Optional focus entities

        Returns:
            QueryContext with analysis results
        """
        return self.query_analyzer.analyze_complete_query(query, focus_entities)
