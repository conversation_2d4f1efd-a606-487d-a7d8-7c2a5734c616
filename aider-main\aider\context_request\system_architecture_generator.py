"""
System Architecture Generator
Generate top-level system architecture from IR data to guide context selection.
"""

from typing import Dict, List, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json


class ModuleType(Enum):
    """Types of modules in the system architecture."""
    CORE_BUSINESS = "core_business"
    INFRASTRUCTURE = "infrastructure"
    INTERFACE = "interface"
    UTILITY = "utility"
    CONFIGURATION = "configuration"
    TESTING = "testing"
    EXTERNAL_INTEGRATION = "external_integration"


class ArchitecturalLayer(Enum):
    """Architectural layers in the system."""
    PRESENTATION = "presentation"
    APPLICATION = "application"
    DOMAIN = "domain"
    INFRASTRUCTURE = "infrastructure"
    EXTERNAL = "external"


@dataclass
class ModuleCluster:
    """A cluster of related modules."""
    name: str
    modules: List[str]
    purpose: str
    module_type: ModuleType
    layer: ArchitecturalLayer
    dependencies: List[str]  # Other clusters this depends on
    dependents: List[str]   # Other clusters that depend on this
    criticality: float
    key_entities: List[str]


@dataclass
class SystemArchitecture:
    """Top-level system architecture."""
    clusters: List[ModuleCluster]
    dependency_graph: Dict[str, List[str]]
    layer_hierarchy: Dict[ArchitecturalLayer, List[str]]
    critical_paths: List[List[str]]
    entry_points: List[str]
    core_workflows: List[Dict[str, Any]]


class SystemArchitectureGenerator:
    """Generate top-level system architecture from IR data."""
    
    def __init__(self):
        self.ir_data = None
        self.module_dependencies = {}
        self.module_entities = {}
        self.module_criticality = {}
    
    def generate_architecture(self, ir_data: Dict[str, Any]) -> SystemArchitecture:
        """
        Generate top-level system architecture from IR data.
        
        Args:
            ir_data: IR data containing modules, dependencies, and entities
            
        Returns:
            SystemArchitecture with clusters, layers, and workflows
        """
        self.ir_data = ir_data
        
        print("🏗️ Generating System Architecture from IR data...")
        
        # Step 1: Analyze modules and their characteristics
        self._analyze_modules()
        
        # Step 2: Identify module clusters based on functionality
        clusters = self._identify_module_clusters()
        
        # Step 3: Determine architectural layers
        self._assign_architectural_layers(clusters)
        
        # Step 4: Build dependency graph between clusters
        dependency_graph = self._build_cluster_dependency_graph(clusters)
        
        # Step 5: Identify critical paths and workflows
        critical_paths = self._identify_critical_paths(clusters, dependency_graph)
        
        # Step 6: Find system entry points
        entry_points = self._identify_entry_points(clusters)
        
        # Step 7: Discover core workflows
        core_workflows = self._discover_core_workflows(clusters, dependency_graph)
        
        # Step 8: Build layer hierarchy
        layer_hierarchy = self._build_layer_hierarchy(clusters)
        
        architecture = SystemArchitecture(
            clusters=clusters,
            dependency_graph=dependency_graph,
            layer_hierarchy=layer_hierarchy,
            critical_paths=critical_paths,
            entry_points=entry_points,
            core_workflows=core_workflows
        )
        
        print(f"✅ Generated architecture with {len(clusters)} clusters across {len(layer_hierarchy)} layers")
        
        return architecture
    
    def _analyze_modules(self):
        """Analyze modules to understand their characteristics."""
        modules = self.ir_data.get('modules', [])
        
        for module in modules:
            module_name = module.get('name', '')
            
            # Extract dependencies
            deps = [dep.get('module', '') for dep in module.get('dependencies', [])]
            self.module_dependencies[module_name] = deps
            
            # Extract entities
            entities = module.get('entities', [])
            self.module_entities[module_name] = entities
            
            # Calculate module criticality
            criticality = self._calculate_module_criticality(module)
            self.module_criticality[module_name] = criticality
    
    def _calculate_module_criticality(self, module: Dict[str, Any]) -> float:
        """Calculate criticality score for a module."""
        entities = module.get('entities', [])
        
        # Factors that increase criticality
        high_criticality_entities = sum(1 for e in entities if e.get('criticality') == 'high')
        total_entities = len(entities)
        dependencies = len(module.get('dependencies', []))
        loc = module.get('loc', 0)
        
        # Calculate base score
        if total_entities == 0:
            return 0.1
        
        criticality_ratio = high_criticality_entities / total_entities
        dependency_factor = min(dependencies / 10.0, 1.0)  # Normalize to 0-1
        size_factor = min(loc / 1000.0, 1.0)  # Normalize to 0-1
        
        # Weighted combination
        score = (criticality_ratio * 0.5) + (dependency_factor * 0.3) + (size_factor * 0.2)
        
        return min(score, 1.0)
    
    def _identify_module_clusters(self) -> List[ModuleCluster]:
        """Identify clusters of related modules."""
        clusters = []
        processed_modules = set()
        
        # Predefined cluster patterns based on common architectural patterns
        cluster_patterns = {
            'core': {
                'keywords': ['main', 'core', 'base', 'coder', 'engine'],
                'type': ModuleType.CORE_BUSINESS,
                'layer': ArchitecturalLayer.APPLICATION
            },
            'context': {
                'keywords': ['context', 'request', 'handler', 'selector'],
                'type': ModuleType.CORE_BUSINESS,
                'layer': ArchitecturalLayer.APPLICATION
            },
            'io': {
                'keywords': ['io', 'input', 'output', 'file', 'stream'],
                'type': ModuleType.INFRASTRUCTURE,
                'layer': ArchitecturalLayer.INFRASTRUCTURE
            },
            'models': {
                'keywords': ['model', 'llm', 'ai', 'chat'],
                'type': ModuleType.EXTERNAL_INTEGRATION,
                'layer': ArchitecturalLayer.EXTERNAL
            },
            'ui': {
                'keywords': ['gui', 'interface', 'prompt', 'display'],
                'type': ModuleType.INTERFACE,
                'layer': ArchitecturalLayer.PRESENTATION
            },
            'utils': {
                'keywords': ['util', 'helper', 'tool', 'format'],
                'type': ModuleType.UTILITY,
                'layer': ArchitecturalLayer.INFRASTRUCTURE
            },
            'config': {
                'keywords': ['config', 'setting', 'arg', 'option'],
                'type': ModuleType.CONFIGURATION,
                'layer': ArchitecturalLayer.INFRASTRUCTURE
            },
            'test': {
                'keywords': ['test', 'spec', 'mock', 'fixture'],
                'type': ModuleType.TESTING,
                'layer': ArchitecturalLayer.EXTERNAL
            }
        }
        
        # Group modules into clusters
        for cluster_name, pattern in cluster_patterns.items():
            cluster_modules = []
            
            for module_name in self.module_entities.keys():
                if module_name in processed_modules:
                    continue
                
                # Check if module matches pattern
                if any(keyword in module_name.lower() for keyword in pattern['keywords']):
                    cluster_modules.append(module_name)
                    processed_modules.add(module_name)
            
            if cluster_modules:
                cluster = self._create_cluster(
                    cluster_name, 
                    cluster_modules, 
                    pattern['type'], 
                    pattern['layer']
                )
                clusters.append(cluster)
        
        # Handle remaining modules
        remaining_modules = [m for m in self.module_entities.keys() if m not in processed_modules]
        if remaining_modules:
            misc_cluster = self._create_cluster(
                'miscellaneous',
                remaining_modules,
                ModuleType.UTILITY,
                ArchitecturalLayer.INFRASTRUCTURE
            )
            clusters.append(misc_cluster)
        
        return clusters
    
    def _create_cluster(self, name: str, modules: List[str], 
                       module_type: ModuleType, layer: ArchitecturalLayer) -> ModuleCluster:
        """Create a module cluster."""
        # Calculate cluster dependencies
        cluster_deps = set()
        cluster_dependents = set()
        
        for module in modules:
            deps = self.module_dependencies.get(module, [])
            cluster_deps.update(deps)
        
        # Remove internal dependencies
        cluster_deps = [dep for dep in cluster_deps if dep not in modules]
        
        # Calculate average criticality
        criticalities = [self.module_criticality.get(m, 0.0) for m in modules]
        avg_criticality = sum(criticalities) / len(criticalities) if criticalities else 0.0
        
        # Extract key entities
        key_entities = []
        for module in modules:
            entities = self.module_entities.get(module, [])
            # Get high-criticality entities
            high_crit_entities = [e.get('name', '') for e in entities 
                                if e.get('criticality') == 'high']
            key_entities.extend(high_crit_entities[:3])  # Top 3 per module
        
        # Generate purpose description
        purpose = self._generate_cluster_purpose(name, modules, module_type)
        
        return ModuleCluster(
            name=name,
            modules=modules,
            purpose=purpose,
            module_type=module_type,
            layer=layer,
            dependencies=cluster_deps,
            dependents=[],  # Will be filled later
            criticality=avg_criticality,
            key_entities=key_entities[:10]  # Top 10 overall
        )
    
    def _generate_cluster_purpose(self, name: str, modules: List[str], 
                                 module_type: ModuleType) -> str:
        """Generate a purpose description for the cluster."""
        purposes = {
            ModuleType.CORE_BUSINESS: f"Core business logic and primary functionality",
            ModuleType.INFRASTRUCTURE: f"Infrastructure services and system utilities",
            ModuleType.INTERFACE: f"User interface and interaction handling",
            ModuleType.UTILITY: f"Utility functions and helper services",
            ModuleType.CONFIGURATION: f"Configuration management and settings",
            ModuleType.TESTING: f"Testing infrastructure and test cases",
            ModuleType.EXTERNAL_INTEGRATION: f"External service integration and APIs"
        }
        
        base_purpose = purposes.get(module_type, "System functionality")
        return f"{base_purpose} ({len(modules)} modules)"
    
    def _assign_architectural_layers(self, clusters: List[ModuleCluster]):
        """Assign architectural layers to clusters (already done in creation)."""
        pass  # Layers are assigned during cluster creation
    
    def _build_cluster_dependency_graph(self, clusters: List[ModuleCluster]) -> Dict[str, List[str]]:
        """Build dependency graph between clusters."""
        dependency_graph = {}
        cluster_modules = {cluster.name: set(cluster.modules) for cluster in clusters}
        
        for cluster in clusters:
            cluster_deps = []
            
            # Find which clusters this cluster depends on
            for dep_module in cluster.dependencies:
                for other_cluster_name, other_modules in cluster_modules.items():
                    if dep_module in other_modules and other_cluster_name != cluster.name:
                        if other_cluster_name not in cluster_deps:
                            cluster_deps.append(other_cluster_name)
            
            dependency_graph[cluster.name] = cluster_deps
        
        # Update dependents
        for cluster in clusters:
            cluster.dependents = [c.name for c in clusters 
                                if cluster.name in dependency_graph.get(c.name, [])]
        
        return dependency_graph
    
    def _identify_critical_paths(self, clusters: List[ModuleCluster], 
                                dependency_graph: Dict[str, List[str]]) -> List[List[str]]:
        """Identify critical execution paths through the system."""
        critical_paths = []
        
        # Find high-criticality clusters
        high_crit_clusters = [c for c in clusters if c.criticality > 0.7]
        
        # Build paths from entry points through high-criticality clusters
        for cluster in high_crit_clusters:
            path = self._trace_dependency_path(cluster.name, dependency_graph)
            if len(path) > 1:
                critical_paths.append(path)
        
        return critical_paths
    
    def _trace_dependency_path(self, cluster_name: str, 
                              dependency_graph: Dict[str, List[str]]) -> List[str]:
        """Trace dependency path from a cluster."""
        path = [cluster_name]
        visited = {cluster_name}
        
        current = cluster_name
        while True:
            deps = dependency_graph.get(current, [])
            if not deps:
                break
            
            # Choose the most critical dependency
            next_cluster = deps[0]  # Simplified - could be more sophisticated
            if next_cluster in visited:
                break
            
            path.append(next_cluster)
            visited.add(next_cluster)
            current = next_cluster
        
        return path
    
    def _identify_entry_points(self, clusters: List[ModuleCluster]) -> List[str]:
        """Identify system entry points."""
        entry_points = []
        
        for cluster in clusters:
            # Entry points are typically interface or core clusters with few dependencies
            if (cluster.layer == ArchitecturalLayer.PRESENTATION or 
                cluster.module_type == ModuleType.CORE_BUSINESS):
                if len(cluster.dependencies) <= 2:  # Few external dependencies
                    entry_points.append(cluster.name)
        
        return entry_points
    
    def _discover_core_workflows(self, clusters: List[ModuleCluster], 
                                dependency_graph: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Discover core system workflows."""
        workflows = []
        
        # Workflow 1: User Request Processing
        user_workflow = {
            'name': 'User Request Processing',
            'description': 'How user requests are processed through the system',
            'clusters': [c.name for c in clusters if 'core' in c.name.lower() or 'coder' in c.name.lower()],
            'entry_point': 'ui',
            'critical_path': True
        }
        workflows.append(user_workflow)
        
        # Workflow 2: Context Analysis
        context_workflow = {
            'name': 'Context Analysis',
            'description': 'How context is analyzed and provided to users',
            'clusters': [c.name for c in clusters if 'context' in c.name.lower()],
            'entry_point': 'context',
            'critical_path': True
        }
        workflows.append(context_workflow)
        
        return workflows
    
    def _build_layer_hierarchy(self, clusters: List[ModuleCluster]) -> Dict[ArchitecturalLayer, List[str]]:
        """Build hierarchy of architectural layers."""
        hierarchy = {}
        
        for cluster in clusters:
            layer = cluster.layer
            if layer not in hierarchy:
                hierarchy[layer] = []
            hierarchy[layer].append(cluster.name)
        
        return hierarchy
    
    def get_architecture_summary(self, architecture: SystemArchitecture) -> str:
        """Generate a human-readable summary of the architecture."""
        summary = f"""
🏗️ System Architecture Summary

📊 Overview:
   • {len(architecture.clusters)} functional clusters
   • {len(architecture.layer_hierarchy)} architectural layers
   • {len(architecture.critical_paths)} critical paths
   • {len(architecture.entry_points)} entry points

🏛️ Architectural Layers:
"""
        
        for layer, clusters in architecture.layer_hierarchy.items():
            summary += f"   • {layer.value.title()}: {', '.join(clusters)}\n"
        
        summary += f"""
🔗 Key Dependencies:
"""
        for cluster_name, deps in architecture.dependency_graph.items():
            if deps:
                summary += f"   • {cluster_name} → {', '.join(deps)}\n"
        
        summary += f"""
🚀 Entry Points: {', '.join(architecture.entry_points)}

🔄 Core Workflows:
"""
        for workflow in architecture.core_workflows:
            summary += f"   • {workflow['name']}: {workflow['description']}\n"
        
        return summary
