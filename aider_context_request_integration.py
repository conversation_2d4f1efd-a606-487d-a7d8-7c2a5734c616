#!/usr/bin/env python

import os
import re
import json
from typing import Dict, List, Optional, Any, <PERSON><PERSON>

from context_request_handler import Con<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContextRequest
from aider_template_renderer import Aider<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from aider_integration_service import AiderIntegrationService


class AiderContextRequestIntegration:
    """
    Integrates the context request handler with the Aider system.
    """

    def __init__(self, project_path: str, aider_service: Optional[AiderIntegrationService] = None):
        """
        Initialize the integration.

        Args:
            project_path: Path to the project root
            aider_service: Optional AiderIntegrationService instance
        """
        self.project_path = project_path
        self.aider_service = aider_service or AiderIntegrationService()
        self.context_handler = ContextRequestHandler(project_path, self.aider_service)
        self.template_renderer = AiderTemplateRenderer()
        self.conversation_history = []
        self.max_context_request_iterations = 3
        self.current_iteration = 0

    def get_llm_instructions(self) -> str:
        """
        Get the instructions for the LLM to use the IR_REQUEST protocol.

        Returns:
            Instructions for the LLM
        """
        return """
You can request intelligent code context using the IR_REQUEST protocol:

{IR_REQUEST: {
  "focus_entities": ["keyword1", "keyword2", "function_name"],
  "task_type": "debugging"
}}

Available task_types:
- "debugging": Prioritizes error handling, caching, performance issues
- "feature_development": Focuses on extension points, dependencies
- "code_review": Emphasizes criticality, change risk, side effects
- "refactoring": Prioritizes change risk, usage frequency
- "general_analysis": Balanced approach across all factors

The system will intelligently select the most relevant code context based on your focus entities and task type.

LEGACY SUPPORT: CONTEXT_REQUEST is still available for backward compatibility:
{CONTEXT_REQUEST: {
  "original_user_query_context": "Brief description",
  "symbols_of_interest": [{"type": "method_definition", "name": "ClassName.method_name"}],
  "reason_for_request": "Why you need this context"
}}
"""

    def detect_context_request(self, llm_response: str) -> Optional[ContextRequest]:
        """
        Detect if the LLM response contains a context request.

        Args:
            llm_response: The LLM response to check

        Returns:
            A ContextRequest object if found, None otherwise
        """
        print("\n=== DETECTING CONTEXT REQUEST ===")
        print(f"LLM response (first 200 chars): {llm_response[:200]}..." if len(llm_response) > 200 else f"LLM response: {llm_response}")

        context_request = self.context_handler.parse_context_request(llm_response)

        if context_request:
            print(f"Context request detected: {context_request}")
            symbols = [s.name for s in context_request.symbols_of_interest]
            print(f"Symbols of interest: {', '.join(symbols)}")
        else:
            print("No context request detected")

        print("=== END OF CONTEXT REQUEST DETECTION ===\n")

        return context_request

    def process_context_request(self,
                               context_request: ContextRequest,
                               original_user_query: str,
                               repo_overview: str) -> str:
        """
        Process a context request and generate an augmented prompt.

        Args:
            context_request: The context request to process
            original_user_query: The original user query
            repo_overview: The repository overview

        Returns:
            An augmented prompt with the extracted context
        """
        # Log the inputs
        print("\n\n=== CONTEXT REQUEST PROCESSING ===")
        print(f"Original user query: {original_user_query}")
        print(f"Context request: {context_request}")
        print(f"Repo overview length: {len(repo_overview)} characters")
        print(f"Conversation history: {self.conversation_history}")

        # Increment the iteration counter
        self.current_iteration += 1

        # Process the context request
        extracted_context = self.context_handler.process_context_request(context_request)

        # Log the extracted context
        print("\n=== EXTRACTED CONTEXT ===")
        print(f"Original user query context: {extracted_context.get('original_user_query_context', '')}")
        print(f"Reason for request: {extracted_context.get('reason_for_request', '')}")
        print(f"Number of extracted symbols: {len(extracted_context.get('extracted_symbols', []))}")
        print(f"Number of dependency snippets: {len(extracted_context.get('dependency_snippets', []))}")

        # Render the augmented prompt
        augmented_prompt = self.template_renderer.render_augmented_prompt(
            original_query=original_user_query,
            repo_overview=repo_overview,
            extracted_context=extracted_context,
            conversation_history=self.conversation_history
        )

        # Log the augmented prompt
        print("\n=== AUGMENTED PROMPT ===")
        print(augmented_prompt[:500] + "..." if len(augmented_prompt) > 500 else augmented_prompt)
        print("=== END OF CONTEXT REQUEST PROCESSING ===\n\n")

        return augmented_prompt

    def update_conversation_history(self, role: str, content: str) -> None:
        """
        Update the conversation history.

        Args:
            role: The role of the message (user or assistant)
            content: The content of the message
        """
        print("\n=== UPDATING CONVERSATION HISTORY ===")
        print(f"Adding message with role: {role}")
        print(f"Content (first 100 chars): {content[:100]}..." if len(content) > 100 else f"Content: {content}")
        print(f"Current history length: {len(self.conversation_history)}")

        # Log the current history before update
        if self.conversation_history:
            print("\nCurrent conversation history BEFORE update:")
            for i, msg in enumerate(self.conversation_history):
                print(f"Message {i+1} - Role: {msg.get('role', '')}")
                msg_content = msg.get('content', '')
                print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
                print("-" * 40)

        self.conversation_history.append({
            "role": role,
            "content": content
        })

        # Limit the conversation history to the last 10 messages
        # This prevents the history from growing too large and confusing the LLM
        if len(self.conversation_history) > 10:
            self.conversation_history = self.conversation_history[-10:]
            print(f"Trimmed history to last 10 messages")

        # Log the updated history
        print("\nConversation history AFTER update:")
        for i, msg in enumerate(self.conversation_history):
            print(f"Message {i+1} - Role: {msg.get('role', '')}")
            msg_content = msg.get('content', '')
            print(f"Content (first 50 chars): {msg_content[:50]}..." if len(msg_content) > 50 else f"Content: {msg_content}")
            print("-" * 40)

        print(f"New history length: {len(self.conversation_history)}")
        print("=== END OF CONVERSATION HISTORY UPDATE ===\n")

    def reset_iteration_counter(self) -> None:
        """Reset the iteration counter."""
        self.current_iteration = 0

    def has_reached_max_iterations(self) -> bool:
        """
        Check if the maximum number of context request iterations has been reached.

        Returns:
            True if the maximum has been reached, False otherwise
        """
        return self.current_iteration >= self.max_context_request_iterations

    def get_context_request_summary(self, context_request: ContextRequest) -> str:
        """
        Get a summary of the context request for logging purposes.

        Args:
            context_request: The context request to summarize

        Returns:
            A summary of the context request
        """
        symbols = [s.name for s in context_request.symbols_of_interest]
        return f"Context request for symbols: {', '.join(symbols)}"
