#!/usr/bin/env python3
"""
Debug the class methods in IR slices
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def debug_class_methods_ir():
    """Debug class methods in IR slices"""
    print("🔍 DEBUGGING CLASS METHODS IN IR SLICES")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test with a position management query
        query = "how does the system manage position?"
        
        request = IRContextRequest(
            user_query=query,
            task_description=f'Debug class methods for: {query}',
            task_type='debugging',
            focus_entities=None,
            max_tokens=3000,
            include_ir_slices=True,
            include_code_context=False,
            llm_friendly=False,  # Get raw IR slices first
            max_entities=3
        )
        
        print(f"🧪 Testing query: '{query}'")
        print(f"   Requesting raw IR slices...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
            return False
        
        # Get IR slices and check for classes
        ir_slices = result.get('ir_slices', [])
        
        print(f"\n📊 IR SLICES ANALYSIS:")
        print(f"   Total IR slices: {len(ir_slices)}")
        
        for i, ir_slice in enumerate(ir_slices):
            entity_name = ir_slice.get('entity_name', 'unknown')
            entity_type = ir_slice.get('entity_type', 'unknown')
            
            print(f"\n🔍 IR Slice {i+1}: {entity_name} ({entity_type})")
            
            # Check all keys in the IR slice
            all_keys = list(ir_slice.keys())
            print(f"   All keys: {all_keys}")
            
            # Check specifically for class_methods
            if 'class_methods' in ir_slice:
                class_methods = ir_slice['class_methods']
                print(f"   ✅ class_methods found: {len(class_methods)} methods")
                
                for j, method in enumerate(class_methods[:3]):  # Show first 3 methods
                    method_name = method.get('name', 'unknown')
                    complexity = method.get('complexity', 0)
                    print(f"      Method {j+1}: {method_name} (complexity: {complexity})")
            else:
                print(f"   ❌ class_methods NOT found in IR slice")
            
            # Check if it's a class
            if entity_type == 'class':
                print(f"   🏛️ This is a class - should have methods!")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_method_lookup():
    """Test the method lookup function directly"""
    print(f"\n🔧 TESTING DIRECT METHOD LOOKUP")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        import json
        
        # Load the latest IR data file
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return False
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        with open(latest_ir_file, 'r', encoding='utf-8') as f:
            ir_data = json.load(f)
        
        # Create handler
        handler = ContextRequestHandler('.')
        
        # Test with known classes
        test_classes = ['AiderProjectManager', 'DependencyIntegrationManager', 'SystemWorkflow']
        
        for class_name in test_classes:
            print(f"\n🏛️ Testing class: {class_name}")
            
            # Call the method lookup function directly
            methods = handler._find_class_methods(class_name, ir_data)
            
            print(f"   Found {len(methods)} methods")
            
            if methods:
                for method in methods[:3]:  # Show first 3
                    method_name = method.get('name', 'unknown')
                    complexity = method.get('complexity', 0)
                    calls_super = method.get('calls_super', False)
                    super_indicator = " 🔗" if calls_super else ""
                    print(f"      - {method_name}{super_indicator} (complexity: {complexity})")
                
                if len(methods) > 3:
                    print(f"      - ... (+{len(methods) - 3} more)")
            else:
                print(f"      ❌ No methods found")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DEBUGGING CLASS METHODS ENHANCEMENT")
    print("=" * 80)
    
    # Test 1: Direct method lookup
    lookup_success = test_direct_method_lookup()
    
    # Test 2: IR slices analysis
    ir_success = debug_class_methods_ir()
    
    print(f"\n🏆 DEBUG RESULTS:")
    print(f"   Direct lookup: {'✅' if lookup_success else '❌'}")
    print(f"   IR slices: {'✅' if ir_success else '❌'}")
    
    if lookup_success and not ir_success:
        print(f"\n💡 DIAGNOSIS:")
        print(f"   Method lookup works, but methods aren't being stored in IR slices")
        print(f"   The issue is in the IR slice creation or storage process")
    elif not lookup_success:
        print(f"\n💡 DIAGNOSIS:")
        print(f"   Method lookup itself is broken")
        print(f"   Need to fix the _find_class_methods function")
