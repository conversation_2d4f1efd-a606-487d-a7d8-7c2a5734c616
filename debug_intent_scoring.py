#!/usr/bin/env python3
"""
Debug Intent Scoring

This script debugs the intent scoring logic to understand why enhanced heuristics
are not working correctly.
"""

import sys
import os
import re
sys.path.append('aider-main/aider/context_request')

from intelligent_context_models import QueryIntent, QUERY_INTENT_PATTERNS


def debug_intent_scoring(query: str):
    """
    Debug the intent scoring for a specific query.
    """
    print(f"🔍 Debugging Intent Scoring for: '{query}'")
    print("=" * 60)
    
    query_lower = query.lower()
    intent_scores = {}
    
    # Step 1: Pattern-based scoring
    print("📋 Step 1: Pattern-based scoring")
    for intent, patterns in QUERY_INTENT_PATTERNS.items():
        score = 0
        print(f"\n{intent.value}:")
        for pattern in patterns:
            if re.search(pattern, query_lower):
                score += 1
                print(f"   ✅ Match: {pattern}")
            else:
                print(f"   ❌ No match: {pattern}")
        intent_scores[intent] = score
        print(f"   Score: {score}")
    
    print(f"\nPattern scores: {[(intent.value, score) for intent, score in intent_scores.items()]}")
    
    # Step 2: Enhanced heuristics
    print("\n📋 Step 2: Enhanced heuristics")
    
    # CRITICAL: Aider-specific workflow indicators (HIGHEST PRIORITY)
    aider_phrases = ['apply edits', 'edit files', 'file editing', 'edits to files']
    for phrase in aider_phrases:
        if phrase in query_lower:
            old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
            intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 5
            print(f"   ✅ Found Aider phrase '{phrase}': workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")
    
    chat_phrases = ['chat loop', 'send message', 'llm api', 'interact with llm']
    for phrase in chat_phrases:
        if phrase in query_lower:
            old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
            intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 5
            print(f"   ✅ Found chat phrase '{phrase}': workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")
    
    context_phrases = ['gather context', 'repo map', 'context from repository']
    for phrase in context_phrases:
        if phrase in query_lower:
            old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
            intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 5
            print(f"   ✅ Found context phrase '{phrase}': workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")
    
    git_phrases = ['git operations', 'handle git', 'git integration']
    for phrase in git_phrases:
        if phrase in query_lower:
            old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
            intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 5
            print(f"   ✅ Found git phrase '{phrase}': workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")

    # Enhanced workflow indicators
    workflow_words = ['apply', 'edit', 'send', 'handle', 'process', 'work', 'loop', 'run']
    found_workflow_words = [word for word in workflow_words if word in query_lower]
    if found_workflow_words:
        old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
        intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 3
        print(f"   ✅ Found workflow words {found_workflow_words}: workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")

    # Other heuristics
    if any(word in query_lower for word in ['error', 'bug', 'fail', 'issue', 'problem', 'broken', 'not working']):
        old_score = intent_scores.get(QueryIntent.DEBUGGING_ASSISTANCE, 0)
        intent_scores[QueryIntent.DEBUGGING_ASSISTANCE] = old_score + 2
        print(f"   ✅ Found debugging words: debugging_assistance {old_score} → {intent_scores[QueryIntent.DEBUGGING_ASSISTANCE]}")

    if any(word in query_lower for word in ['add', 'create', 'implement', 'build', 'develop', 'extend']):
        old_score = intent_scores.get(QueryIntent.FEATURE_IMPLEMENTATION, 0)
        intent_scores[QueryIntent.FEATURE_IMPLEMENTATION] = old_score + 2
        print(f"   ✅ Found implementation words: feature_implementation {old_score} → {intent_scores[QueryIntent.FEATURE_IMPLEMENTATION]}")

    if any(word in query_lower for word in ['architecture', 'design', 'structure', 'overview', 'organization']):
        old_score = intent_scores.get(QueryIntent.ARCHITECTURE_UNDERSTANDING, 0)
        intent_scores[QueryIntent.ARCHITECTURE_UNDERSTANDING] = old_score + 2
        print(f"   ✅ Found architecture words: architecture_understanding {old_score} → {intent_scores[QueryIntent.ARCHITECTURE_UNDERSTANDING]}")

    if any(word in query_lower for word in ['flow', 'sequence', 'steps', 'workflow', 'pipeline']):
        old_score = intent_scores.get(QueryIntent.WORKFLOW_ANALYSIS, 0)
        intent_scores[QueryIntent.WORKFLOW_ANALYSIS] = old_score + 2
        print(f"   ✅ Found general workflow words: workflow_analysis {old_score} → {intent_scores[QueryIntent.WORKFLOW_ANALYSIS]}")

    if any(word in query_lower for word in ['performance', 'slow', 'fast', 'optimize', 'speed', 'efficiency']):
        old_score = intent_scores.get(QueryIntent.PERFORMANCE_ANALYSIS, 0)
        intent_scores[QueryIntent.PERFORMANCE_ANALYSIS] = old_score + 2
        print(f"   ✅ Found performance words: performance_analysis {old_score} → {intent_scores[QueryIntent.PERFORMANCE_ANALYSIS]}")

    if any(word in query_lower for word in ['security', 'auth', 'permission', 'access', 'secure', 'vulnerability']):
        old_score = intent_scores.get(QueryIntent.SECURITY_ANALYSIS, 0)
        intent_scores[QueryIntent.SECURITY_ANALYSIS] = old_score + 2
        print(f"   ✅ Found security words: security_analysis {old_score} → {intent_scores[QueryIntent.SECURITY_ANALYSIS]}")
    
    print(f"\nFinal scores: {[(intent.value, score) for intent, score in intent_scores.items() if score > 0]}")
    
    # Step 3: Final selection
    print("\n📋 Step 3: Final selection")
    if not intent_scores or max(intent_scores.values()) == 0:
        final_intent = QueryIntent.COMPONENT_DISCOVERY
        print(f"   No scores or all zero → default to {final_intent.value}")
    else:
        final_intent = max(intent_scores, key=intent_scores.get)
        max_score = intent_scores[final_intent]
        print(f"   Highest score: {final_intent.value} ({max_score})")
    
    print(f"\n🎯 FINAL RESULT: {final_intent.value}")
    
    return final_intent


def main():
    """
    Main debug function.
    """
    print("🐛 Intent Scoring Debug Tool")
    print("=" * 70)
    
    # Test the problematic queries
    test_queries = [
        "How does Aider apply edits to files?",
        "How does Aider's chat loop work?",
        "How does Aider gather context from the repository?",
        "How does Aider interact with LLM APIs?",
        "How does Aider handle Git operations?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{'='*70}")
        print(f"TEST {i}")
        debug_intent_scoring(query)
        print()


if __name__ == "__main__":
    main()
