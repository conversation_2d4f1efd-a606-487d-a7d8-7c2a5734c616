#!/usr/bin/env python3
"""
Debug the IR_REQUEST parsing issue
"""

import re
import json

def test_ir_request_parsing():
    """Test the IR_REQUEST parsing with the exact content from the LLM"""
    
    print("🔍 Debugging IR_REQUEST Parsing Issue")
    print("=" * 50)
    
    # The exact content that the LLM sent (based on the error message)
    llm_content = """{IR_REQUEST: { "focus_entities": ["compute_next_boundary", "get_latest_candle_time"], "task_type": "debugging" }}"""
    
    print(f"📋 LLM Content:")
    print(f"'{llm_content}'")
    print()
    
    # Test the FIXED regex patterns
    patterns = [
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    for i, pattern in enumerate(patterns, 1):
        print(f"🧪 Testing Pattern {i}: {pattern}")
        
        match = re.search(pattern, llm_content, re.DOTALL)
        if match:
            captured = match.group(1).strip()
            print(f"✅ MATCH FOUND!")
            print(f"   Captured: '{captured}'")
            
            # Try to parse the JSON
            try:
                parsed = json.loads(captured)
                print(f"✅ JSON PARSED SUCCESSFULLY!")
                print(f"   Parsed: {parsed}")
                print(f"   focus_entities: {parsed.get('focus_entities')}")
                print(f"   task_type: {parsed.get('task_type')}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON PARSING FAILED: {e}")
                print(f"   Error at character {e.pos}: '{captured[e.pos-5:e.pos+5]}'")
                
                # Show character by character around the error
                if hasattr(e, 'pos') and e.pos < len(captured):
                    start = max(0, e.pos - 10)
                    end = min(len(captured), e.pos + 10)
                    print(f"   Context: '{captured[start:end]}'")
                    print(f"   Error pos: {' ' * (e.pos - start)}^")
            
            print()
            break
        else:
            print(f"❌ NO MATCH")
            print()
    
    # Test with a simpler approach
    print("🔧 Testing Alternative Parsing Approach")
    print("-" * 40)
    
    # Try to extract just the JSON part manually
    if "IR_REQUEST:" in llm_content:
        start_idx = llm_content.find("IR_REQUEST:") + len("IR_REQUEST:")
        json_part = llm_content[start_idx:].strip()
        
        # Remove the closing }
        if json_part.endswith("}"):
            json_part = json_part[:-1].strip()
        
        print(f"📋 Manual extraction: '{json_part}'")
        
        try:
            parsed = json.loads(json_part)
            print(f"✅ MANUAL PARSING SUCCESS!")
            print(f"   Parsed: {parsed}")
        except json.JSONDecodeError as e:
            print(f"❌ MANUAL PARSING FAILED: {e}")

if __name__ == "__main__":
    test_ir_request_parsing()
