#!/usr/bin/env python3
"""
Debug the IR data structure to understand how methods are stored
"""

import json
from pathlib import Path

def debug_ir_structure():
    """Debug the IR data structure"""
    print("🔍 DEBUGGING IR DATA STRUCTURE")
    print("=" * 60)
    
    # Find the latest IR data file
    ir_files = list(Path(".").glob("ir_data_*.json"))
    if not ir_files:
        print("❌ No IR data files found")
        return
        
    latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
    print(f"📊 Loading IR data from: {latest_ir_file}")
    
    with open(latest_ir_file, 'r', encoding='utf-8') as f:
        ir_data = json.load(f)
    
    # Analyze the structure
    modules = ir_data.get('modules', [])
    print(f"📁 Total modules: {len(modules)}")
    
    # Count entity types
    entity_types = {}
    entities_with_class_name = 0
    sample_entities = []
    
    for module in modules[:5]:  # Check first 5 modules
        module_name = module.get('name', 'unknown')
        entities = module.get('entities', [])
        
        print(f"\n📁 Module: {module_name}")
        print(f"   Entities: {len(entities)}")
        
        for entity in entities[:3]:  # Show first 3 entities per module
            entity_type = entity.get('type', 'unknown')
            entity_name = entity.get('name', 'unknown')
            
            # Count entity types
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            # Check for class_name attribute
            if 'class_name' in entity:
                entities_with_class_name += 1
                print(f"      ✅ {entity_name} ({entity_type}) - class_name: {entity.get('class_name')}")
            else:
                print(f"      ❌ {entity_name} ({entity_type}) - no class_name")
            
            # Collect sample entities
            if len(sample_entities) < 10:
                sample_entities.append({
                    'name': entity_name,
                    'type': entity_type,
                    'module': module_name,
                    'keys': list(entity.keys())
                })
    
    print(f"\n📊 ENTITY TYPE SUMMARY:")
    for entity_type, count in sorted(entity_types.items()):
        print(f"   {entity_type}: {count}")
    
    print(f"\n🔍 ENTITIES WITH class_name: {entities_with_class_name}")
    
    print(f"\n📋 SAMPLE ENTITY STRUCTURES:")
    for entity in sample_entities:
        print(f"   {entity['name']} ({entity['type']}):")
        print(f"      Keys: {entity['keys']}")
    
    # Look specifically for methods
    print(f"\n🔍 SEARCHING FOR METHODS:")
    method_count = 0
    function_count = 0
    
    for module in modules:
        for entity in module.get('entities', []):
            entity_type = entity.get('type', '')
            if entity_type == 'method':
                method_count += 1
                if method_count <= 5:  # Show first 5 methods
                    print(f"   Method: {entity.get('name', '')} in {module.get('name', '')}")
                    print(f"      Keys: {list(entity.keys())}")
            elif entity_type == 'function':
                function_count += 1
    
    print(f"\n📊 METHOD vs FUNCTION COUNT:")
    print(f"   Methods: {method_count}")
    print(f"   Functions: {function_count}")
    
    # Check if methods are stored as functions with class context
    print(f"\n🔍 CHECKING FUNCTION ENTITIES FOR CLASS CONTEXT:")
    functions_with_class_info = 0
    
    for module in modules:
        for entity in module.get('entities', []):
            if entity.get('type') == 'function':
                # Check various ways class info might be stored
                has_class_info = any(key in entity for key in ['class_name', 'class', 'containing_class', 'parent_class'])
                if has_class_info:
                    functions_with_class_info += 1
                    if functions_with_class_info <= 5:
                        print(f"   Function with class info: {entity.get('name', '')}")
                        for key in ['class_name', 'class', 'containing_class', 'parent_class']:
                            if key in entity:
                                print(f"      {key}: {entity[key]}")
    
    print(f"\n📊 Functions with class info: {functions_with_class_info}")

if __name__ == "__main__":
    debug_ir_structure()
