#!/usr/bin/env python3
"""
Debug script to understand symbol extraction behavior.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

from surgical_file_extractor import SurgicalFileExtractor
from aider.context_request.aider_integration_service import AiderIntegrationService

def debug_symbol_extraction():
    """Debug the symbol extraction process."""
    print("🔍 Debugging symbol extraction...")

    # Initialize services
    aider_service = AiderIntegrationService()

    # Initialize the repository map
    print("🔧 Initializing repository map...")
    try:
        repo_map = aider_service.get_repo_map(".")
        if repo_map:
            print("✅ Repository map initialized successfully")
        else:
            print("❌ Failed to initialize repository map")
            return
    except Exception as e:
        print(f"❌ Error initializing repository map: {e}")
        return

    extractor = SurgicalFileExtractor(aider_service)
    
    # Test file and symbol
    file_path = "context_request_handler.py"
    project_path = "."
    target_symbol = "ContextRequestHandler"
    
    print(f"\n📁 File: {file_path}")
    print(f"🎯 Target symbol: {target_symbol}")
    
    # Step 1: Get all symbols in the file
    print(f"\n=== Step 1: Get all symbols in file ===")

    # First, let's see what raw tags we get from the repository map
    repo_map = extractor._get_repo_map(project_path)
    if repo_map:
        abs_file_path = os.path.join(project_path, file_path)
        rel_file_path = repo_map.get_rel_fname(abs_file_path)
        raw_tags = repo_map.get_tags(abs_file_path, rel_file_path)

        print(f"Raw tags from repository map:")
        for tag in raw_tags:
            print(f"  - {tag.name} (kind: {tag.kind}, line: {tag.line})")

    file_symbols = extractor.get_symbols_in_file(project_path, file_path)

    print(f"\nFound {len(file_symbols)} symbols:")
    for i, symbol in enumerate(file_symbols, 1):
        print(f"  {i}. {symbol.name} (type: {symbol.symbol_type}, line: {symbol.start_line})")
    
    # Step 2: Find target symbol
    print(f"\n=== Step 2: Find target symbol ===")
    target = next((s for s in file_symbols if s.name == target_symbol), None)
    if target:
        print(f"✅ Found target: {target.name} (type: {target.symbol_type}, line: {target.start_line})")
    else:
        print(f"❌ Target symbol '{target_symbol}' not found!")
        return
    
    # Step 3: Find next symbol boundary
    print(f"\n=== Step 3: Find next symbol boundary ===")
    if target.symbol_type == "class":
        print("Target is a class - looking for next top-level symbol...")
        next_symbols = []
        for symbol in file_symbols:
            if symbol.start_line > target.start_line:
                print(f"  Checking symbol: {symbol.name} (type: {symbol.symbol_type}, line: {symbol.start_line})")
                # Include next classes
                if symbol.symbol_type == "class":
                    print(f"    ✅ Including class: {symbol.name}")
                    next_symbols.append(symbol)
                # Include top-level functions (not methods within classes)
                elif symbol.symbol_type == "function":
                    is_within_class = extractor._is_symbol_within_class(file_symbols, symbol)
                    print(f"    Function {symbol.name} within class: {is_within_class}")
                    if not is_within_class:
                        print(f"    ✅ Including top-level function: {symbol.name}")
                        next_symbols.append(symbol)
                    else:
                        print(f"    ❌ Skipping method within class: {symbol.name}")
    else:
        print("Target is a function/method - looking for next symbol at any level...")
        next_symbols = [s for s in file_symbols if s.start_line > target.start_line]
    
    print(f"\nNext symbols found: {len(next_symbols)}")
    for symbol in next_symbols:
        print(f"  - {symbol.name} (line: {symbol.start_line})")
    
    # Step 4: Determine extraction range
    print(f"\n=== Step 4: Determine extraction range ===")
    if next_symbols:
        next_boundary = min(next_symbols, key=lambda s: s.start_line)
        end_line = next_boundary.start_line - 1
        print(f"Next boundary: {next_boundary.name} at line {next_boundary.start_line}")
        print(f"Extraction range: lines {target.start_line} to {end_line}")
    else:
        file_line_count = extractor._get_file_line_count(project_path, file_path)
        end_line = file_line_count
        print(f"No next boundary found - extracting to end of file (line {end_line})")
    
    # Step 5: Extract content
    print(f"\n=== Step 5: Extract content ===")
    content = extractor.extract_symbol_content(target_symbol, file_path, project_path)
    if content:
        lines = content.split('\n')
        print(f"Extracted content: {len(content)} characters, {len(lines)} lines")
        print(f"First line: {lines[0] if lines else 'N/A'}")
        print(f"Last line: {lines[-1] if lines else 'N/A'}")
        
        # Check for methods
        method_count = content.count('def ')
        print(f"Method definitions found: {method_count}")
        
        if method_count > 0:
            print("✅ SUCCESS: Full class implementation extracted!")
        else:
            print("❌ ISSUE: Only class signature extracted!")
    else:
        print("❌ Failed to extract content!")

if __name__ == "__main__":
    debug_symbol_extraction()
