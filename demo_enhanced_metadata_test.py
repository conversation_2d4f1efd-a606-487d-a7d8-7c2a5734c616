#!/usr/bin/env python3
"""
Demo Test for Enhanced Metadata System

This script runs a small demonstration of the Enhanced Metadata System
comprehensive testing capabilities with a subset of queries.
"""

import time
from datetime import datetime
from enhanced_metadata_comprehensive_test import EnhancedMetadataTestRunner
from test_queries import TestQueries
from test_results_analyzer import TestResultsAnalyzer


def run_demo_test():
    """Run a demonstration test with a subset of queries."""
    print("🎯 Enhanced Metadata System - Demo Test")
    print("=" * 60)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Initialize test runner
    runner = EnhancedMetadataTestRunner(".")
    
    # Select a representative subset of queries (one from each category)
    all_queries = TestQueries.get_all_queries()
    demo_queries = [
        all_queries[0],   # Architecture Overview
        all_queries[10],  # Workflow Analysis
        all_queries[20],  # Component Deep Dive
        all_queries[30],  # Cross-Cutting Concerns
        all_queries[40],  # Integration & External Systems
        all_queries[45]   # Debugging & Maintenance
    ]
    
    print(f"📋 Running demo with {len(demo_queries)} representative queries:")
    for i, query in enumerate(demo_queries, 1):
        print(f"   {i}. {query.category}: {query.query[:50]}...")
    print()
    
    # Execute demo queries
    print("🔍 Executing Demo Queries...")
    print("-" * 50)
    
    start_time = time.time()
    results = []
    
    for query in demo_queries:
        result = runner.execute_test_query(
            query.id, query.query, query.expected_strategy, query.category
        )
        results.append(result)
        print()  # Add spacing
    
    total_time = time.time() - start_time
    
    print("=" * 60)
    print(f"✅ Demo Test Completed in {total_time:.1f} seconds")
    print()
    
    # Analyze results
    analyzer = TestResultsAnalyzer(results)
    summary = analyzer.generate_summary_metrics()
    categories = analyzer.generate_category_breakdown()
    issues = analyzer.identify_performance_issues()
    recommendations = analyzer.generate_recommendations()
    
    # Display summary
    print("📊 DEMO RESULTS SUMMARY")
    print("=" * 60)
    
    print(f"🎯 Success Rate: {summary['success_rate']:.1%}")
    print(f"⏱️  Average Processing Time: {summary['average_processing_time']:.2f}s")
    print(f"📋 Metadata Completeness: {summary['metadata_completeness_avg']:.1%}")
    print(f"🎨 Type Icon Coverage: {summary['type_icons_coverage']:.1%}")
    print(f"🎯 Strategy Alignment: {summary['strategy_alignment_avg']:.1%}")
    print(f"⭐ Overall Quality Score: {summary['overall_quality_avg']:.1%}")
    print()
    
    # Show individual results
    print("📈 INDIVIDUAL QUERY RESULTS")
    print("-" * 60)
    for result in results:
        status = "✅" if result.success else "❌"
        quality = result.analysis.get('overall_quality_score', 0) if result.analysis else 0
        print(f"{status} Query {result.query_id}: {result.category}")
        print(f"   Time: {result.processing_time:.2f}s | Quality: {quality:.1%}")
        if not result.success:
            print(f"   Error: {result.error}")
        print()
    
    # Issues and recommendations
    if issues:
        print("⚠️  IDENTIFIED ISSUES")
        print("-" * 40)
        for issue in issues:
            print(f"• {issue['description']}")
        print()
    
    if recommendations:
        print("💡 RECOMMENDATIONS")
        print("-" * 40)
        for rec in recommendations:
            print(f"• {rec}")
        print()
    
    # Success validation
    success_criteria = [
        ("Success Rate ≥90%", summary['success_rate'] >= 0.9),
        ("Quality Score ≥75%", summary['overall_quality_avg'] >= 0.75),
        ("Processing ≤15s avg", summary['average_processing_time'] <= 15.0)
    ]
    
    print("✅ SUCCESS CRITERIA")
    print("-" * 40)
    for criterion, passed in success_criteria:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{criterion:<25} {status}")
    
    passed = sum(1 for _, p in success_criteria if p)
    print(f"\nCriteria Met: {passed}/{len(success_criteria)}")
    
    if passed == len(success_criteria):
        print("\n🎉 Demo test successful! System is ready for comprehensive testing.")
        print("\n💡 To run the full 50-query test suite:")
        print("   python enhanced_metadata_comprehensive_test.py")
    else:
        print("\n⚠️ Some criteria not met. Review issues before full testing.")
    
    return summary['success_rate'] >= 0.8  # 80% success rate for demo


if __name__ == "__main__":
    try:
        success = run_demo_test()
        exit_code = 0 if success else 1
    except Exception as e:
        print(f"\n❌ Demo test failed: {e}")
        import traceback
        traceback.print_exc()
        exit_code = 1
    
    exit(exit_code)
