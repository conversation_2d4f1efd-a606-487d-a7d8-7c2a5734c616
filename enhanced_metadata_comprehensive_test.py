#!/usr/bin/env python3
"""
Enhanced Metadata System - Comprehensive Testing Script

This script implements the comprehensive testing protocol outlined in task.txt
to validate the Enhanced Metadata System's context package generation capabilities.
"""

import json
import time
import os
import re
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict

# Import the Enhanced Metadata System components
try:
    from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
except ImportError:
    # Fallback for local testing
    try:
        from context_request_handler import <PERSON>textR<PERSON><PERSON><PERSON><PERSON>ler, IRContextRequest
    except ImportError:
        # Try importing from the aider-main directory structure
        import sys
        import os
        sys.path.append(os.path.join('.', 'aider-main'))
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest


@dataclass
class TestResult:
    """Represents the result of a single test query."""
    query_id: int
    query: str
    category: str
    expected_strategy: str
    processing_time: float
    success: bool
    analysis: Optional[Dict[str, Any]] = None
    package: Optional[str] = None
    error: Optional[str] = None
    timestamp: str = ""


class EnhancedMetadataTestRunner:
    """
    Main test runner for the Enhanced Metadata System comprehensive testing.
    
    This class implements the testing protocol from task.txt, executing 50 diverse
    user queries across different categories to validate system performance.
    """
    
    def __init__(self, project_path: str = "."):
        """
        Initialize the test runner.
        
        Args:
            project_path: Path to the project root for testing
        """
        self.project_path = project_path
        self.handler = ContextRequestHandler(project_path)
        self.results: List[TestResult] = []
        self.start_time = None
        self.end_time = None
        
        # Test configuration
        self.test_config = {
            'project_path': project_path,
            'max_entities': 8,
            'max_tokens': 8000,
            'max_output_chars': 30000,
            'include_metadata': True,
            'llm_friendly': True
        }
        
        print("🧪 Enhanced Metadata System - Comprehensive Test Runner")
        print("=" * 70)
        print(f"📁 Project Path: {project_path}")
        print(f"⚙️  Configuration: {self.test_config}")
        print()
    
    def execute_test_query(self, query_id: int, query: str, expected_strategy: str, category: str) -> TestResult:
        """
        Execute a single test query and collect results.
        
        Args:
            query_id: Unique identifier for the query
            query: The user query to test
            expected_strategy: Expected strategy selection
            category: Query category
            
        Returns:
            TestResult object with execution results
        """
        print(f"🔍 Test {query_id:2d}: {category}")
        print(f"   Query: {query}")
        print(f"   Expected Strategy: {expected_strategy}")
        
        start_time = time.time()
        
        # Create IR context request
        request = IRContextRequest(
            user_query=query,
            task_description=f"Test query {query_id}: {category}",
            task_type="analysis",
            max_entities=self.test_config['max_entities'],
            llm_friendly=self.test_config['llm_friendly'],
            max_tokens=self.test_config['max_tokens'],
            max_output_chars=self.test_config['max_output_chars'],
            include_ir_slices=True,
            include_code_context=True
        )
        
        try:
            # Process the request
            result = self.handler.process_ir_context_request(request)
            processing_time = time.time() - start_time
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                return TestResult(
                    query_id=query_id,
                    query=query,
                    category=category,
                    expected_strategy=expected_strategy,
                    processing_time=processing_time,
                    success=False,
                    error=result['error'],
                    timestamp=datetime.now().isoformat()
                )
            
            # Analyze the generated package
            package = result.get('llm_friendly_package', '')
            analysis = self.analyze_package(package, expected_strategy)
            
            print(f"   ✅ Success: {len(package):,} chars, {processing_time:.2f}s")
            print(f"   📊 Quality Score: {analysis.get('overall_quality_score', 0):.1%}")
            
            return TestResult(
                query_id=query_id,
                query=query,
                category=category,
                expected_strategy=expected_strategy,
                processing_time=processing_time,
                success=True,
                analysis=analysis,
                package=package,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            print(f"   ❌ Exception: {str(e)}")
            
            return TestResult(
                query_id=query_id,
                query=query,
                category=category,
                expected_strategy=expected_strategy,
                processing_time=processing_time,
                success=False,
                error=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def analyze_package(self, package: str, expected_strategy: str) -> Dict[str, Any]:
        """
        Analyze the generated package for quality metrics.
        
        Args:
            package: The generated LLM-friendly package
            expected_strategy: Expected strategy for this query
            
        Returns:
            Dictionary containing analysis metrics
        """
        if not package:
            return {
                'metadata_completeness': 0.0,
                'type_icons_present': 0.0,
                'strategy_indicators': 0.0,
                'entity_count': 0,
                'package_length': 0,
                'readability_score': 0.0,
                'architectural_context': 0.0,
                'overall_quality_score': 0.0
            }
        
        analysis = {
            'metadata_completeness': self.check_metadata_completeness(package),
            'type_icons_present': self.check_type_icons(package),
            'strategy_indicators': self.check_strategy_indicators(package, expected_strategy),
            'entity_count': self.count_entities(package),
            'package_length': len(package),
            'readability_score': self.assess_readability(package),
            'architectural_context': self.check_architectural_context(package)
        }
        
        # Calculate overall quality score
        weights = {
            'metadata_completeness': 0.25,
            'type_icons_present': 0.15,
            'strategy_indicators': 0.20,
            'readability_score': 0.20,
            'architectural_context': 0.20
        }
        
        overall_score = sum(
            analysis[metric] * weight 
            for metric, weight in weights.items()
        )
        analysis['overall_quality_score'] = overall_score
        
        return analysis

    def check_metadata_completeness(self, package: str) -> float:
        """
        Verify all required metadata fields are present.

        Args:
            package: The generated package content

        Returns:
            Completeness score (0.0 to 1.0)
        """
        required_fields = [
            '**Type**:', '**File**:', '**Module**:', '**Line**:',
            '**Cluster**:', '**Criticality**:', '**Change Risk**:',
            '**Relevance Score**:', '**Semantic Rationale**:'
        ]

        entities = self.extract_entities(package)
        if not entities:
            return 0.0

        completeness_scores = []
        for entity in entities:
            present_fields = sum(1 for field in required_fields if field in entity)
            completeness_scores.append(present_fields / len(required_fields))

        return sum(completeness_scores) / len(completeness_scores)

    def check_type_icons(self, package: str) -> float:
        """
        Verify type-specific icons are present.

        Args:
            package: The generated package content

        Returns:
            Icon coverage score (0.0 to 1.0)
        """
        type_icons = ['🏛️', '⚙️', '🔧', '📊', '🔒', '🏷️', '📄', '🔍', '🎯', '🌐']

        entities = self.extract_entities(package)
        if not entities:
            return 0.0

        entities_with_icons = sum(
            1 for entity in entities
            if any(icon in entity for icon in type_icons)
        )

        return entities_with_icons / len(entities)

    def check_strategy_indicators(self, package: str, expected_strategy: str) -> float:
        """
        Check if package content aligns with expected strategy.

        Args:
            package: The generated package content
            expected_strategy: Expected strategy selection

        Returns:
            Strategy alignment score (0.0 to 1.0)
        """
        strategy_indicators = {
            'ARCHITECTURE_OVERVIEW': ['cluster', 'system', 'architecture', 'main', 'core', 'overview'],
            'WORKFLOW_FOCUSED': ['process', 'workflow', 'handle', 'execute', 'flow', 'step'],
            'CLUSTER_DEEP_DIVE': ['class', 'method', 'implementation', 'detail', 'function'],
            'CROSS_CUTTING': ['utility', 'helper', 'shared', 'common', 'infrastructure', 'global']
        }

        indicators = strategy_indicators.get(expected_strategy, [])
        if not indicators:
            return 0.5  # Neutral score for unknown strategies

        package_lower = package.lower()
        matches = sum(1 for indicator in indicators if indicator in package_lower)

        return min(matches / len(indicators), 1.0)

    def count_entities(self, package: str) -> int:
        """
        Count the number of entities in the package.

        Args:
            package: The generated package content

        Returns:
            Number of entities found
        """
        entities = self.extract_entities(package)
        return len(entities)

    def assess_readability(self, package: str) -> float:
        """
        Assess the readability and formatting of the package.

        Args:
            package: The generated package content

        Returns:
            Readability score (0.0 to 1.0)
        """
        if not package:
            return 0.0

        score = 0.0
        max_score = 5.0

        # Check for proper markdown formatting
        if '##' in package:
            score += 1.0

        # Check for consistent bullet points
        if '- **' in package or '* **' in package:
            score += 1.0

        # Check for code blocks
        if '```' in package:
            score += 1.0

        # Check for proper line breaks and spacing
        lines = package.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        if len(non_empty_lines) > 10:  # Reasonable content length
            score += 1.0

        # Check for structured sections
        if 'Selected Components' in package or 'KEY IMPLEMENTATIONS' in package:
            score += 1.0

        return score / max_score

    def check_architectural_context(self, package: str) -> float:
        """
        Check if architectural context is properly provided.

        Args:
            package: The generated package content

        Returns:
            Architectural context score (0.0 to 1.0)
        """
        if not package:
            return 0.0

        score = 0.0
        max_score = 4.0

        # Check for cluster information
        if '**Cluster**:' in package:
            score += 1.0

        # Check for dependency information
        if '**Calls**:' in package or '**Used By**:' in package:
            score += 1.0

        # Check for architectural rationale
        if '**Semantic Rationale**:' in package or 'Architectural Context' in package:
            score += 1.0

        # Check for system-level understanding
        if any(term in package.lower() for term in ['system', 'architecture', 'component', 'module']):
            score += 1.0

        return score / max_score

    def extract_entities(self, package: str) -> List[str]:
        """
        Extract individual entities from the package.

        Args:
            package: The generated package content

        Returns:
            List of entity sections
        """
        if not package:
            return []

        # Split by entity headers (numbered sections like "### 1. ⚙️ function_name")
        entity_pattern = r'###\s+\d+\.\s+[^\n]+'
        entities = re.split(entity_pattern, package)

        # Filter out empty sections and the header
        entities = [entity.strip() for entity in entities if entity.strip()]

        # If no numbered entities found, try alternative patterns
        if len(entities) <= 1:
            # Try splitting by markdown headers
            entities = package.split('##')
            entities = [entity.strip() for entity in entities if entity.strip()]

        return entities


    def run_comprehensive_test(self, save_results: bool = True,
                              save_packages: bool = False) -> Dict[str, Any]:
        """
        Run the comprehensive test suite with all 50 queries.

        Args:
            save_results: Whether to save detailed results to file
            save_packages: Whether to save individual packages to files

        Returns:
            Dictionary containing test results and analysis
        """
        from test_queries import TestQueries
        from test_results_analyzer import TestResultsAnalyzer

        print("🚀 Starting Enhanced Metadata System Comprehensive Test")
        print("=" * 70)

        # Load all test queries
        all_queries = TestQueries.get_all_queries()
        print(f"📋 Loaded {len(all_queries)} test queries across 6 categories")

        # Display category summary
        category_summary = TestQueries.get_category_summary()
        print("\n📊 Test Query Distribution:")
        for category, count in category_summary.items():
            print(f"   • {category}: {count} queries")
        print()

        # Start timing
        self.start_time = time.time()

        # Execute all queries
        print("🔍 Executing Test Queries...")
        print("-" * 50)

        for query in all_queries:
            result = self.execute_test_query(
                query.id, query.query, query.expected_strategy, query.category
            )
            self.results.append(result)

            # Save individual package if requested
            if save_packages and result.success and result.package:
                package_filename = f"test_package_{query.id:02d}_{query.category.replace(' ', '_').lower()}.txt"
                with open(package_filename, 'w', encoding='utf-8') as f:
                    f.write(f"# Test Query {query.id}: {query.category}\n")
                    f.write(f"**Query**: {query.query}\n")
                    f.write(f"**Expected Strategy**: {query.expected_strategy}\n")
                    f.write(f"**Processing Time**: {result.processing_time:.2f}s\n\n")
                    f.write("---\n\n")
                    f.write(result.package)

            print()  # Add spacing between queries

        # End timing
        self.end_time = time.time()
        total_time = self.end_time - self.start_time

        print("=" * 70)
        print(f"✅ Comprehensive Test Completed in {total_time:.1f} seconds")
        print()

        # Analyze results
        analyzer = TestResultsAnalyzer(self.results)
        summary_metrics = analyzer.generate_summary_metrics()
        category_breakdown = analyzer.generate_category_breakdown()
        issues = analyzer.identify_performance_issues()
        recommendations = analyzer.generate_recommendations()

        # Display summary
        self.display_test_summary(summary_metrics, category_breakdown, issues, recommendations)

        # Save detailed results if requested
        if save_results:
            results_file = analyzer.save_detailed_results()
            print(f"💾 Detailed results saved to: {results_file}")

        return {
            'summary_metrics': summary_metrics,
            'category_breakdown': category_breakdown,
            'issues': issues,
            'recommendations': recommendations,
            'total_time': total_time,
            'results_count': len(self.results)
        }

    def display_test_summary(self, summary: Dict[str, Any],
                           categories: Dict[str, Dict[str, Any]],
                           issues: List[Dict[str, Any]],
                           recommendations: List[str]) -> None:
        """
        Display a comprehensive test summary.

        Args:
            summary: Summary metrics
            categories: Category breakdown
            issues: Identified issues
            recommendations: Improvement recommendations
        """
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 70)

        # Overall metrics
        print(f"🎯 Overall Success Rate: {summary['success_rate']:.1%}")
        print(f"⏱️  Average Processing Time: {summary['average_processing_time']:.2f}s")
        print(f"📋 Metadata Completeness: {summary['metadata_completeness_avg']:.1%}")
        print(f"🎨 Type Icon Coverage: {summary['type_icons_coverage']:.1%}")
        print(f"🎯 Strategy Alignment: {summary['strategy_alignment_avg']:.1%}")
        print(f"⭐ Overall Quality Score: {summary['overall_quality_avg']:.1%}")
        print()

        # Category performance
        print("📈 CATEGORY PERFORMANCE")
        print("-" * 50)
        print(f"{'Category':<25} {'Queries':<8} {'Success':<8} {'Avg Time':<10} {'Quality':<8}")
        print("-" * 50)

        for category, metrics in categories.items():
            category_short = category[:24]
            print(f"{category_short:<25} "
                  f"{metrics['total_queries']:<8} "
                  f"{metrics['success_rate']:.1%}    "
                  f"{metrics['average_processing_time']:.2f}s     "
                  f"{metrics['average_quality_score']:.1%}")
        print()

        # Issues
        if issues:
            print("⚠️  IDENTIFIED ISSUES")
            print("-" * 50)
            for issue in issues:
                severity_icon = {
                    'critical': '🚨',
                    'high': '⚠️',
                    'medium': '⚡',
                    'low': 'ℹ️'
                }.get(issue['severity'], '❓')

                print(f"{severity_icon} {issue['description']}")
                print(f"   Type: {issue['type']} | Severity: {issue['severity']}")
                print(f"   Affected queries: {len(issue['affected_queries'])}")
                if 'details' in issue:
                    print(f"   Details: {issue['details']}")
                print()
        else:
            print("✅ No significant issues identified")
            print()

        # Recommendations
        if recommendations:
            print("💡 RECOMMENDATIONS")
            print("-" * 50)
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
            print()
        else:
            print("🎉 No recommendations - system performing excellently!")
            print()

        # Success criteria validation
        print("✅ SUCCESS CRITERIA VALIDATION")
        print("-" * 50)
        criteria = [
            ("Overall Success Rate ≥90%", summary['success_rate'] >= 0.9),
            ("Metadata Completeness ≥95%", summary['metadata_completeness_avg'] >= 0.95),
            ("Processing Performance ≤15s avg", summary['average_processing_time'] <= 15.0),
            ("Strategy Alignment ≥70%", summary['strategy_alignment_avg'] >= 0.7),
            ("Quality Score ≥75%", summary['overall_quality_avg'] >= 0.75)
        ]

        for criterion, passed in criteria:
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"{criterion:<35} {status}")

        passed_criteria = sum(1 for _, passed in criteria if passed)
        print(f"\nCriteria Met: {passed_criteria}/{len(criteria)} ({passed_criteria/len(criteria):.1%})")


if __name__ == "__main__":
    import sys

    # Parse command line arguments
    save_results = "--no-save" not in sys.argv
    save_packages = "--save-packages" in sys.argv
    project_path = "."

    # Check for custom project path
    for arg in sys.argv:
        if arg.startswith("--project="):
            project_path = arg.split("=", 1)[1]

    print("🧪 Enhanced Metadata System - Comprehensive Testing")
    print("=" * 70)
    print(f"📁 Project Path: {project_path}")
    print(f"💾 Save Results: {save_results}")
    print(f"📦 Save Packages: {save_packages}")
    print()

    try:
        # Initialize and run comprehensive test
        runner = EnhancedMetadataTestRunner(project_path)
        results = runner.run_comprehensive_test(
            save_results=save_results,
            save_packages=save_packages
        )

        print("🎉 Comprehensive testing completed successfully!")
        print(f"📊 Processed {results['results_count']} queries in {results['total_time']:.1f} seconds")

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
