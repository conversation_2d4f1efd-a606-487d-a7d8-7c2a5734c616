#!/usr/bin/env python3
"""
Context Selection Fix for Enhanced Metadata System

This script identifies and fixes the critical issue where the Enhanced Metadata System
is selecting irrelevant entities (like argument parsing functions) instead of the
actual relevant methods (like apply_edits, send_message, etc.).
"""

import json
import time
from datetime import datetime


def analyze_ir_data_issue():
    """
    Analyze the IR data to understand why relevant entities are not being selected.
    """
    print("🔍 Analyzing IR Data Selection Issue")
    print("=" * 60)
    
    # Load the most recent IR data
    try:
        with open("aider-main/ir_data_20250601_220538.json", "r") as f:
            ir_data = json.load(f)
        print(f"✅ Loaded IR data with {len(ir_data.get('modules', []))} modules")
    except FileNotFoundError:
        print("❌ IR data file not found")
        return
    
    # Search for the missing methods
    target_methods = [
        "apply_edits", "prepare_to_edit", "dirty_commit", "send_message", 
        "send", "get_files_content", "apply_updates"
    ]
    
    found_methods = {}
    
    print("\n🔍 Searching for target methods in IR data...")
    for module in ir_data.get('modules', []):
        module_name = module.get('name', 'unknown')
        
        for entity in module.get('entities', []):
            entity_name = entity.get('name', '')
            entity_type = entity.get('type', '')
            
            if entity_name in target_methods:
                found_methods[entity_name] = {
                    'module': module_name,
                    'type': entity_type,
                    'file': entity.get('file', ''),
                    'criticality': entity.get('criticality_score', 0),
                    'relevance_keywords': entity.get('relevance_keywords', [])
                }
                print(f"   ✅ Found {entity_name} in {module_name}")
    
    print(f"\n📊 Found {len(found_methods)}/{len(target_methods)} target methods")
    
    # Check what's being selected instead
    print("\n🔍 Analyzing what gets selected instead...")
    
    # Look for frequently selected but irrelevant entities
    common_selections = ["get_parser", "get_md_help", "main", "__init__"]
    
    for module in ir_data.get('modules', []):
        for entity in module.get('entities', []):
            entity_name = entity.get('name', '')
            if entity_name in common_selections:
                print(f"   ⚠️  Common selection: {entity_name} in {module.get('name', '')}")
                print(f"      Criticality: {entity.get('criticality_score', 0)}")
                print(f"      Keywords: {entity.get('relevance_keywords', [])}")
    
    return found_methods


def test_direct_entity_selection():
    """
    Test if we can directly select the correct entities by bypassing the selection algorithm.
    """
    print("\n🧪 Testing Direct Entity Selection")
    print("=" * 60)
    
    from enhanced_metadata_comprehensive_test import EnhancedMetadataTestRunner
    
    # Initialize test runner
    runner = EnhancedMetadataTestRunner("aider-main")
    
    # Create a test request that should find apply_edits
    from aider.context_request.context_request_handler import IRContextRequest
    
    request = IRContextRequest(
        user_query="apply_edits method in Coder class",
        task_description="Direct test for apply_edits method",
        task_type="analysis",
        max_entities=8,
        llm_friendly=True,
        focus_entities=["apply_edits", "prepare_to_edit", "dirty_commit"]  # Direct targeting
    )
    
    print("🔍 Testing direct entity selection...")
    start_time = time.time()
    
    try:
        result = runner.handler.process_ir_context_request(request)
        processing_time = time.time() - start_time
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        package = result.get('llm_friendly_package', '')
        print(f"✅ Generated package: {len(package):,} characters")
        print(f"⏱️  Processing time: {processing_time:.2f}s")
        
        # Check if the package contains the target methods
        target_methods = ["apply_edits", "prepare_to_edit", "dirty_commit"]
        found_targets = []
        
        for method in target_methods:
            if method in package:
                found_targets.append(method)
                print(f"   ✅ Found: {method}")
            else:
                print(f"   ❌ Missing: {method}")
        
        success_rate = len(found_targets) / len(target_methods)
        print(f"\n📊 Direct selection success rate: {success_rate:.1%}")
        
        return success_rate > 0.5
        
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False


def identify_selection_algorithm_issue():
    """
    Identify the specific issue in the selection algorithm.
    """
    print("\n🔍 Identifying Selection Algorithm Issue")
    print("=" * 60)
    
    # The issue is likely in one of these areas:
    issues = [
        {
            "area": "Query Intent Classification",
            "description": "System misclassifies 'file editing' queries",
            "likely_cause": "Semantic analysis maps to wrong intent"
        },
        {
            "area": "Entity Relevance Scoring", 
            "description": "apply_edits gets lower score than get_parser",
            "likely_cause": "Relevance scoring algorithm prioritizes wrong features"
        },
        {
            "area": "Cluster Selection",
            "description": "System selects 'config' cluster instead of 'core' cluster",
            "likely_cause": "Cluster mapping or selection logic is wrong"
        },
        {
            "area": "Strategy Selection",
            "description": "Wrong strategy selected for file editing queries",
            "likely_cause": "Strategy mapping doesn't match query intent"
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"{i}. {issue['area']}")
        print(f"   Problem: {issue['description']}")
        print(f"   Likely Cause: {issue['likely_cause']}")
        print()
    
    return issues


def propose_fixes():
    """
    Propose specific fixes for the context selection issues.
    """
    print("💡 PROPOSED FIXES")
    print("=" * 60)
    
    fixes = [
        {
            "priority": "CRITICAL",
            "fix": "Enhance Query-to-Method Mapping",
            "description": "Create explicit mappings for common queries to expected methods",
            "implementation": "Add query pattern matching before semantic analysis"
        },
        {
            "priority": "HIGH", 
            "fix": "Fix Relevance Scoring",
            "description": "Boost scores for methods that match query keywords",
            "implementation": "Add keyword-based relevance boosting in scoring algorithm"
        },
        {
            "priority": "HIGH",
            "fix": "Improve Cluster Selection",
            "description": "Ensure file editing queries select 'core' cluster, not 'config'",
            "implementation": "Fix cluster classification logic"
        },
        {
            "priority": "MEDIUM",
            "fix": "Add Method-Specific Boosting",
            "description": "Boost core Aider methods like apply_edits, send_message",
            "implementation": "Add hardcoded relevance boost for critical methods"
        }
    ]
    
    for fix in fixes:
        print(f"🔧 {fix['priority']}: {fix['fix']}")
        print(f"   Description: {fix['description']}")
        print(f"   Implementation: {fix['implementation']}")
        print()
    
    return fixes


def main():
    """
    Main analysis and fix identification function.
    """
    print("🚨 ENHANCED METADATA SYSTEM - CRITICAL ISSUE ANALYSIS")
    print("=" * 70)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Step 1: Analyze IR data
    found_methods = analyze_ir_data_issue()
    
    # Step 2: Test direct selection
    direct_success = test_direct_entity_selection()
    
    # Step 3: Identify algorithm issues
    issues = identify_selection_algorithm_issue()
    
    # Step 4: Propose fixes
    fixes = propose_fixes()
    
    # Summary
    print("📋 ANALYSIS SUMMARY")
    print("=" * 60)
    print(f"✅ Target methods found in IR: {len(found_methods) if found_methods else 0}")
    print(f"{'✅' if direct_success else '❌'} Direct selection test: {'PASSED' if direct_success else 'FAILED'}")
    print(f"🔍 Identified {len(issues)} potential algorithm issues")
    print(f"💡 Proposed {len(fixes)} fixes")
    
    print("\n🎯 CONCLUSION:")
    if found_methods and len(found_methods) > 3:
        print("✅ The IR data CONTAINS the correct methods")
        print("❌ The SELECTION ALGORITHM is the problem")
        print("🔧 Need to fix context selection logic, not IR generation")
    else:
        print("❌ The IR data is MISSING the correct methods")
        print("🔧 Need to fix IR generation first")
    
    print("\n🚀 NEXT STEPS:")
    print("1. Implement query-to-method mapping")
    print("2. Fix relevance scoring algorithm") 
    print("3. Test with corrected selection logic")
    print("4. Re-run real-world Aider tests")


if __name__ == "__main__":
    main()
