#!/usr/bin/env python3
"""
Critical Fix for Enhanced Metadata System Context Selection

This script implements the critical fixes needed to make the Enhanced Metadata System
select the correct entities (like apply_edits, send_message) instead of irrelevant ones
(like get_parser, get_md_help).
"""

import re
from typing import Dict, List, Any


def create_enhanced_entity_scoring():
    """
    Create enhanced entity scoring logic that prioritizes relevant methods.
    """
    
    # Critical Aider methods that should be prioritized
    CRITICAL_AIDER_METHODS = {
        'apply_edits': 10.0,
        'prepare_to_edit': 9.0,
        'dirty_commit': 8.0,
        'send_message': 10.0,
        'send': 9.0,
        'run': 9.0,
        'run_one': 8.0,
        'get_files_content': 8.0,
        'apply_updates': 8.0,
        'get_input': 7.0,
        'auto_commit': 7.0,
        'format_messages': 7.0,
        'show_send_output': 7.0
    }
    
    # Query-to-method mappings for explicit targeting
    QUERY_METHOD_MAPPINGS = {
        'file editing': ['apply_edits', 'prepare_to_edit', 'dirty_commit', 'apply_updates'],
        'edit': ['apply_edits', 'prepare_to_edit', 'dirty_commit'],
        'apply': ['apply_edits', 'apply_updates', 'prepare_to_edit'],
        'chat loop': ['run', 'run_one', 'send', 'get_input'],
        'send message': ['send_message', 'send', 'format_messages'],
        'llm': ['send_message', 'send', 'format_messages', 'show_send_output'],
        'git': ['dirty_commit', 'auto_commit', 'repo'],
        'context': ['get_files_content', 'get_repo_map', 'RepoMap'],
        'architecture': ['Coder', 'Model', 'InputOutput', 'GitRepo']
    }
    
    # Methods to PENALIZE (they're selected too often but are not core functionality)
    PENALIZED_METHODS = {
        'get_parser': -5.0,
        'get_md_help': -5.0,
        '__init__': -3.0,
        'main': -2.0,
        'create': -1.0  # Unless specifically requested
    }
    
    def enhanced_score_entity(entity: Dict[str, Any], query: str, focus_entities: List[str] = None) -> float:
        """
        Enhanced entity scoring that prioritizes relevant Aider methods.
        
        Args:
            entity: Entity to score
            query: User query
            focus_entities: Entities to focus on
            
        Returns:
            Enhanced relevance score
        """
        entity_name = entity.get('name', '').lower()
        query_lower = query.lower()
        
        # Base score
        score = 0.0
        
        # 1. CRITICAL: Focus entities get massive boost
        if focus_entities:
            for focus_entity in focus_entities:
                if focus_entity.lower() in entity_name:
                    score += 20.0  # Massive boost for focus entities
                    print(f"   🎯 FOCUS BOOST: {entity_name} +20.0")
        
        # 2. CRITICAL: Aider method priority boost
        if entity_name in CRITICAL_AIDER_METHODS:
            boost = CRITICAL_AIDER_METHODS[entity_name]
            score += boost
            print(f"   🚀 AIDER METHOD BOOST: {entity_name} +{boost}")
        
        # 3. CRITICAL: Query-method mapping boost
        for query_pattern, methods in QUERY_METHOD_MAPPINGS.items():
            if query_pattern in query_lower:
                for method in methods:
                    if method.lower() in entity_name:
                        score += 15.0
                        print(f"   🎯 QUERY MAPPING BOOST: {entity_name} +15.0 (pattern: {query_pattern})")
        
        # 4. PENALTY: Penalize over-selected irrelevant methods
        if entity_name in PENALIZED_METHODS:
            penalty = PENALIZED_METHODS[entity_name]
            score += penalty
            print(f"   ❌ PENALTY: {entity_name} {penalty}")
        
        # 5. Keyword matching boost
        query_keywords = re.findall(r'\b\w+\b', query_lower)
        for keyword in query_keywords:
            if keyword in entity_name:
                score += 5.0
                print(f"   📝 KEYWORD MATCH: {entity_name} +5.0 ({keyword})")
        
        # 6. Type-based scoring
        entity_type = entity.get('type', '').lower()
        if entity_type == 'class':
            score += 2.0  # Classes are generally important
        elif entity_type == 'method' or entity_type == 'function':
            score += 1.0  # Methods are important
        
        # 7. Criticality from IR data
        criticality = entity.get('criticality_score', 0)
        score += criticality * 0.1  # Small boost from IR criticality
        
        print(f"   📊 FINAL SCORE: {entity_name} = {score:.2f}")
        return score
    
    return enhanced_score_entity


def create_query_intent_fix():
    """
    Create improved query intent classification.
    """
    
    INTENT_PATTERNS = {
        'file_editing': [
            r'\b(apply|edit|modify|change|update|file)\b',
            r'\b(edits?|editing|modification)\b',
            r'\b(prepare.*edit|dirty.*commit)\b'
        ],
        'chat_workflow': [
            r'\b(chat|loop|conversation|run|send)\b',
            r'\b(message|input|output)\b',
            r'\b(workflow|process|flow)\b'
        ],
        'llm_integration': [
            r'\b(llm|model|api|send|message)\b',
            r'\b(completion|response|generate)\b',
            r'\b(openai|anthropic|claude|gpt)\b'
        ],
        'git_operations': [
            r'\b(git|commit|repository|repo)\b',
            r'\b(track|version|branch)\b'
        ],
        'context_system': [
            r'\b(context|repo.*map|gather|extract)\b',
            r'\b(files?.*content|symbols?)\b'
        ]
    }
    
    def classify_query_intent(query: str) -> str:
        """
        Classify query intent with improved patterns.
        
        Args:
            query: User query
            
        Returns:
            Classified intent
        """
        query_lower = query.lower()
        intent_scores = {}
        
        for intent, patterns in INTENT_PATTERNS.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    score += 1
            intent_scores[intent] = score
        
        # Return highest scoring intent
        if intent_scores:
            best_intent = max(intent_scores, key=intent_scores.get)
            if intent_scores[best_intent] > 0:
                return best_intent
        
        return 'component_discovery'  # Default
    
    return classify_query_intent


def create_selection_strategy_fix():
    """
    Create improved selection strategy logic.
    """
    
    def determine_selection_strategy(query: str, focus_entities: List[str] = None) -> str:
        """
        Determine the best selection strategy based on query and focus.
        
        Args:
            query: User query
            focus_entities: Focus entities
            
        Returns:
            Selection strategy
        """
        query_lower = query.lower()
        
        # If focus entities provided, use cluster deep dive
        if focus_entities and len(focus_entities) <= 3:
            return 'cluster_deep_dive'
        
        # File editing queries should use workflow focused
        if any(term in query_lower for term in ['edit', 'apply', 'modify', 'file']):
            return 'workflow_focused'
        
        # Chat/workflow queries
        if any(term in query_lower for term in ['chat', 'loop', 'run', 'send', 'message']):
            return 'workflow_focused'
        
        # Architecture queries
        if any(term in query_lower for term in ['architecture', 'overall', 'system', 'structure']):
            return 'architecture_overview'
        
        # Default to cluster deep dive for specific queries
        return 'cluster_deep_dive'
    
    return determine_selection_strategy


def main():
    """
    Main function to demonstrate the fixes.
    """
    print("🔧 Enhanced Metadata System - Selection Algorithm Fixes")
    print("=" * 60)
    
    # Create enhanced scoring function
    enhanced_scorer = create_enhanced_entity_scoring()
    
    # Create improved intent classifier
    intent_classifier = create_query_intent_fix()
    
    # Create improved strategy selector
    strategy_selector = create_selection_strategy_fix()
    
    # Test cases
    test_cases = [
        {
            'query': 'How does Aider apply edits to files?',
            'focus_entities': ['apply_edits', 'prepare_to_edit'],
            'expected_methods': ['apply_edits', 'prepare_to_edit', 'dirty_commit']
        },
        {
            'query': 'How does the chat loop work?',
            'focus_entities': ['run', 'send'],
            'expected_methods': ['run', 'run_one', 'send', 'get_input']
        },
        {
            'query': 'How does Aider send messages to LLM?',
            'focus_entities': ['send_message'],
            'expected_methods': ['send_message', 'send', 'format_messages']
        }
    ]
    
    print("\n🧪 Testing Enhanced Scoring Logic")
    print("-" * 40)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['query']}")
        print(f"Focus: {test_case['focus_entities']}")
        
        # Test intent classification
        intent = intent_classifier(test_case['query'])
        print(f"Intent: {intent}")
        
        # Test strategy selection
        strategy = strategy_selector(test_case['query'], test_case['focus_entities'])
        print(f"Strategy: {strategy}")
        
        # Test entity scoring
        print("Entity Scores:")
        for method in test_case['expected_methods']:
            entity = {'name': method, 'type': 'method', 'criticality_score': 0.5}
            score = enhanced_scorer(entity, test_case['query'], test_case['focus_entities'])
            print(f"  {method}: {score:.2f}")
        
        # Test penalty for irrelevant methods
        irrelevant_entity = {'name': 'get_parser', 'type': 'function', 'criticality_score': 0.3}
        penalty_score = enhanced_scorer(irrelevant_entity, test_case['query'], test_case['focus_entities'])
        print(f"  get_parser (penalty): {penalty_score:.2f}")
    
    print("\n✅ Enhanced scoring logic created successfully!")
    print("\n🚀 Next Steps:")
    print("1. Apply these fixes to hierarchical_context_selector.py")
    print("2. Update the _score_entities_for_query method")
    print("3. Re-run the real-world Aider tests")
    print("4. Verify improved relevance scores")


if __name__ == "__main__":
    main()
