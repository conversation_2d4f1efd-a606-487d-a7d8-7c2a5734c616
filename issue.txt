You're right to focus on the logic, especially concerning how the IR data is leveraged. The `HierarchicalContextSelector` has a lot of sophisticated components, but it seems like some key parts might be disconnected or not fully utilized, leading to suboptimal context selection.

Let's break down potential areas for logical improvement, focusing on your observation that "the ir json is amazing but we cant use it properly."

**Key Observations and Potential Issues:**

1.  **The "NUCLEAR OPTION" Path Seems Incomplete/Problematic:**
    In `_select_entities_from_clusters`, you have a "NUCLEAR OPTION" path:
    ```python
    # Extract key terms from query
    query_terms = self._extract_query_terms(user_query) # UNDEFINED in this file
    print(f"   🔍 Query terms: {query_terms}")

    # Find entities that directly match query terms
    relevant_entities = self._find_directly_relevant_entities(all_candidate_entities, query_terms, ir_data) # UNDEFINED in this file
    print(f"   ✅ Found {len(relevant_entities)} directly relevant entities")

    if relevant_entities:
        all_scored_entities = relevant_entities[:max_entities]
    else:
        # Fallback
        all_scored_entities = self._score_entities_for_query(...)
    ```
    *   **Missing Implementations:** The methods `_extract_query_terms` and `_find_directly_relevant_entities` are not defined within the provided code.
        *   If this code is run as-is, it would raise an `AttributeError`.
        *   If they are defined elsewhere in your project, their effectiveness is crucial. If this "nuclear option" is underperforming or returning empty `relevant_entities` too often, you'll always fall back to `_score_entities_for_query`.
    *   **Intended Logic:** This path seems designed for high-precision hits. If it's not working well, the overall selection quality will suffer.

2.  **Significant Amount of Unused Semantic Logic:**
    You have a suite of promising-looking methods for deeper semantic analysis that are **defined but not called** by the main scoring function (`_semantic_entity_intelligence`) or its sub-components:
    *   `_compute_semantic_relevance(self, user_query: str, entity_name: str, entity_type: str, ir_data: dict)`: This looks particularly valuable as it tries to match query concepts with entity domains derived from IR.
    *   `_analyze_contextual_importance(self, entity_name: str, entity_type: str, ir_data: dict)`: Uses IR data for complexity, side effects, and criticality.
    *   `_extract_query_concepts(self, query: str)`
    *   `_extract_entity_domain(self, entity_name: str, ir_data: dict)`
    *   `_analyze_query_intent(self, query: str)` (the second version, different from the one used in strategy selection)
    *   `_analyze_entity_capability(self, entity_name: str, entity_type: str, ir_data: dict)`
    *   `_analyze_query_intent_semantics(self, query: str, entity_name: str, entity_type: str)`
    *   `_analyze_file_context(self, file_path: str, entity_name: str)`

    **This is likely the core reason you feel the "IR JSON is not being used properly."** These unused functions seem designed to leverage the IR more deeply than the currently active scoring path.

3.  **Overemphasis on Direct Name Matching in Active Scoring:**
    The current primary scoring function, `_semantic_entity_intelligence`, heavily weights direct relevance:
    ```python
    total_score = (
        direct_relevance * 0.8 +      # 80% - find what user actually asked for
        relationship_boost * 0.15 +   # 15% - related entities (uses IR)
        architectural_significance * 0.03 +  # 3% - barely matters (uses IR)
        contextual_coherence * 0.02   # 2% - barely matters
    )
    ```
    While direct matches are important, an 80% weight on `_calculate_direct_relevance` (which itself is largely string-matching based) can overshadow valuable signals from relationships (`relationship_boost`) and architectural properties (`architectural_significance`) derived from the IR. If a user's query doesn't use exact entity names, relevant items might get low scores.

4.  **`_calculate_relationship_boost` Could Be Stronger:**
    This function *does* use the IR to find connections (class methods, function calls, module coherence). However, it primarily boosts based on whether the *names* of these related entities match `query_keywords`. A more powerful approach would be to assess the *semantic relevance* of these related entities to the query, not just their names.

**Logical Steps for Improvement:**

Here's a suggested path to enhance the logic and better utilize your IR data:

**Step 1: Address the "NUCLEAR OPTION" Path**

*   **Clarify `_extract_query_terms`:**
    *   You already have `_extract_intelligent_keywords`. Consider if this can be used directly:
        ```python
        query_terms = self._extract_intelligent_keywords(user_query)
        ```
*   **Implement or Refine `_find_directly_relevant_entities`:** This method should return a *scored and sorted* list of entities if it's to be used as `relevant_entities[:max_entities]`.
    *   **Option A (Simple but Focused):** Use a stripped-down, high-precision version of your existing scoring. For example, only consider entities that have very strong direct name matches or specific structural roles.
    *   **Option B (Leverage Unused Logic):** This is a great place to start integrating! For instance, `_compute_semantic_relevance` could be the core of `_find_directly_relevant_entities`:
        ```python
        def _find_directly_relevant_entities(self, candidate_entities: List[Dict[str, Any]],
                                             query_terms: List[str], # query_terms might not be needed if _compute_semantic_relevance uses full query
                                             ir_data: Dict[str, Any],
                                             user_query: str) -> List[Dict[str, Any]]: # Pass full user_query
            scored_entities = []
            for entity in candidate_entities:
                entity_name = entity.get('name', '')
                entity_type = entity.get('type', '')
                # Use one of your powerful unused functions
                score = self._compute_semantic_relevance(user_query, entity_name, entity_type, ir_data)
                
                # Potentially add a boost for exact keyword matches from query_terms if desired
                # ...

                if score > SOME_THRESHOLD: # Only consider highly relevant items for the "nuclear" path
                    entity['relevance_score'] = score
                    scored_entities.append(entity)
            
            scored_entities.sort(key=lambda e: e.get('relevance_score', 0.0), reverse=True)
            return scored_entities
        ```
        Then, in `_select_entities_from_clusters`:
        ```python
        # ...
        query_keywords = self._extract_intelligent_keywords(user_query) # If needed by _find_directly_relevant_entities
        relevant_entities = self._find_directly_relevant_entities(
            all_candidate_entities, query_keywords, ir_data, user_query # Pass user_query
        )
        # ...
        ```

**Step 2: Integrate Unused Semantic Functions into the Main Scoring Logic**

If the "nuclear option" is meant to be a simple first pass, then the fallback `_score_entities_for_query` (and thus `_semantic_entity_intelligence`) must be robust. This is where the unused functions can shine.

*   **Modify `_semantic_entity_intelligence`:**
    ```python
    def _semantic_entity_intelligence(self, entity_name: str, entity_type: str,
                                      user_query: str, entity_file: str = "", ir_data: dict = None) -> float:
        if ir_data is None: # Ensure ir_data is available
            ir_data = getattr(self, '_current_ir_data', {})

        # 1. Direct Name & Keyword Relevance (Keep, but perhaps with adjusted internal scoring)
        direct_relevance_score = self._calculate_direct_relevance(entity_name, entity_type, entity_file, 
                                                                 self._extract_intelligent_keywords(user_query), # Ensure keywords are passed
                                                                 ir_data)

        # 2. Relationship-Based Relevance (Keep and enhance)
        relationship_score = self._calculate_relationship_boost(entity_name, entity_type, 
                                                               self._extract_intelligent_keywords(user_query), # Ensure keywords are passed
                                                               ir_data)

        # 3. Deeper Semantic Relevance (NEW - Integrate _compute_semantic_relevance)
        # This uses query concepts vs entity domain from IR
        semantic_match_score = self._compute_semantic_relevance(user_query, entity_name, entity_type, ir_data)

        # 4. Contextual & Architectural Importance (NEW - Integrate _analyze_contextual_importance)
        # This uses complexity, side-effects, IR criticality
        contextual_importance_score = self._analyze_contextual_importance(entity_name, entity_type, ir_data)
        
        # Add architectural significance from existing function, if still distinct
        architectural_significance_score = self._calculate_architectural_significance(entity_name, entity_type, entity_file, ir_data)

        # Consider _analyze_query_intent_semantics and _analyze_file_context for additional small boosts
        intent_semantics_boost = self._analyze_query_intent_semantics(user_query, entity_name, entity_type) # If deemed useful
        file_context_boost = self._analyze_file_context(entity_file, entity_name) # If deemed useful


        # Rebalance Weights (Example - NEEDS TUNING)
        # These weights should be determined through experimentation.
        # The goal is to give IR-driven semantic understanding more influence.
        total_score = (
            direct_relevance_score * 0.30 +       # Reduced weight
            relationship_score * 0.25 +           # Increased weight
            semantic_match_score * 0.25 +         # New significant component
            contextual_importance_score * 0.10 +  # New component
            architectural_significance_score * 0.05 + 
            intent_semantics_boost * 0.025 +      # Small influence
            file_context_boost * 0.025            # Small influence
        )
        
        # Log individual score components for easier debugging
        print(f"   🧠 {entity_name}: direct={direct_relevance_score:.1f} rel={relationship_score:.1f} sem_match={semantic_match_score:.1f} ctx_imp={contextual_importance_score:.1f} arch_sig={architectural_significance_score:.1f} intent_sem={intent_semantics_boost:.1f} file_ctx={file_context_boost:.1f} TOTAL = {total_score:.1f}")

        return max(total_score, 0.0)
    ```

**Step 3: Re-evaluate and Tune Weights**

The example weights above are illustrative. The key is to reduce the dominance of `direct_relevance` and give more agency to scores derived from deeper IR analysis (relationships, semantic concept matching, contextual importance signals like complexity and side-effects). This requires experimentation with representative queries.

**Step 4: Enhance `_calculate_relationship_boost` (Optional Advanced)**

Currently, `_calculate_relationship_boost` checks if *names* of related entities match query keywords. To make it more robust:
*   When you find a related entity (e.g., a method in a class, a function called by another), instead of just checking its name against keywords, you could recursively (or with a depth limit) calculate a simplified semantic score for *that related entity*.
*   The boost an entity gets from its relationships would then depend on how semantically relevant its *neighbors* are to the query, not just on name similarity.

**Step 5: Consolidate and Clean Up**

*   Once the unused functions are integrated, review if any parts of `_calculate_direct_relevance`, `_calculate_relationship_boost`, `_calculate_architectural_significance`, or `_calculate_contextual_coherence` become redundant.
*   Ensure that functions like `_extract_intelligent_keywords` are called consistently and their results passed where needed, rather than being called multiple times with the same query.

**Why these changes are "logical":**

*   **Fuller IR Utilization:** They explicitly bring in your IR-centric analysis functions (`_compute_semantic_relevance`, `_analyze_contextual_importance`) into the scoring pipeline.
*   **Balanced Scoring:** They shift the balance from purely name-based matching towards a more holistic view that includes semantic relationships, structural importance, and conceptual overlap, all potentially derived from the IR.
*   **Robustness to Query Phrasing:** By relying less on exact name matches and more on conceptual understanding and relationships, the system should become more robust to variations in user query phrasing.
*   **Fixing Potential Breakage:** Addressing the undefined functions in the "NUCLEAR OPTION" path is crucial for intended behavior.

By making these logical adjustments, you should be able to leverage your "amazing IR JSON" more effectively and hopefully achieve the context selection results you're expecting. Remember to test incrementally and observe how scores change for different types of queries.