#!/usr/bin/env python3
"""
PROVE IT! Real-world test of Enhanced Semantic Intelligence
Test on actual aider-main codebase with real queries and expected results
"""

import sys
import os
import json
from pathlib import Path
from typing import List, Dict, Set

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
from aider.context_request.intelligent_context_models import QueryContext, QueryIntent, QueryScope

class RealWorldTestSuite:
    """Real-world test suite for semantic intelligence"""
    
    def __init__(self):
        self.selector = HierarchicalContextSelector()
        self.ir_data = None
        self.test_results = []
        
    def load_aider_ir_data(self):
        """Load IR data for aider-main codebase"""
        ir_files = list(Path("aider-main").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data found in aider-main. Generating...")
            # Try to find IR data in current directory
            ir_files = list(Path(".").glob("ir_data_*.json"))
            
        if not ir_files:
            print("❌ No IR data files found. Please run IR generation first.")
            return False
            
        # Use the most recent IR file
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        with open(latest_ir_file, 'r') as f:
            self.ir_data = json.load(f)
            
        print(f"✅ Loaded IR data with {len(self.ir_data.get('modules', []))} modules")
        return True
        
    def get_real_world_test_cases(self) -> List[Dict]:
        """Define real-world test cases with expected entities"""
        return [
            {
                "query": "How does aider apply edits to files?",
                "description": "File editing workflow - core aider functionality",
                "expected_entities": [
                    "apply_edits", "edit_files", "FileEditor", "apply_edit", 
                    "write_text", "do_edit", "EditBlockFencer"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "workflow"
            },
            {
                "query": "How does the chat loop work in aider?",
                "description": "Main interaction loop - central workflow",
                "expected_entities": [
                    "run", "chat_loop", "send_message", "get_input", 
                    "process_message", "main_loop", "Coder"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "workflow"
            },
            {
                "query": "What handles LLM API calls and responses?",
                "description": "LLM integration - external API handling",
                "expected_entities": [
                    "send_message", "get_response", "LLMClient", "api_call",
                    "chat_completion", "openai", "anthropic"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "integration"
            },
            {
                "query": "How does aider gather context from the repository?",
                "description": "Context gathering - repository analysis",
                "expected_entities": [
                    "get_repo_map", "gather_context", "RepoMap", "scan_files",
                    "get_context", "context_request", "map_request"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "workflow"
            },
            {
                "query": "What classes handle git operations?",
                "description": "Git integration - version control",
                "expected_entities": [
                    "GitRepo", "commit", "git_add", "git_commit", "GitManager",
                    "repo", "git_status", "get_commit_message"
                ],
                "expected_types": ["class", "method", "function"],
                "category": "integration"
            },
            {
                "query": "How are errors handled in aider?",
                "description": "Error handling - debugging and exceptions",
                "expected_entities": [
                    "handle_error", "exception", "error_handler", "try_catch",
                    "log_error", "ErrorHandler", "raise_error"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "debugging"
            },
            {
                "query": "What manages different LLM models?",
                "description": "Model management - configuration and selection",
                "expected_entities": [
                    "Model", "ModelManager", "get_model", "model_config",
                    "select_model", "model_settings", "configure_model"
                ],
                "expected_types": ["class", "method", "function"],
                "category": "architecture"
            },
            {
                "query": "How does file parsing and analysis work?",
                "description": "File processing - code analysis",
                "expected_entities": [
                    "parse_file", "FileParser", "analyze_code", "extract_functions",
                    "scan_code", "code_parser", "ast_parser"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "implementation"
            },
            {
                "query": "What handles user input and commands?",
                "description": "User interface - command processing",
                "expected_entities": [
                    "get_input", "process_command", "InputHandler", "parse_command",
                    "handle_command", "user_input", "command_parser"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "workflow"
            },
            {
                "query": "How does aider manage file watching and changes?",
                "description": "File monitoring - change detection",
                "expected_entities": [
                    "watch_files", "FileWatcher", "detect_changes", "file_monitor",
                    "on_file_change", "watch_directory", "file_observer"
                ],
                "expected_types": ["method", "function", "class"],
                "category": "implementation"
            }
        ]
    
    def run_test_case(self, test_case: Dict) -> Dict:
        """Run a single test case and analyze results"""
        print(f"\n🧪 TESTING: {test_case['description']}")
        print(f"Query: '{test_case['query']}'")
        print(f"Expected entities: {test_case['expected_entities']}")
        print("-" * 70)
        
        try:
            # Run hierarchical context selection
            result = self.selector.select_hierarchical_context(
                ir_data=self.ir_data,
                user_query=test_case['query'],
                focus_entities=None,
                max_entities=10  # Get more entities for better analysis
            )
            
            selected_entities = result.get('selected_entities', [])
            
            # Analyze results
            found_entities = []
            entity_scores = []
            
            for entity in selected_entities:
                entity_name = entity.get('name', '').lower()
                entity_type = entity.get('type', '')
                score = entity.get('relevance_score', 0)
                
                found_entities.append({
                    'name': entity.get('name', ''),
                    'type': entity_type,
                    'score': score,
                    'file': entity.get('file_path', ''),
                    'cluster': entity.get('cluster', '')
                })
                entity_scores.append(score)
            
            # Calculate relevance metrics
            expected_set = set(e.lower() for e in test_case['expected_entities'])
            found_set = set(e['name'].lower() for e in found_entities)
            
            # Check for exact matches
            exact_matches = []
            partial_matches = []
            
            for expected in test_case['expected_entities']:
                expected_lower = expected.lower()
                
                # Check for exact matches
                exact_found = False
                for found in found_entities:
                    if expected_lower == found['name'].lower():
                        exact_matches.append((expected, found['name'], found['score']))
                        exact_found = True
                        break
                
                # Check for partial matches if no exact match
                if not exact_found:
                    for found in found_entities:
                        if (expected_lower in found['name'].lower() or 
                            found['name'].lower() in expected_lower):
                            partial_matches.append((expected, found['name'], found['score']))
                            break
            
            # Calculate metrics
            total_expected = len(test_case['expected_entities'])
            total_found = len(exact_matches) + len(partial_matches)
            relevance_ratio = total_found / total_expected if total_expected > 0 else 0
            
            # Show results
            print(f"\n📊 RESULTS:")
            print(f"   Selected entities: {len(selected_entities)}")
            print(f"   Expected entities: {total_expected}")
            print(f"   Found (exact + partial): {total_found}")
            print(f"   Relevance ratio: {relevance_ratio:.1%}")
            
            # Show top entities
            print(f"\n🎯 TOP SELECTED ENTITIES:")
            for i, entity in enumerate(found_entities[:5], 1):
                print(f"   {i}. {entity['name']} ({entity['type']}) - Score: {entity['score']:.2f}")
            
            # Show matches
            if exact_matches:
                print(f"\n✅ EXACT MATCHES ({len(exact_matches)}):")
                for expected, found, score in exact_matches:
                    print(f"   ✓ {expected} → {found} (score: {score:.2f})")
            
            if partial_matches:
                print(f"\n🔍 PARTIAL MATCHES ({len(partial_matches)}):")
                for expected, found, score in partial_matches:
                    print(f"   ~ {expected} → {found} (score: {score:.2f})")
            
            # Show misses
            all_matches = set(m[0].lower() for m in exact_matches + partial_matches)
            missed = [e for e in test_case['expected_entities'] if e.lower() not in all_matches]
            if missed:
                print(f"\n❌ MISSED ENTITIES ({len(missed)}):")
                for entity in missed:
                    print(f"   ✗ {entity}")
            
            # Return test result
            return {
                'test_case': test_case,
                'selected_entities': found_entities,
                'exact_matches': len(exact_matches),
                'partial_matches': len(partial_matches),
                'total_expected': total_expected,
                'relevance_ratio': relevance_ratio,
                'avg_score': sum(entity_scores) / len(entity_scores) if entity_scores else 0,
                'missed_entities': missed,
                'success': relevance_ratio >= 0.3  # 30% threshold for success
            }
            
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
            return {
                'test_case': test_case,
                'error': str(e),
                'success': False
            }
    
    def run_full_test_suite(self):
        """Run the complete test suite"""
        print("🚀 ENHANCED SEMANTIC INTELLIGENCE - REAL WORLD PROOF")
        print("Testing on actual aider-main codebase")
        print("=" * 80)
        
        # Load IR data
        if not self.load_aider_ir_data():
            return
        
        # Get test cases
        test_cases = self.get_real_world_test_cases()
        
        # Run all tests
        results = []
        for test_case in test_cases:
            result = self.run_test_case(test_case)
            results.append(result)
            self.test_results.append(result)
        
        # Generate summary
        self.generate_test_summary(results)
    
    def generate_test_summary(self, results: List[Dict]):
        """Generate comprehensive test summary"""
        print("\n" + "=" * 80)
        print("🎯 FINAL RESULTS - PROOF OF SEMANTIC INTELLIGENCE")
        print("=" * 80)
        
        successful_tests = [r for r in results if r.get('success', False)]
        failed_tests = [r for r in results if not r.get('success', False)]
        
        total_tests = len(results)
        success_rate = len(successful_tests) / total_tests if total_tests > 0 else 0
        
        print(f"\n📊 OVERALL PERFORMANCE:")
        print(f"   Total tests: {total_tests}")
        print(f"   Successful: {len(successful_tests)}")
        print(f"   Failed: {len(failed_tests)}")
        print(f"   Success rate: {success_rate:.1%}")
        
        # Calculate detailed metrics
        total_expected = sum(r.get('total_expected', 0) for r in results if 'total_expected' in r)
        total_exact_matches = sum(r.get('exact_matches', 0) for r in results if 'exact_matches' in r)
        total_partial_matches = sum(r.get('partial_matches', 0) for r in results if 'partial_matches' in r)
        
        if total_expected > 0:
            exact_match_rate = total_exact_matches / total_expected
            total_match_rate = (total_exact_matches + total_partial_matches) / total_expected
            
            print(f"\n🎯 RELEVANCE METRICS:")
            print(f"   Total expected entities: {total_expected}")
            print(f"   Exact matches: {total_exact_matches} ({exact_match_rate:.1%})")
            print(f"   Partial matches: {total_partial_matches}")
            print(f"   Total matches: {total_exact_matches + total_partial_matches} ({total_match_rate:.1%})")
        
        # Show best and worst performing tests
        valid_results = [r for r in results if 'relevance_ratio' in r]
        if valid_results:
            best_test = max(valid_results, key=lambda r: r['relevance_ratio'])
            worst_test = min(valid_results, key=lambda r: r['relevance_ratio'])
            
            print(f"\n🏆 BEST PERFORMING TEST:")
            print(f"   Query: '{best_test['test_case']['query']}'")
            print(f"   Relevance: {best_test['relevance_ratio']:.1%}")
            
            print(f"\n📉 WORST PERFORMING TEST:")
            print(f"   Query: '{worst_test['test_case']['query']}'")
            print(f"   Relevance: {worst_test['relevance_ratio']:.1%}")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if success_rate >= 0.7:
            print("✅ SEMANTIC INTELLIGENCE SYSTEM: PROVEN SUCCESSFUL!")
            print("   The enhanced system significantly outperforms basic keyword matching")
        elif success_rate >= 0.5:
            print("🔶 SEMANTIC INTELLIGENCE SYSTEM: PARTIALLY SUCCESSFUL")
            print("   Shows improvement but needs further refinement")
        else:
            print("❌ SEMANTIC INTELLIGENCE SYSTEM: NEEDS IMPROVEMENT")
            print("   Current implementation requires significant enhancement")

if __name__ == "__main__":
    test_suite = RealWorldTestSuite()
    test_suite.run_full_test_suite()
