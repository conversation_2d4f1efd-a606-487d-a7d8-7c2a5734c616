#!/usr/bin/env python3
"""
Test the new AI-centric scoring approach for position management queries.
"""

import sys
import os
sys.path.append('aider-main/aider/context_request')


def test_ai_centric_position_scoring():
    """Test the AI-centric scoring with position management entities."""
    print("🧠 TESTING AI-CENTRIC POSITION SCORING")
    print("=" * 70)
    
    try:
        from hierarchical_context_selector import HierarchicalContextSelector
        from query_intent_analyzer import QueryIntentAnalyzer
        from system_architecture_generator import SystemArchitectureGenerator
        from semantic_context_selector import SemanticContextSelector
        
        # Initialize components
        analyzer = QueryIntentAnalyzer()
        arch_gen = SystemArchitectureGenerator()
        semantic_sel = SemanticContextSelector()
        selector = HierarchicalContextSelector(analyzer, arch_gen, semantic_sel)
        
        # Test entities (mix of relevant and irrelevant)
        test_entities = [
            {'name': 'PositionOpener', 'type': 'class', 'file': 'position_entry_manager.py'},
            {'name': 'PositionCloser', 'type': 'class', 'file': 'position_exit_manager.py'},
            {'name': 'BacktestPositionManager', 'type': 'class', 'file': 'backtest_position_manager.py'},
            {'name': 'DatabaseManager', 'type': 'class', 'file': 'database_manager.py'},
            {'name': 'TelegramManager', 'type': 'class', 'file': 'notification_service.py'},
            {'name': 'position_exit_manager', 'type': 'variable', 'file': 'main.py'},
            {'name': 'db_manager', 'type': 'variable', 'file': 'main.py'},
            {'name': 'initialize_positions_if_needed', 'type': 'function', 'file': 'position_repository.py'},
            {'name': 'main', 'type': 'function', 'file': 'main.py'},
        ]
        
        query = "how does the system manage positions?"
        
        print(f"🎯 Query: {query}")
        print(f"📊 Testing {len(test_entities)} entities")
        print()
        
        # Score each entity using AI-centric approach
        scored_results = []
        
        for entity in test_entities:
            entity_name = entity['name']
            entity_type = entity['type']
            entity_file = entity['file']
            
            print(f"🧠 Scoring: {entity_name} ({entity_type})")
            score = selector._ai_centric_entity_scoring(entity_name, entity_type, query, entity_file)
            
            scored_results.append({
                'name': entity_name,
                'type': entity_type,
                'file': entity_file,
                'score': score
            })
            print(f"   Final Score: {score:.1f}")
            print()
        
        # Sort by score (highest first)
        scored_results.sort(key=lambda x: x['score'], reverse=True)
        
        print("🏆 FINAL RANKING (AI-Centric Scoring)")
        print("=" * 70)
        
        for i, result in enumerate(scored_results, 1):
            name = result['name']
            score = result['score']
            entity_type = result['type']
            
            # Determine if this is a good selection
            is_position_related = any(keyword in name.lower() for keyword in ['position', 'trade', 'order'])
            is_business_logic = entity_type == 'class' and not name.endswith('Manager')
            
            status = "✅ EXCELLENT" if is_position_related and is_business_logic else \
                    "🎯 GOOD" if is_position_related else \
                    "🔧 INFRASTRUCTURE" if entity_type == 'class' else \
                    "🚫 NOISE"
            
            print(f"{i:2d}. {name:<25} ({entity_type:<8}) Score: {score:6.1f} {status}")
        
        # Analyze results
        print("\n📊 ANALYSIS")
        print("=" * 70)
        
        top_3 = scored_results[:3]
        position_classes_in_top_3 = sum(1 for r in top_3 if 'Position' in r['name'])
        infrastructure_in_top_3 = sum(1 for r in top_3 if r['name'].endswith('Manager') and 'Position' not in r['name'])
        
        print(f"Position classes in top 3: {position_classes_in_top_3}/3")
        print(f"Infrastructure classes in top 3: {infrastructure_in_top_3}/3")
        
        if position_classes_in_top_3 >= 2:
            print("🎉 SUCCESS: Position classes properly prioritized!")
        elif position_classes_in_top_3 >= 1:
            print("🔄 PARTIAL: Some position classes prioritized")
        else:
            print("❌ FAILURE: Position classes not prioritized")
        
        if infrastructure_in_top_3 == 0:
            print("✅ EXCELLENT: No irrelevant infrastructure in top 3")
        else:
            print(f"⚠️  WARNING: {infrastructure_in_top_3} infrastructure classes in top 3")
        
        return scored_results
        
    except Exception as e:
        print(f"❌ Error testing AI-centric scoring: {e}")
        import traceback
        traceback.print_exc()
        return []


def test_with_real_context_system():
    """Test with the actual context request system."""
    print("\n🧪 TESTING WITH REAL CONTEXT SYSTEM")
    print("=" * 70)
    
    try:
        from context_request_handler import ContextRequestHandler
        
        # Test with current directory (aider project)
        handler = ContextRequestHandler(".")
        
        # Create position query request
        request = {
            'user_query': 'how does the system manage positions?',
            'task_description': 'Position management analysis',
            'task_type': 'analysis',
            'max_entities': 8,
            'llm_friendly': True,
            'focus_entities': ['PositionOpener', 'PositionCloser']
        }
        
        print("🎯 Processing position query with AI-centric scoring...")
        
        # Process the request
        result = handler.process_context_request(request)
        
        if 'error' in result:
            print(f"❌ Error: {result['error']}")
            return False
        
        # Check what was selected
        package = result.get('llm_friendly_package', '')
        
        if not package:
            print("❌ No package generated")
            return False
        
        print(f"✅ Package generated: {len(package):,} characters")
        
        # Analyze the package content
        lines = package.split('\n')
        selected_entities = []
        
        for line in lines:
            if line.startswith('### ') and ('🏛️' in line or '⚙️' in line or '📊' in line):
                # Extract entity name
                entity_name = line.split('🏛️')[-1].split('⚙️')[-1].split('📊')[-1].strip()
                if entity_name and entity_name not in selected_entities:
                    selected_entities.append(entity_name)
        
        print(f"\n📊 Selected Entities ({len(selected_entities)}):")
        for i, entity in enumerate(selected_entities, 1):
            # Categorize entity
            if 'Position' in entity:
                category = "🎯 POSITION CLASS"
            elif entity.endswith('Manager') and 'Position' not in entity:
                category = "🔧 INFRASTRUCTURE"
            elif entity.islower() and '_' in entity:
                category = "📊 VARIABLE"
            else:
                category = "⚙️ OTHER"
            
            print(f"   {i}. {entity:<25} {category}")
        
        # Check success criteria
        position_entities = [e for e in selected_entities if 'position' in e.lower()]
        infrastructure_entities = [e for e in selected_entities if e.endswith('Manager') and 'position' not in e.lower()]
        
        print(f"\n🎯 Position-related entities: {len(position_entities)}")
        print(f"🔧 Infrastructure entities: {len(infrastructure_entities)}")
        
        if len(position_entities) >= 2:
            print("🎉 SUCCESS: Multiple position entities selected!")
            
            # Save successful package
            with open("ai_centric_position_package.txt", "w", encoding='utf-8') as f:
                f.write(package)
            print("📄 Saved package to: ai_centric_position_package.txt")
            return True
        else:
            print("❌ FAILURE: Not enough position entities selected")
            return False
        
    except Exception as e:
        print(f"❌ Error testing real system: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🧠 AI-CENTRIC POSITION SCORING TEST")
    print("=" * 70)
    print("Testing the new AI-model-thinking approach to context selection")
    print()
    
    # Test 1: Direct scoring test
    scoring_results = test_ai_centric_position_scoring()
    
    # Test 2: Real system test
    real_system_success = test_with_real_context_system()
    
    # Summary
    print("\n💡 SUMMARY")
    print("=" * 70)
    
    if scoring_results:
        top_entity = scoring_results[0]['name'] if scoring_results else "None"
        print(f"🏆 Top scored entity: {top_entity}")
        
        if 'Position' in top_entity:
            print("✅ AI-centric scoring working: Position class scored highest")
        else:
            print("❌ AI-centric scoring needs improvement")
    
    if real_system_success:
        print("🎉 OVERALL SUCCESS: AI-centric approach working in real system!")
    else:
        print("🔧 NEEDS WORK: AI-centric approach needs refinement")
    
    print("\n🧠 KEY INSIGHT:")
    print("AI models need BUSINESS LOGIC context, not infrastructure!")
    print("PositionOpener/PositionCloser > DatabaseManager/TelegramManager")


if __name__ == "__main__":
    main()
