"""
Test Both Templates
Test both proactive and reactive context templates to ensure they work correctly.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import Context<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest
from aider.coders.base_prompts import Coder<PERSON>rom<PERSON>


def test_template_1_proactive():
    """Test Template 1: Proactive context (user query → system provides context)"""
    print("🧪 Testing Template 1: Proactive Context")
    print("=" * 60)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Create a test request (simulating what process_direct_ir_context does)
    user_message = "How does user authentication work in the system?"
    request = IRContextRequest(
        user_query=user_message,
        task_description=f"Analyze and provide context for: {user_message}",
        task_type="general_analysis",
        focus_entities=["authenticate", "user", "login", "auth"],
        max_tokens=2000,
        llm_friendly=True,
        include_code_context=True,
        max_entities=8
    )
    
    try:
        print(f"🚀 Processing proactive IR context request...")
        
        # Process the request (this uses semantic intelligence)
        result = handler.process_ir_context_request(request)
        
        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        
        if "llm_friendly_package" not in result:
            print(f"   ❌ No LLM-friendly package created")
            return False
        
        llm_package = result["llm_friendly_package"]
        print(f"   ✅ LLM package created: {len(llm_package):,} characters")
        
        # Create Template 1 format (what process_direct_ir_context does)
        context_message = f"""# Intelligent Context for Your Query

{llm_package}

---

**Original User Query**: {user_message}

Please analyze the above context and provide a comprehensive answer to the user's query."""
        
        print(f"   ✅ Template 1 created: {len(context_message):,} characters")
        
        # Show template preview
        print(f"\n📄 Template 1 Preview (first 300 chars):")
        print("-" * 50)
        print(context_message[:300])
        print("... (content continues)")
        print("-" * 50)
        
        # Verify semantic content
        if "Intelligent Semantic Analysis" in llm_package:
            print(f"   ✅ Contains semantic intelligence markers")
        if "Query Intent:" in llm_package:
            print(f"   ✅ Contains query intent analysis")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False


def test_template_2_reactive():
    """Test Template 2: Reactive context (LLM requests context)"""
    print(f"\n🧪 Testing Template 2: Reactive Context")
    print("=" * 60)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Create a test request (simulating what an LLM would send)
    user_message = "How does user authentication work in the system?"
    ir_request = IRContextRequest(
        user_query=user_message,
        task_description="Analyze authentication workflow and security components",
        task_type="security_analysis",
        focus_entities=["authenticate", "user", "login", "auth"],
        max_tokens=8000,
        llm_friendly=True,
        include_ir_slices=True,
        include_code_context=True,
        max_output_chars=30000,
        max_entities=8
    )
    
    try:
        print(f"🚀 Processing reactive IR context request...")
        
        # Process the request (this uses semantic intelligence)
        result = handler.process_ir_context_request(ir_request)
        
        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        
        if "llm_friendly_package" not in result:
            print(f"   ❌ No LLM-friendly package created")
            return False
        
        llm_package = result["llm_friendly_package"]
        print(f"   ✅ LLM package created: {len(llm_package):,} characters")
        
        # Create Template 2 format (what _create_ir_context_augmented_prompt does)
        prompts = CoderPrompts()
        prefix = prompts.ir_context_response_prefix
        suffix = prompts.ir_context_response_suffix
        
        # Format the prefix with the user query
        if "{user_query}" in prefix:
            formatted_prefix = prefix.format(user_query=user_message)
        else:
            formatted_prefix = prefix
        
        # Create the final template
        augmented_prompt = formatted_prefix + llm_package + suffix
        
        print(f"   ✅ Template 2 created: {len(augmented_prompt):,} characters")
        
        # Show template preview
        print(f"\n📄 Template 2 Preview (first 300 chars):")
        print("-" * 50)
        print(augmented_prompt[:300])
        print("... (content continues)")
        print("-" * 50)
        
        # Verify template format
        expected_start = f"based on your request {user_message}, here's the relevant context"
        if augmented_prompt.startswith(expected_start):
            print(f"   ✅ Template format correct")
        else:
            print(f"   ❌ Template format incorrect")
            print(f"      Expected: '{expected_start}'")
            print(f"      Actual: '{augmented_prompt[:len(expected_start)]}'")
            return False
        
        # Verify semantic content
        if "Intelligent Semantic Analysis" in llm_package:
            print(f"   ✅ Contains semantic intelligence markers")
        if "Query Intent:" in llm_package:
            print(f"   ✅ Contains query intent analysis")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        return False


def compare_templates():
    """Compare both templates to show the differences."""
    print(f"\n🔍 Template Comparison")
    print("=" * 60)
    
    prompts = CoderPrompts()
    
    print(f"📋 Template 1 (Proactive - User Query):")
    print(f"   Format: '# Intelligent Context for Your Query\\n\\n{{llm_package}}\\n\\n---\\n\\n**Original User Query**: {{user_message}}\\n\\nPlease analyze...'")
    print(f"   Purpose: System proactively provides context when user asks a question")
    print(f"   Trigger: User asks a question")
    
    print(f"\n📋 Template 2 (Reactive - LLM Request):")
    print(f"   Format: '{prompts.ir_context_response_prefix}{{llm_package}}{prompts.ir_context_response_suffix}'")
    print(f"   Purpose: LLM requests specific context and receives it")
    print(f"   Trigger: LLM sends IR_CONTEXT_REQUEST")
    
    print(f"\n🎯 Key Differences:")
    print(f"   ✅ Both use semantic intelligence (same llm_package)")
    print(f"   ✅ Both include user query in the response")
    print(f"   📝 Template 1: More detailed format with analysis instructions")
    print(f"   📝 Template 2: Cleaner format with 'based on your request...'")


if __name__ == "__main__":
    print("🔧 Testing Both Context Templates")
    print("=" * 70)
    
    # Test Template 1 (Proactive)
    test1_passed = test_template_1_proactive()
    
    # Test Template 2 (Reactive)
    test2_passed = test_template_2_reactive()
    
    # Compare templates
    compare_templates()
    
    print(f"\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"   Template 1 (Proactive): {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Template 2 (Reactive): {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 BOTH TEMPLATES WORKING CORRECTLY!")
        print(f"   ✅ Both use semantic intelligence")
        print(f"   ✅ Both include user query in response")
        print(f"   ✅ Both deliver enhanced context to LLMs")
        print(f"\n💡 If LLMs aren't receiving context as expected, the issue may be:")
        print(f"   • LLM not making IR_CONTEXT_REQUEST properly")
        print(f"   • Different expectation about response format")
        print(f"   • Caching or delivery mechanism issue")
    else:
        print(f"\n❌ TEMPLATE ISSUES DETECTED!")
        print(f"   Check the failed tests above for details")
