#!/usr/bin/env python3
"""
Test the enhanced class methods display in context packages
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_class_methods_enhancement():
    """Test that class methods are included in context packages"""
    print("🏛️ TESTING CLASS METHODS ENHANCEMENT")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test with a position management query
        query = "how does the system manage position?"
        
        request = IRContextRequest(
            user_query=query,
            task_description=f'Test class methods for: {query}',
            task_type='debugging',
            focus_entities=None,
            max_tokens=20000,  # 🚀 INCREASED from 3000 to 20000
            include_ir_slices=True,
            include_code_context=False,
            llm_friendly=True,  # Generate LLM-friendly package
            max_entities=15,   # 🚀 INCREASED from 3 to 15
            max_output_chars=100000  # 🚀 INCREASED from 50000 to 100000
        )
        
        print(f"🧪 Testing query: '{query}'")
        print(f"   Requesting LLM-friendly package with class methods...")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
            return False
        
        # Get IR slices and check for classes
        ir_slices = result.get('ir_slices', [])
        classes_found = [slice for slice in ir_slices if slice.get('entity_type') == 'class']
        
        print(f"\n📊 RESULTS:")
        print(f"   Total entities: {len(ir_slices)}")
        print(f"   Classes found: {len(classes_found)}")
        
        # Check each class for methods
        methods_found = False
        for class_slice in classes_found:
            class_name = class_slice.get('entity_name', '')
            class_methods = class_slice.get('class_methods', [])
            
            print(f"\n🏛️ Class: {class_name}")
            print(f"   Methods: {len(class_methods)}")
            
            if class_methods:
                methods_found = True
                for method in class_methods[:5]:  # Show first 5 methods
                    method_name = method.get('name', '')
                    complexity = method.get('complexity', 0)
                    calls_super = method.get('calls_super', False)
                    super_indicator = " 🔗" if calls_super else ""
                    print(f"      - {method_name}{super_indicator} (complexity: {complexity})")
                
                if len(class_methods) > 5:
                    print(f"      - ... (+{len(class_methods) - 5} more)")
        
        # Check LLM-friendly package for class methods display
        llm_package = result.get('llm_friendly_package', '')
        has_methods_section = '🏛️ Class Methods' in llm_package
        
        print(f"\n📦 LLM PACKAGE ANALYSIS:")
        print(f"   Package size: {len(llm_package):,} characters")
        print(f"   Contains class methods section: {'✅' if has_methods_section else '❌'}")
        
        if has_methods_section:
            # Count how many method sections are in the package
            method_sections = llm_package.count('🏛️ Class Methods')
            print(f"   Number of classes with methods shown: {method_sections}")
            
            # Show a snippet of the methods section
            start_idx = llm_package.find('🏛️ Class Methods')
            if start_idx != -1:
                end_idx = llm_package.find('\n\n', start_idx + 200)  # Find next section
                if end_idx == -1:
                    end_idx = start_idx + 500
                snippet = llm_package[start_idx:end_idx]
                print(f"\n📋 METHODS SECTION PREVIEW:")
                print(f"   {snippet}...")
        
        # Success criteria
        success = methods_found and has_methods_section
        
        if success:
            print(f"\n🎉 SUCCESS!")
            print(f"   ✅ Class methods are being found and included")
            print(f"   ✅ LLM-friendly package displays class methods")
            print(f"   ✅ Enhancement is working correctly!")
            return True
        else:
            print(f"\n❌ ISSUES FOUND:")
            if not methods_found:
                print(f"   ❌ No class methods found in IR slices")
            if not has_methods_section:
                print(f"   ❌ No class methods section in LLM package")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_class_methods():
    """Test finding methods for a specific class"""
    print(f"\n🔍 TESTING SPECIFIC CLASS METHOD LOOKUP")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        import json
        
        # Load the latest IR data file
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return False
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        with open(latest_ir_file, 'r', encoding='utf-8') as f:
            ir_data = json.load(f)
        
        # Create handler and test the method finding function
        handler = ContextRequestHandler('.')
        
        # Find some classes to test
        test_classes = []
        for module in ir_data.get('modules', []):
            for entity in module.get('entities', []):
                if entity.get('type') == 'class':
                    test_classes.append(entity.get('name', ''))
                    if len(test_classes) >= 3:  # Test first 3 classes
                        break
            if len(test_classes) >= 3:
                break
        
        print(f"🧪 Testing method lookup for classes: {test_classes}")
        
        for class_name in test_classes:
            methods = handler._find_class_methods(class_name, ir_data)
            print(f"\n🏛️ Class: {class_name}")
            print(f"   Found {len(methods)} methods:")
            
            for method in methods[:5]:  # Show first 5
                method_name = method.get('name', '')
                complexity = method.get('complexity', 0)
                calls_super = method.get('calls_super', False)
                super_indicator = " 🔗" if calls_super else ""
                print(f"      - {method_name}{super_indicator} (complexity: {complexity})")
            
            if len(methods) > 5:
                print(f"      - ... (+{len(methods) - 5} more)")
        
        return len(test_classes) > 0
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🏛️ TESTING CLASS METHODS ENHANCEMENT")
    print("=" * 80)
    
    # Test 1: Specific class method lookup
    lookup_success = test_specific_class_methods()
    
    # Test 2: Full integration test
    integration_success = test_class_methods_enhancement()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Method lookup: {'✅' if lookup_success else '❌'}")
    print(f"   Integration test: {'✅' if integration_success else '❌'}")
    
    if lookup_success and integration_success:
        print(f"\n🎉 CLASS METHODS ENHANCEMENT IS WORKING!")
        print(f"   ✅ Classes now show their methods in context packages")
        print(f"   ✅ Users can see what each class can do")
        print(f"   ✅ Enhanced architectural understanding!")
    else:
        print(f"\n❌ ENHANCEMENT NEEDS MORE WORK")
        print(f"   Some issues remain to be resolved")
