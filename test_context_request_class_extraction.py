#!/usr/bin/env python3
"""
Test script to investigate CONTEXT_REQUEST class extraction issue.
This script tests how classes vs functions are extracted and formatted.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContextRequest, SymbolRequest

def test_class_extraction():
    """Test class extraction to see if full implementation is included."""
    print("🔍 Testing CONTEXT_REQUEST class extraction...")
    
    # Create handler
    handler = ContextRequestHandler('.')
    
    # Test with a class
    request = ContextRequest(
        original_user_query_context='Test class extraction',
        reason_for_request='Testing class vs function extraction',
        symbols_of_interest=[
            SymbolRequest(type='class_definition', name='ContextRequestHandler'),
            SymbolRequest(type='class_definition', name='IntelligentContextSelector')
        ]
    )
    
    # Process the request
    result = handler.process_context_request(request)
    
    # Analyze the results
    print(f"\n📊 Results Summary:")
    print(f"   Extracted symbols: {len(result['extracted_symbols'])}")
    print(f"   Dependency snippets: {len(result['dependency_snippets'])}")
    
    for i, symbol in enumerate(result['extracted_symbols'], 1):
        print(f"\n=== Symbol {i}: {symbol['symbol_name']} ===")
        print(f"File: {symbol['file_path']}")
        print(f"Content length: {len(symbol['content'])} characters")
        
        # Check if it's a class signature only or full implementation
        content = symbol['content']
        lines = content.split('\n')
        
        print(f"Total lines: {len(lines)}")
        print(f"First line: {lines[0] if lines else 'N/A'}")
        
        # Check for class methods
        method_count = content.count('def ')
        print(f"Method definitions found: {method_count}")
        
        # Check if it looks like just a signature
        if len(lines) <= 5 and method_count == 0:
            print("⚠️  WARNING: This looks like a class signature only!")
        elif method_count > 0:
            print("✅ Full class implementation with methods detected")
        
        # Show first 300 chars for inspection
        print(f"\nContent preview (first 300 chars):")
        print("-" * 50)
        print(content[:300])
        if len(content) > 300:
            print("... [truncated]")
        print("-" * 50)

def test_function_extraction():
    """Test function extraction for comparison."""
    print("\n🔍 Testing function extraction for comparison...")
    
    handler = ContextRequestHandler('.')
    
    # Test with a function
    request = ContextRequest(
        original_user_query_context='Test function extraction',
        reason_for_request='Testing function extraction',
        symbols_of_interest=[
            SymbolRequest(type='function_definition', name='process_context_request')
        ]
    )
    
    result = handler.process_context_request(request)
    
    for symbol in result['extracted_symbols']:
        print(f"\n=== Function: {symbol['symbol_name']} ===")
        print(f"Content length: {len(symbol['content'])} characters")
        print(f"Content preview (first 300 chars):")
        print("-" * 50)
        print(symbol['content'][:300])
        print("-" * 50)

if __name__ == "__main__":
    test_class_extraction()
    test_function_extraction()
