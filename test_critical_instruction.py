#!/usr/bin/env python3
"""
Test that the critical instruction appears in LLM packages
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_critical_instruction_in_package():
    """Test that the critical instruction appears at the top of LLM packages"""
    print("🚀 TESTING CRITICAL INSTRUCTION IN LLM PACKAGES")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRe<PERSON><PERSON>and<PERSON>, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        print("\n🧪 TEST: Generate LLM package with critical instruction")
        print("-" * 50)
        
        request = IRContextRequest(
            user_query="how does the context system work?",
            task_description="Test critical instruction",
            task_type="general_analysis",
            max_tokens=15000,
            max_entities=8,
            llm_friendly=True,
            max_output_chars=100000
        )
        
        print(f"   📊 Request settings:")
        print(f"      max_tokens: {request.max_tokens}")
        print(f"      max_entities: {request.max_entities}")
        print(f"      llm_friendly: {request.llm_friendly}")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
            return False
        
        # Get the LLM package
        llm_package = result.get('llm_friendly_package', '')
        
        if not llm_package:
            print(f"   ❌ ERROR: No LLM package generated")
            return False
        
        print(f"\n📦 LLM PACKAGE ANALYSIS:")
        print(f"   Package size: {len(llm_package):,} characters")
        
        # Check for the critical instruction
        expected_instruction = "Critical Instruction:"
        expected_content = "Before answering the user query, identify the most relevant entities"
        
        lines = llm_package.split('\n')
        
        # Find the critical instruction
        instruction_found = False
        instruction_line_num = -1
        entities_line_num = -1
        
        for i, line in enumerate(lines):
            if expected_instruction in line:
                instruction_found = True
                instruction_line_num = i
                print(f"   ✅ Found critical instruction at line {i+1}")
                break
        
        if not instruction_found:
            print(f"   ❌ CRITICAL INSTRUCTION NOT FOUND")
            print(f"   📋 First 10 lines of package:")
            for i, line in enumerate(lines[:10]):
                print(f"      {i+1:2d}: {line}")
            return False
        
        # Check that the instruction content is correct
        content_found = False
        for i in range(instruction_line_num, min(instruction_line_num + 5, len(lines))):
            if expected_content in lines[i]:
                content_found = True
                print(f"   ✅ Found instruction content at line {i+1}")
                break
        
        if not content_found:
            print(f"   ❌ INSTRUCTION CONTENT NOT FOUND")
            print(f"   📋 Lines around instruction:")
            start = max(0, instruction_line_num - 2)
            end = min(len(lines), instruction_line_num + 5)
            for i in range(start, end):
                print(f"      {i+1:2d}: {lines[i]}")
            return False
        
        # Find the CRITICAL ENTITIES section
        for i, line in enumerate(lines):
            if "CRITICAL ENTITIES" in line and "most important" in line:
                entities_line_num = i
                print(f"   ✅ Found CRITICAL ENTITIES section at line {i+1}")
                break
        
        if entities_line_num == -1:
            print(f"   ❌ CRITICAL ENTITIES SECTION NOT FOUND")
            return False
        
        # Check that instruction comes before entities
        if instruction_line_num < entities_line_num:
            print(f"   ✅ Critical instruction appears BEFORE entities section")
            print(f"      Instruction at line {instruction_line_num + 1}")
            print(f"      Entities at line {entities_line_num + 1}")
        else:
            print(f"   ❌ Critical instruction appears AFTER entities section")
            print(f"      Instruction at line {instruction_line_num + 1}")
            print(f"      Entities at line {entities_line_num + 1}")
            return False
        
        # Show the actual instruction in the package
        print(f"\n📋 CRITICAL INSTRUCTION IN PACKAGE:")
        start = max(0, instruction_line_num)
        end = min(len(lines), entities_line_num + 1)
        for i in range(start, end):
            if lines[i].strip():  # Only show non-empty lines
                print(f"   {lines[i]}")
        
        # Count entities to verify dynamic limit is working
        import re
        entities_line = lines[entities_line_num]
        match = re.search(r'CRITICAL ENTITIES \((\d+) most important\)', entities_line)
        if match:
            entities_count = int(match.group(1))
            print(f"\n📊 ENTITIES COUNT:")
            print(f"   Found {entities_count} entities in package")
            
            if entities_count > 3:
                print(f"   ✅ Dynamic entity limit working (more than 3)")
            else:
                print(f"   ⚠️  Only {entities_count} entities (may be limited)")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_instruction_with_different_queries():
    """Test that the instruction appears with different types of queries"""
    print(f"\n🧪 TEST: Critical instruction with different queries")
    print("-" * 50)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        test_queries = [
            "show me the integration classes",
            "how does dependency management work?",
            "what are the context selection algorithms?"
        ]
        
        for i, query in enumerate(test_queries):
            print(f"\n   Query {i+1}: '{query}'")
            
            # Clear caches
            ContextRequestHandler._ir_cache.clear()
            ContextRequestHandler._ir_cache_timestamps.clear()
            handler = ContextRequestHandler('.')
            handler.cache.clear()
            
            request = IRContextRequest(
                user_query=query,
                task_description=f"Test query {i+1}",
                task_type="general_analysis",
                max_tokens=10000,
                max_entities=5,
                llm_friendly=True,
                max_output_chars=50000
            )
            
            result = handler.process_ir_context_request(request)
            
            if 'error' not in result:
                llm_package = result.get('llm_friendly_package', '')
                
                if "Critical Instruction:" in llm_package:
                    print(f"      ✅ Critical instruction found")
                else:
                    print(f"      ❌ Critical instruction missing")
                    return False
            else:
                print(f"      ❌ Error: {result['error']}")
                return False
        
        print(f"\n   ✅ Critical instruction appears in all packages")
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING CRITICAL INSTRUCTION IN LLM PACKAGES")
    print("=" * 80)
    
    # Test 1: Basic instruction presence
    basic_success = test_critical_instruction_in_package()
    
    # Test 2: Instruction with different queries
    queries_success = test_instruction_with_different_queries()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Basic instruction test: {'✅' if basic_success else '❌'}")
    print(f"   Multiple queries test: {'✅' if queries_success else '❌'}")
    
    if basic_success and queries_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   ✅ Critical instruction added to LLM packages")
        print(f"   ✅ Instruction appears before entities section")
        print(f"   ✅ Instruction guides LLM reasoning correctly")
        print(f"   ✅ Works with all types of queries")
        print(f"\n💡 LLM PACKAGES NOW INCLUDE:")
        print(f"   • Critical instruction for better reasoning")
        print(f"   • Dynamic entity counts (not limited to 3)")
        print(f"   • Class methods with inheritance info")
        print(f"   • Rich architectural context")
        print(f"\n🚀 USERS WILL GET:")
        print(f"   • Better LLM responses focused on core components")
        print(f"   • More targeted reasoning based on entities and methods")
        print(f"   • Improved understanding of system architecture")
    else:
        print(f"\n❌ ISSUES FOUND")
        print(f"   Need to verify critical instruction implementation")
