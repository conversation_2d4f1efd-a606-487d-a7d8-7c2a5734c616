## CRITICAL ENTITIES (2 most important)

### 1. warm_cache (method)
- File: aider-main\aider\coders\base_coder.py
- **Belongs to Class**: `Coder`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `Coder` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get", "time", "sleep", "completion", "cacheable_messages", "..."] (total: 10)
- **Used by**: ["base_coder_old", "base_coder"] (total: 2)
- **Side Effects**: network_io, writes_log, modifies_state

### 2. _update_cache (method)
- File: aider-main\aider\models.py
- **Belongs to Class**: `ModelInfoManager`
- **Inherits From**: No inheritance (base class)
- Criticality: medium | Risk: medium

#### 🔁 Class Context
- Part of `ModelInfoManager` class

#### 🧩 Method Details
- **Calls super()**: No
- **Calls**: ["get", "json", "write_text", "dumps"] (total: 4)
- **Used by**: ["openrouter", "surgical_context_extractor", "models", "context_request_handler", "surgical_file_extractor"] (total: 5)
- **Side Effects**: writes_log, modifies_state, modifies_file

⚠️ **Context Selection Notice**
The following code context was selected based on the user's query and initial relevance scoring.
It is not guaranteed to be the most relevant or complete subset of the codebase.

You, as the AI assistant, are responsible for validating assumptions, identifying potential missing dependencies, and requesting further context if needed.

A full index of related modules and functions is available below for reference.

## KEY IMPLEMENTATIONS (2 functions)
Complete code available on request for any function.

### 1. warm_cache
```python
    def warm_cache(self, chunks):
        if not self.add_cache_headers:
            return
        if not self.num_cache_warming_pings:
            return
        if not self.ok_to_warm_cache:
            return

        delay = 5 * 60 - 5
        delay = float(os.environ.get("AIDER_CACHE_KEEPALIVE_DELAY", delay))
        self.next_cache_warm = time.time() + delay
        self.warming_pings_left = self.num_cache_warming_pings
        self.cache_warming_chunks = chunks

        if self.cache_warming_thread:
            return

```

### 2. _update_cache
```python
    def _update_cache(self):
        try:
            import requests

            # Respect the --no-verify-ssl switch
            response = requests.get(self.MODEL_INFO_URL, timeout=5, verify=self.verify_ssl)
            if response.status_code == 200:
                self.content = response.json()
                try:
                    self.cache_file.write_text(json.dumps(self.content, indent=4))
                except OSError:
                    pass
        except Exception as ex:
            print(str(ex))
            try:
                # Save empty dict to cache file on failure
                self.cache_file.write_text("{}")
            except OSError:
                pass

```

## AWARENESS INDEX (15 additional entities)
*These exist in the system but are not included in the primary context above.*
*You can request specific functions/classes from this index if needed.*

### 📁 chat_chunks.py
- **Functions**: cacheable_messages

### 📁 context_request_handler.py
- **Functions**: _get_from_cache

### 📁 surgical_file_extractor.py
- **Functions**: _get_from_cache

### 📁 debug_symbol_extraction.py
- **Functions**: debug_symbol_extraction

### 📁 homepage.py
- **Functions**: read_from_cache, write_to_cache

### 📁 test_context_request_class_extraction.py
- **Functions**: test_class_extraction, test_function_extraction

### 📁 models.py
- **Functions**: _load_cache, get_model_from_cached_json_db

### 📁 repomap.py
- **Functions**: load_tags_cache, tags_cache_error

### 📁 surgical_context_extractor.py
- **Functions**: _get_from_cache

### 📁 openrouter.py
- **Functions**: _load_cache

### 📁 test_direct_extraction.py
- **Functions**: test_direct_extraction

**Summary**: 15 functions, 0 classes across 11 files
*To request specific implementations, ask: "Show me the implementation of [function_name]"*

