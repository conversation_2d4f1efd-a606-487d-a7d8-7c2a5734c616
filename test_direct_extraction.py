#!/usr/bin/env python3
"""
Direct test of the AiderIntegrationService extraction to verify the fix.
"""

import sys
import os
sys.path.insert(0, 'aider-main')

from aider.context_request.aider_integration_service import AiderIntegrationService

def test_direct_extraction():
    """Test direct extraction using AiderIntegrationService."""
    print("🔍 Testing direct AiderIntegrationService extraction...")
    
    # Initialize service
    service = AiderIntegrationService()
    
    # Test parameters
    project_path = "."
    file_path = "context_request_handler.py"
    symbol_name = "ContextRequestHandler"
    
    print(f"\n📁 File: {file_path}")
    print(f"🎯 Symbol: {symbol_name}")
    print(f"📂 Project: {project_path}")
    
    # Test extraction
    try:
        content = service.extract_symbol_content(symbol_name, file_path, project_path)
        
        if content:
            lines = content.split('\n')
            method_count = content.count('def ')
            
            print(f"\n✅ Extraction successful!")
            print(f"   Content length: {len(content)} characters")
            print(f"   Total lines: {len(lines)}")
            print(f"   Method definitions: {method_count}")
            print(f"   First line: {lines[0] if lines else 'N/A'}")
            print(f"   Last line: {lines[-1] if lines else 'N/A'}")
            
            if method_count > 0:
                print("🎉 SUCCESS: Full class implementation extracted!")
            else:
                print("❌ ISSUE: Only class signature extracted!")
                
            # Show preview
            print(f"\nContent preview (first 500 chars):")
            print("-" * 60)
            print(content[:500])
            if len(content) > 500:
                print("... [truncated]")
            print("-" * 60)
            
        else:
            print("❌ Extraction failed - no content returned")
            
    except Exception as e:
        print(f"❌ Extraction failed with error: {e}")

if __name__ == "__main__":
    test_direct_extraction()
