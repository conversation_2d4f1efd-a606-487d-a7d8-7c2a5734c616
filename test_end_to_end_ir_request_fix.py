#!/usr/bin/env python3
"""
End-to-end test for the IR_REQUEST format fix using the exact problematic format.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

def test_problematic_format():
    """Test the exact problematic format that was reported."""
    
    print("🧪 Testing End-to-End IR_REQUEST Fix")
    print("=" * 50)
    
    # The exact problematic format that was reported
    problematic_content = '''{"IR_REQUEST": {"focus_entities": ["process_all_positions", "close_all_positions", "update_positions"], "task_type": "verification"}}'''
    
    print(f"📋 Testing problematic format:")
    print(f"   {problematic_content}")
    print()
    
    try:
        # Import the base coder to test the actual parsing logic
        from aider.coders.base_coder import Coder
        from aider.io import InputOutput
        
        # Create a mock IO object
        io = InputOutput()
        
        # Create a mock coder instance
        coder = Coder(main_model=None, edit_format="whole", io=io, fnames=[])
        
        # Test the process_ir_requests method directly
        print("🔍 Testing process_ir_requests method...")
        
        # Mock user message
        user_message = "Please analyze these functions"
        
        # Call the method with the problematic content
        cleaned_content, augmented_prompt = coder.process_ir_requests(problematic_content, user_message)
        
        if augmented_prompt is not None:
            print("✅ IR_REQUEST was successfully detected and processed!")
            print(f"   Cleaned content: {cleaned_content[:100]}...")
            print(f"   Augmented prompt generated: {len(augmented_prompt)} characters")
            return True
        else:
            print("❌ IR_REQUEST was not detected or processed")
            print(f"   Returned content: {cleaned_content}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   This is expected if aider modules are not available")
        
        # Fallback: test just the regex patterns
        print("\n🔄 Fallback: Testing regex patterns directly...")
        return test_regex_patterns_directly(problematic_content)
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_regex_patterns_directly(content):
    """Test the regex patterns directly without importing aider."""
    
    import re
    import json
    
    # The exact patterns from our fix
    patterns = [
        # Standard JSON format: {"IR_REQUEST": {...}}
        r'\{\s*"IR_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_REQUEST": {...}}
        # Simplified format: {IR_REQUEST: {...}}
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    print(f"📋 Testing content: {content}")
    
    for i, pattern in enumerate(patterns, 1):
        match = re.search(pattern, content, re.DOTALL)
        if match:
            captured = match.group(1).strip()
            print(f"✅ Pattern {i} matched: {repr(captured)}")
            
            try:
                parsed = json.loads(captured)
                focus_entities = parsed.get("focus_entities", [])
                task_type = parsed.get("task_type", "")
                
                print(f"✅ JSON parsed successfully!")
                print(f"   focus_entities: {focus_entities}")
                print(f"   task_type: {task_type}")
                
                # Verify the expected values
                expected_entities = ["process_all_positions", "close_all_positions", "update_positions"]
                expected_task = "verification"
                
                if focus_entities == expected_entities and task_type == expected_task:
                    print("✅ VALIDATION SUCCESS: All values match expected results")
                    return True
                else:
                    print("❌ VALIDATION FAILED: Values don't match expected results")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON parsing failed: {e}")
                continue
    
    print("❌ NO PATTERN MATCHED")
    return False

def test_working_format():
    """Test the format that was working before."""
    
    print("\n🔍 Testing the format that was working before...")
    
    working_content = '''{{IR_REQUEST: {{ "focus_entities": ["keyword1", "keyword2"], "task_type": "verification" }}}}'''
    
    return test_regex_patterns_directly(working_content)

if __name__ == "__main__":
    print("🚀 IR_REQUEST Format Fix - End-to-End Test")
    print("=" * 60)
    
    # Test the problematic format
    problematic_success = test_problematic_format()
    
    # Test the working format to ensure we didn't break it
    working_success = test_working_format()
    
    print("\n📊 Final Results:")
    print(f"   Problematic format (Standard JSON): {'✅ FIXED' if problematic_success else '❌ STILL BROKEN'}")
    print(f"   Working format (Simplified): {'✅ STILL WORKS' if working_success else '❌ BROKEN'}")
    
    if problematic_success and working_success:
        print("\n🎉 SUCCESS! Both formats now work correctly!")
        print("✅ LLMs can use standard JSON format: {\"IR_REQUEST\": {...}}")
        print("✅ Simplified format still works: {{IR_REQUEST: {{ ... }}}}")
    else:
        print("\n❌ Some issues remain with format compatibility")
