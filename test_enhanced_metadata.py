"""
Test Enhanced Metadata in LLM Packages
Test that the enhanced metadata is correctly included in generated LLM packages.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest


def test_enhanced_metadata():
    """Test that enhanced metadata is included in LLM packages."""
    print("🔧 Testing Enhanced Metadata in LLM Packages")
    print("=" * 70)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Create a test request
    ir_request = IRContextRequest(
        user_query="How does the context_request_handler work?",
        task_description="Analyze context request handler functionality",
        task_type="workflow_analysis",
        focus_entities=["context_request_handler"],
        max_tokens=8000,
        llm_friendly=True,
        include_ir_slices=True,
        include_code_context=True,
        max_output_chars=30000,
        max_entities=6
    )
    
    try:
        print(f"🚀 Processing enhanced metadata test...")
        
        # Process the request
        result = handler.process_ir_context_request(ir_request)
        
        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        
        if 'llm_friendly_package' not in result:
            print(f"   ❌ No LLM package generated")
            return False
        
        package = result['llm_friendly_package']
        print(f"   ✅ Generated package: {len(package):,} characters")
        
        # Check for enhanced metadata fields
        metadata_fields = [
            '**Type**:',
            '**Module**:',
            '**Line**:',
            '**Cluster**:',
            '**Criticality**:',
            '**Change Risk**:',
            '**Relevance Score**:',
            '**Calls**:',
            '**Used By**:'
        ]
        
        inheritance_fields = [
            '**Inherits From**:',
            '**Belongs to Class**:',
            '**Overrides**:',
            '**Calls Super**:'
        ]
        
        # Check for type icons
        type_icons = ['🏛️', '⚙️', '🔧', '📊', '🔒', '🏷️', '📄']
        
        found_metadata = []
        found_inheritance = []
        found_icons = []
        
        lines = package.split('\n')
        for line in lines:
            # Check metadata fields
            for field in metadata_fields:
                if field in line:
                    found_metadata.append(field)
            
            # Check inheritance fields
            for field in inheritance_fields:
                if field in line:
                    found_inheritance.append(field)
            
            # Check type icons
            for icon in type_icons:
                if icon in line:
                    found_icons.append(icon)
        
        # Remove duplicates
        found_metadata = list(set(found_metadata))
        found_inheritance = list(set(found_inheritance))
        found_icons = list(set(found_icons))
        
        print(f"\n📊 Metadata Analysis:")
        print(f"   ✅ Found {len(found_metadata)}/{len(metadata_fields)} metadata fields:")
        for field in found_metadata:
            print(f"      • {field}")
        
        if found_inheritance:
            print(f"   🏛️ Found {len(found_inheritance)} inheritance fields:")
            for field in found_inheritance:
                print(f"      • {field}")
        
        if found_icons:
            print(f"   🎨 Found {len(found_icons)} type icons:")
            for icon in found_icons:
                print(f"      • {icon}")
        
        # Show a sample component with enhanced metadata
        print(f"\n📄 Sample Component with Enhanced Metadata:")
        print("-" * 60)
        
        # Find the first component section
        in_component = False
        component_lines = []
        for line in lines:
            if line.startswith('### ') and any(icon in line for icon in type_icons):
                in_component = True
                component_lines = [line]
            elif in_component:
                if line.startswith('### ') or line.startswith('---'):
                    break
                component_lines.append(line)
        
        # Show first 15 lines of the component
        for line in component_lines[:15]:
            print(line)
        
        if len(component_lines) > 15:
            print("... (truncated)")
        
        print("-" * 60)
        
        # Evaluate completeness
        metadata_score = len(found_metadata) / len(metadata_fields)
        has_icons = len(found_icons) > 0
        has_inheritance = len(found_inheritance) > 0
        
        print(f"\n🎯 Enhancement Results:")
        print(f"   • Metadata Completeness: {metadata_score:.1%}")
        print(f"   • Type Icons Present: {'✅ Yes' if has_icons else '❌ No'}")
        print(f"   • Inheritance Info: {'✅ Yes' if has_inheritance else '⚠️ None found'}")
        
        # Save enhanced package for inspection
        filename = "test_enhanced_metadata_package.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(package)
        print(f"   💾 Enhanced package saved: {filename}")
        
        # Success criteria
        success = (
            metadata_score >= 0.7 and  # At least 70% of metadata fields
            has_icons and              # Type icons present
            len(package) > 1000        # Reasonable package size
        )
        
        if success:
            print(f"\n🎉 ENHANCED METADATA WORKING!")
            print(f"   ✅ Components now include comprehensive metadata")
            print(f"   ✅ Type information with visual icons")
            print(f"   ✅ Line numbers and module information")
            print(f"   ✅ Criticality and change risk assessment")
            print(f"   ✅ Architectural cluster information")
            print(f"   ✅ Dependency relationships")
            if has_inheritance:
                print(f"   ✅ Inheritance and OOP information")
        else:
            print(f"\n⚠️ ENHANCEMENT INCOMPLETE:")
            if metadata_score < 0.7:
                print(f"   • Low metadata completeness: {metadata_score:.1%}")
            if not has_icons:
                print(f"   • Missing type icons")
            if len(package) <= 1000:
                print(f"   • Package too small: {len(package)} chars")
        
        return success
        
    except Exception as e:
        print(f"   ❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_metadata_comparison():
    """Compare old vs new metadata format."""
    print(f"\n📊 Testing Metadata Format Comparison")
    print("=" * 70)
    
    # Check if we have any old packages to compare
    ica_dir = "ICA_package"
    if os.path.exists(ica_dir):
        files = [f for f in os.listdir(ica_dir) if f.endswith('.txt')]
        if files:
            # Get the most recent file
            latest_file = max(files, key=lambda f: os.path.getctime(os.path.join(ica_dir, f)))
            file_path = os.path.join(ica_dir, latest_file)
            
            print(f"   📄 Analyzing latest package: {latest_file}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for enhanced metadata
            enhanced_indicators = [
                '**Type**:',
                '**Module**:',
                '**Line**:',
                '**Cluster**:',
                '🏛️', '⚙️', '🔧'  # Type icons
            ]
            
            found_enhanced = sum(1 for indicator in enhanced_indicators if indicator in content)
            enhancement_ratio = found_enhanced / len(enhanced_indicators)
            
            print(f"   📊 Enhancement indicators found: {found_enhanced}/{len(enhanced_indicators)}")
            print(f"   📈 Enhancement ratio: {enhancement_ratio:.1%}")
            
            if enhancement_ratio >= 0.5:
                print(f"   ✅ Package appears to use enhanced metadata format")
                return True
            else:
                print(f"   ⚠️ Package may be using old metadata format")
                return False
        else:
            print(f"   ⚠️ No packages found in {ica_dir}")
            return False
    else:
        print(f"   ⚠️ ICA_package directory not found")
        return False


if __name__ == "__main__":
    print("🔧 Testing Enhanced Metadata in LLM Package Generation")
    print("=" * 70)
    
    # Test 1: Enhanced metadata generation
    test1_passed = test_enhanced_metadata()
    
    # Test 2: Metadata format comparison
    test2_passed = test_metadata_comparison()
    
    print(f"\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"   Enhanced Metadata Generation: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Metadata Format Comparison: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 ENHANCED METADATA WORKING PERFECTLY!")
        print(f"   ✅ Components now include comprehensive information")
        print(f"   ✅ Type, line numbers, modules, clusters")
        print(f"   ✅ Criticality and change risk assessment")
        print(f"   ✅ Visual type icons for better readability")
        print(f"   ✅ Inheritance and OOP relationships")
        print(f"   ✅ Dependency information with call graphs")
        print(f"\n💡 LLMs now receive much richer context with:")
        print(f"   • Complete entity metadata")
        print(f"   • Architectural positioning")
        print(f"   • Risk and criticality assessment")
        print(f"   • Visual organization with icons")
    else:
        print(f"\n❌ ENHANCED METADATA ISSUES DETECTED!")
        print(f"   Check the failed tests above for details")
        print(f"   The metadata enhancement may need debugging")
