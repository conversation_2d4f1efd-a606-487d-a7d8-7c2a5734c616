#!/usr/bin/env python3
"""
Test Enhanced Query Intent Classification

This script tests the enhanced query intent analyzer to ensure it correctly
classifies Aider-specific queries as workflow_analysis instead of component_discovery.
"""

import sys
import os
sys.path.append('aider-main/aider/context_request')

from query_intent_analyzer import QueryIntentAnalyzer
from intelligent_context_models import QueryIntent


def test_aider_query_classification():
    """
    Test the enhanced query intent analyzer with real Aider queries.
    """
    print("🧪 Testing Enhanced Query Intent Classification")
    print("=" * 60)
    
    analyzer = QueryIntentAnalyzer()
    
    # Test queries that were previously misclassified
    test_cases = [
        {
            'query': 'How does Aider apply edits to files?',
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'File editing workflow'
        },
        {
            'query': "How does Aider's chat loop work?",
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'Chat loop workflow'
        },
        {
            'query': 'How does <PERSON><PERSON> gather context from the repository?',
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'Context gathering workflow'
        },
        {
            'query': 'How does Aid<PERSON> interact with LLM APIs?',
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'LLM integration workflow'
        },
        {
            'query': 'How does Aider handle Git operations?',
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'Git operations workflow'
        },
        {
            'query': 'How does Aider handle different edit formats?',
            'expected': QueryIntent.WORKFLOW_ANALYSIS,
            'description': 'Edit format handling workflow'
        },
        {
            'query': 'How does Aider handle errors and exceptions?',
            'expected': QueryIntent.DEBUGGING_ASSISTANCE,
            'description': 'Error handling (debugging)'
        },
        {
            'query': "What is Aider's overall architecture?",
            'expected': QueryIntent.ARCHITECTURE_UNDERSTANDING,
            'description': 'System architecture overview'
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case['query']
        expected = test_case['expected']
        description = test_case['description']
        
        print(f"\n🔍 Test {i}: {description}")
        print(f"   Query: {query}")
        
        # Analyze the query
        context = analyzer.analyze_complete_query(query)
        actual = context.intent
        
        # Check if classification is correct
        is_correct = actual == expected
        status = "✅ CORRECT" if is_correct else "❌ WRONG"
        
        print(f"   Expected: {expected.value}")
        print(f"   Actual: {actual.value}")
        print(f"   Status: {status}")
        print(f"   Confidence: {context.confidence:.2f}")
        
        results.append({
            'query': query,
            'expected': expected.value,
            'actual': actual.value,
            'correct': is_correct,
            'confidence': context.confidence
        })
    
    # Summary
    print("\n📊 CLASSIFICATION RESULTS")
    print("=" * 60)
    
    correct_count = sum(1 for r in results if r['correct'])
    total_count = len(results)
    accuracy = correct_count / total_count
    
    print(f"✅ Correct Classifications: {correct_count}/{total_count}")
    print(f"📈 Accuracy: {accuracy:.1%}")
    print(f"📊 Average Confidence: {sum(r['confidence'] for r in results) / len(results):.2f}")
    
    # Show misclassifications
    misclassified = [r for r in results if not r['correct']]
    if misclassified:
        print(f"\n❌ MISCLASSIFIED QUERIES ({len(misclassified)}):")
        for r in misclassified:
            print(f"   • {r['query']}")
            print(f"     Expected: {r['expected']} | Got: {r['actual']}")
    else:
        print("\n🎉 ALL QUERIES CORRECTLY CLASSIFIED!")
    
    return accuracy >= 0.8  # 80% accuracy threshold


def test_specific_patterns():
    """
    Test specific pattern matching for debugging.
    """
    print("\n🔍 Testing Specific Pattern Matching")
    print("=" * 60)
    
    analyzer = QueryIntentAnalyzer()
    
    # Test the problematic query
    query = "How does Aider apply edits to files?"
    print(f"Query: {query}")
    
    # Check what patterns match
    query_lower = query.lower()
    
    # Check workflow patterns
    workflow_patterns = analyzer.intent_patterns.get(QueryIntent.WORKFLOW_ANALYSIS, [])
    print(f"\nWorkflow patterns: {workflow_patterns}")
    
    for pattern in workflow_patterns:
        import re
        if re.search(pattern, query_lower):
            print(f"   ✅ Matches: {pattern}")
        else:
            print(f"   ❌ No match: {pattern}")
    
    # Check enhanced heuristics
    print(f"\nEnhanced heuristics:")
    
    # Aider-specific phrases
    aider_phrases = ['apply edits', 'edit files', 'file editing', 'edits to files']
    for phrase in aider_phrases:
        if phrase in query_lower:
            print(f"   ✅ Found Aider phrase: '{phrase}'")
    
    # Workflow words
    workflow_words = ['apply', 'edit', 'send', 'handle', 'process', 'work', 'loop', 'run']
    found_words = [word for word in workflow_words if word in query_lower]
    if found_words:
        print(f"   ✅ Found workflow words: {found_words}")
    
    # Full analysis
    context = analyzer.analyze_complete_query(query)
    print(f"\nFinal classification: {context.intent.value}")
    print(f"Confidence: {context.confidence:.2f}")


def main():
    """
    Main test function.
    """
    print("🎯 Enhanced Query Intent Classification Test")
    print("=" * 70)
    
    # Test specific patterns first
    test_specific_patterns()
    
    # Test full classification
    success = test_aider_query_classification()
    
    if success:
        print("\n🎉 SUCCESS: Enhanced query intent classification is working!")
        print("✅ File editing queries should now be classified as workflow_analysis")
        print("✅ This should trigger the enhanced scoring algorithm")
    else:
        print("\n❌ FAILURE: Query intent classification needs more work")
        print("⚠️  Some queries are still being misclassified")
    
    return success


if __name__ == "__main__":
    main()
