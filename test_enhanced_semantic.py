#!/usr/bin/env python3
"""
Test the enhanced semantic intelligence system with IR-powered analysis
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_enhanced_semantic_system():
    """Test the enhanced semantic system with fallback to IR-powered scoring"""
    print("🧠 TESTING ENHANCED SEMANTIC INTELLIGENCE SYSTEM")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Create a mock IR dataset that won't trigger nuclear option
        mock_ir_data = {
            "modules": [
                {
                    "name": "base_coder",
                    "entities": [
                        {
                            "name": "process_files", 
                            "type": "function", 
                            "file_path": "base_coder.py",
                            "calls": ["read_file", "write_file"],
                            "used_by": ["main_loop"],
                            "complexity": 8,
                            "side_effects": ["file_io"],
                            "criticality_score": 7
                        },
                        {
                            "name": "handle_errors", 
                            "type": "function", 
                            "file_path": "error_handler.py",
                            "calls": ["log_error", "send_notification"],
                            "used_by": ["process_files", "main_loop"],
                            "complexity": 5,
                            "side_effects": ["network_io", "logging"],
                            "criticality_score": 9
                        },
                        {
                            "name": "FileManager", 
                            "type": "class", 
                            "file_path": "file_manager.py",
                            "calls": [],
                            "used_by": ["process_files"],
                            "complexity": 12,
                            "side_effects": ["file_io"],
                            "criticality_score": 6
                        },
                        {
                            "name": "config_data", 
                            "type": "variable", 
                            "file_path": "config.py",
                            "calls": [],
                            "used_by": ["process_files", "handle_errors"],
                            "complexity": 1,
                            "side_effects": [],
                            "criticality_score": 3
                        },
                    ]
                }
            ]
        }
        
        print(f"📊 Mock IR data created with {len(mock_ir_data['modules'][0]['entities'])} entities")
        
        # Test the selector with a query that won't trigger nuclear option
        selector = HierarchicalContextSelector()
        
        # Test 1: Query that should use semantic analysis (no exact matches)
        print(f"\n🧪 TEST 1: Semantic analysis query (no nuclear option)")
        result1 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='What components handle file processing workflows?',  # No exact matches
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities1 = result1.get('selected_entities', [])
        print(f"   Selected {len(selected_entities1)} entities:")
        
        found_process_files = False
        found_file_manager = False
        for entity in selected_entities1:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            print(f"   - {name}: {score:.1f}")
            
            if 'process_files' in name.lower():
                found_process_files = True
                print(f"     ✅ FOUND process_files!")
            if 'filemanager' in name.lower():
                found_file_manager = True
                print(f"     ✅ FOUND FileManager!")
        
        # Test 2: Query that should prioritize high-criticality entities
        print(f"\n🧪 TEST 2: Error handling query (should find high-criticality)")
        result2 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='How are system failures managed?',  # Should find handle_errors (criticality=9)
            focus_entities=None,
            max_entities=2
        )
        
        selected_entities2 = result2.get('selected_entities', [])
        print(f"   Selected {len(selected_entities2)} entities:")
        
        found_handle_errors = False
        for entity in selected_entities2:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            print(f"   - {name}: {score:.1f}")
            
            if 'handle_errors' in name.lower():
                found_handle_errors = True
                print(f"     ✅ FOUND handle_errors (high criticality)!")
        
        # Test 3: Query that should deprioritize variables
        print(f"\n🧪 TEST 3: Variable deprioritization test")
        result3 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='What configuration settings are used?',  # Should find config_data but rank it low
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities3 = result3.get('selected_entities', [])
        print(f"   Selected {len(selected_entities3)} entities:")
        
        variable_rank = None
        for i, entity in enumerate(selected_entities3):
            name = entity.get('name', '')
            entity_type = entity.get('type', '')
            score = entity.get('relevance_score', 0)
            print(f"   {i+1}. {name} ({entity_type}): {score:.1f}")
            
            if name == 'config_data':
                variable_rank = i + 1
                print(f"     📊 config_data ranked #{variable_rank}")
        
        # Summary
        print(f"\n📊 ENHANCED SEMANTIC INTELLIGENCE TEST SUMMARY:")
        print(f"   Test 1 (file processing): {'✅ PASS' if found_process_files or found_file_manager else '❌ FAIL'}")
        print(f"   Test 2 (error handling): {'✅ PASS' if found_handle_errors else '❌ FAIL'}")
        print(f"   Test 3 (variable ranking): {'✅ PASS' if variable_rank and variable_rank > 1 else '❌ FAIL'} (variable ranked #{variable_rank})")
        
        if (found_process_files or found_file_manager) and found_handle_errors and (variable_rank and variable_rank > 1):
            print(f"\n🎉 ENHANCED SEMANTIC INTELLIGENCE IS WORKING!")
            print(f"   The system is now using IR data for semantic analysis!")
        else:
            print(f"\n❌ ENHANCED SEMANTIC INTELLIGENCE NEEDS MORE WORK!")
            print(f"   The IR-powered analysis is not working as expected.")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_semantic_methods_directly():
    """Test the semantic methods directly to ensure they work"""
    print(f"\n🧠 TESTING SEMANTIC METHODS DIRECTLY")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        selector = HierarchicalContextSelector()
        
        # Mock IR data
        ir_data = {
            "modules": [
                {
                    "name": "test_module",
                    "entities": [
                        {
                            "name": "process_files",
                            "type": "function",
                            "complexity": 8,
                            "side_effects": ["file_io"],
                            "criticality_score": 7
                        }
                    ]
                }
            ]
        }
        
        # Test semantic relevance
        print(f"\n🧪 Testing _compute_semantic_relevance...")
        semantic_score = selector._compute_semantic_relevance(
            "How does file processing work?", 
            "process_files", 
            "function", 
            ir_data
        )
        print(f"   Semantic score: {semantic_score:.1f}")
        
        # Test contextual importance
        print(f"\n🧪 Testing _analyze_contextual_importance...")
        importance_score = selector._analyze_contextual_importance(
            "process_files", 
            "function", 
            ir_data
        )
        print(f"   Importance score: {importance_score:.1f}")
        
        # Test file context
        print(f"\n🧪 Testing _analyze_file_context...")
        file_score = selector._analyze_file_context(
            "file_manager.py", 
            "process_files"
        )
        print(f"   File context score: {file_score:.1f}")
        
        print(f"\n✅ All semantic methods are callable!")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_semantic_system()
    test_semantic_methods_directly()
    
    print(f"\n🎯 ENHANCED SEMANTIC INTELLIGENCE TEST COMPLETE")
