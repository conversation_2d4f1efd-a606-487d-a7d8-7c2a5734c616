#!/usr/bin/env python3
"""
Test Enhanced Semantic Intelligence System
Verify the new logical rules + relationship awareness implementation
"""

import sys
import os
import json
from pathlib import Path

# Add the aider-main directory to Python path
aider_main_path = Path(__file__).parent / "aider-main"
sys.path.insert(0, str(aider_main_path))

from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
from aider.context_request.intelligent_context_models import QueryContext, QueryIntent, QueryScope

def load_test_ir_data():
    """Load IR data for testing"""
    ir_files = list(Path(".").glob("ir_data_*.json"))
    if not ir_files:
        print("❌ No IR data files found. Please run IR generation first.")
        return None
    
    # Use the most recent IR file
    latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
    print(f"📊 Loading IR data from: {latest_ir_file}")
    
    with open(latest_ir_file, 'r') as f:
        return json.load(f)

def create_test_query_context(query: str) -> QueryContext:
    """Create a test query context"""
    return QueryContext(
        intent=QueryIntent.DEBUGGING_ASSISTANCE,
        scope=QueryScope.MULTI_COMPONENT,
        confidence=0.8,
        keywords=[],
        entities=[],
        original_query=query
    )

def test_semantic_intelligence():
    """Test the enhanced semantic intelligence system"""
    print("🧠 TESTING ENHANCED SEMANTIC INTELLIGENCE SYSTEM")
    print("=" * 60)
    
    # Load IR data
    ir_data = load_test_ir_data()
    if not ir_data:
        return
    
    # Initialize the hierarchical context selector
    selector = HierarchicalContextSelector()
    
    # Test queries that should benefit from relationship awareness
    test_queries = [
        {
            "query": "Why is my context selection taking so long?",
            "description": "Performance debugging query - should find context processing entities",
            "expected_entities": ["process_context_request", "select_optimal_context", "context_request"]
        },
        {
            "query": "How does user authentication work?",
            "description": "Workflow query - should find auth-related classes and methods",
            "expected_entities": ["authenticate", "login", "user", "auth"]
        },
        {
            "query": "What classes handle file processing?",
            "description": "Architecture query - should find file-related classes with methods",
            "expected_entities": ["file", "process", "handler", "manager"]
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n🧪 TEST {i}: {test_case['description']}")
        print(f"Query: '{test_case['query']}'")
        print("-" * 50)
        
        # Create query context
        query_context = create_test_query_context(test_case['query'])
        
        # Run hierarchical context selection
        try:
            result = selector.select_hierarchical_context(
                ir_data=ir_data,
                user_query=test_case['query'],
                focus_entities=None,
                max_entities=8
            )
            
            # Analyze results
            selected_entities = result.get('selected_entities', [])
            print(f"\n📊 RESULTS: {len(selected_entities)} entities selected")
            
            # Show top entities with scores
            for j, entity in enumerate(selected_entities[:5], 1):
                name = entity.get('name', 'unknown')
                score = entity.get('relevance_score', 0)
                entity_type = entity.get('type', 'unknown')
                cluster = entity.get('cluster', 'unknown')
                print(f"   {j}. {name} ({entity_type}) - Score: {score:.2f} - Cluster: {cluster}")
            
            # Check if expected entities are found
            found_expected = 0
            for expected in test_case['expected_entities']:
                for entity in selected_entities:
                    if expected.lower() in entity.get('name', '').lower():
                        found_expected += 1
                        break
            
            relevance_ratio = found_expected / len(test_case['expected_entities'])
            print(f"\n✅ Relevance Check: {found_expected}/{len(test_case['expected_entities'])} expected entities found ({relevance_ratio:.1%})")
            
            # Show architectural explanation
            explanation = result.get('architectural_explanation', '')
            if explanation:
                print(f"\n🏗️ Architectural Context:")
                print(explanation[:300] + "..." if len(explanation) > 300 else explanation)
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()

def test_relationship_boost():
    """Test the core relationship boost functionality"""
    print("\n🔗 TESTING RELATIONSHIP BOOST FUNCTIONALITY")
    print("=" * 60)
    
    # Load IR data
    ir_data = load_test_ir_data()
    if not ir_data:
        return
    
    # Initialize selector
    selector = HierarchicalContextSelector()
    
    # Test the relationship boost method directly
    test_cases = [
        {
            "entity_name": "BaseCoder",
            "entity_type": "class",
            "query_keywords": ["context", "request", "process"],
            "description": "Class with methods that should match keywords"
        },
        {
            "entity_name": "process_context_request",
            "entity_type": "method",
            "query_keywords": ["context", "selection", "slow"],
            "description": "Method that calls other relevant functions"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['entity_name']} ({test_case['entity_type']})")
        print(f"Description: {test_case['description']}")
        print(f"Keywords: {test_case['query_keywords']}")
        
        try:
            boost_score = selector._calculate_relationship_boost(
                entity_name=test_case['entity_name'],
                entity_type=test_case['entity_type'],
                query_keywords=test_case['query_keywords'],
                ir_data=ir_data
            )
            
            print(f"🔗 Relationship Boost Score: {boost_score:.2f}")
            
        except Exception as e:
            print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    print("🚀 ENHANCED SEMANTIC INTELLIGENCE TEST SUITE")
    print("Testing the new logical rules + relationship awareness system")
    print("=" * 80)
    
    test_semantic_intelligence()
    test_relationship_boost()
    
    print("\n🎯 TEST COMPLETE")
    print("The enhanced system should show:")
    print("✅ Better relevance scores for connected entities")
    print("✅ Class-method clustering boosts")
    print("✅ Function call relationship awareness")
    print("✅ Architectural significance detection")
    print("✅ Query intent-based selection strategies")
