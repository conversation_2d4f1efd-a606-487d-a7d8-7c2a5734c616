#!/usr/bin/env python3
"""
Final verification test for the IR_REQUEST format fix.
Tests both the exact problematic format and the working format.
"""

import re
import json

def test_all_formats():
    """Test all supported IR_REQUEST formats."""
    
    print("🎯 Final IR_REQUEST Format Fix Verification")
    print("=" * 60)
    
    # Test cases covering all scenarios
    test_cases = [
        {
            "name": "❌ PROBLEMATIC FORMAT (Standard JSON) - NOW FIXED",
            "content": '{"IR_REQUEST": {"focus_entities": ["process_all_positions", "close_all_positions", "update_positions"], "task_type": "verification"}}',
            "expected_entities": ["process_all_positions", "close_all_positions", "update_positions"],
            "expected_task": "verification",
            "should_work": True
        },
        {
            "name": "✅ WORKING FORMAT (Double Braces) - STILL WORKS",
            "content": '{{IR_REQUEST: {{ "focus_entities": ["keyword1", "keyword2"], "task_type": "verification" }}}}',
            "expected_entities": ["keyword1", "keyword2"],
            "expected_task": "verification",
            "should_work": True
        },
        {
            "name": "Standard JSON with whitespace",
            "content": '{ "IR_REQUEST" : { "focus_entities": ["cache", "performance"], "task_type": "debugging" } }',
            "expected_entities": ["cache", "performance"],
            "expected_task": "debugging",
            "should_work": True
        },
        {
            "name": "Standard JSON embedded in text",
            "content": 'I need more context. {"IR_REQUEST": {"focus_entities": ["extract", "symbol"], "task_type": "feature_development"}} Please provide this.',
            "expected_entities": ["extract", "symbol"],
            "expected_task": "feature_development",
            "should_work": True
        },
        {
            "name": "Simplified format single brace",
            "content": '{IR_REQUEST: {"focus_entities": ["test"], "task_type": "debugging"}}',
            "expected_entities": ["test"],
            "expected_task": "debugging",
            "should_work": True
        },
        {
            "name": "Simplified format double brace (alternative)",
            "content": '{{IR_REQUEST: {"focus_entities": ["alt"], "task_type": "analysis"}}}}',
            "expected_entities": ["alt"],
            "expected_task": "analysis",
            "should_work": True
        }
    ]
    
    # Updated patterns from the fix
    patterns = [
        # Standard JSON format: {"IR_REQUEST": {...}}
        r'\{\s*"IR_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_REQUEST": {...}}
        # Simplified format with double braces: {{IR_REQUEST: {{ ... }}}}
        r'\{\{IR_REQUEST:\s*\{\{\s*(.*?)\s*\}\}\s*\}\}',  # {{IR_REQUEST: {{ ... }}}}
        # Simplified format: {IR_REQUEST: {...}}
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"   Content: {test_case['content']}")
        
        matched = False
        for j, pattern in enumerate(patterns, 1):
            match = re.search(pattern, test_case['content'], re.DOTALL)
            if match:
                captured = match.group(1).strip()
                print(f"   ✅ Pattern {j} matched: {repr(captured)}")
                
                # Handle double brace format
                json_content = captured
                if j == 2:  # Double brace pattern
                    json_content = "{" + captured + "}"
                
                try:
                    parsed = json.loads(json_content)
                    focus_entities = parsed.get("focus_entities", [])
                    task_type = parsed.get("task_type", "")
                    
                    print(f"   ✅ JSON parsed successfully!")
                    print(f"      focus_entities: {focus_entities}")
                    print(f"      task_type: {task_type}")
                    
                    # Verify expected results
                    if (focus_entities == test_case["expected_entities"] and 
                        task_type == test_case["expected_task"]):
                        print(f"   🎉 VALIDATION SUCCESS")
                        success_count += 1
                        matched = True
                        break
                    else:
                        print(f"   ❌ VALIDATION FAILED")
                        print(f"      Expected entities: {test_case['expected_entities']}")
                        print(f"      Expected task: {test_case['expected_task']}")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON parsing failed: {e}")
                    print(f"      Tried to parse: {repr(json_content)}")
                    continue
        
        if not matched:
            if test_case["should_work"]:
                print(f"   ❌ FAILED - Expected to work but no pattern matched")
            else:
                print(f"   ✅ CORRECTLY FAILED - Expected to fail")
                success_count += 1
    
    print(f"\n📊 Final Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Standard JSON format now works: {\"IR_REQUEST\": {...}}")
        print("✅ Double brace format still works: {{IR_REQUEST: {{ ... }}}}")
        print("✅ All edge cases handled correctly")
        print("\n🚀 The IR_REQUEST format compatibility issue is RESOLVED!")
        return True
    else:
        print(f"\n❌ {total_count - success_count} tests failed")
        print("Some format compatibility issues remain.")
        return False

def show_usage_examples():
    """Show usage examples for both formats."""
    
    print("\n📚 Usage Examples")
    print("=" * 40)
    
    print("\n✅ STANDARD JSON FORMAT (LLM Preferred):")
    print('{"IR_REQUEST": {"focus_entities": ["function_name", "class_name"], "task_type": "debugging"}}')
    
    print("\n✅ SIMPLIFIED FORMAT (Original):")
    print('{{IR_REQUEST: {{ "focus_entities": ["function_name", "class_name"], "task_type": "debugging" }}}}')
    
    print("\n📝 Supported task_types:")
    print("   - debugging")
    print("   - verification") 
    print("   - feature_development")
    print("   - general_analysis")
    print("   - code_review")
    print("   - refactoring")

if __name__ == "__main__":
    success = test_all_formats()
    show_usage_examples()
    
    if success:
        print("\n🎯 CONCLUSION: IR_REQUEST format fix is working perfectly!")
        print("   LLMs can now use either format successfully.")
    else:
        print("\n❌ CONCLUSION: Some issues remain with the format fix.")
