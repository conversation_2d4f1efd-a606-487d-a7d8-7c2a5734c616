#!/usr/bin/env python3
"""
Final verification that the enhanced semantic intelligence system is working
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_context_packages_change_with_queries():
    """Test that context packages actually change based on different user queries"""
    print("🎯 FINAL VERIFICATION: Context packages change with queries")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Create a comprehensive mock IR dataset
        mock_ir_data = {
            "modules": [
                {
                    "name": "aider_core",
                    "entities": [
                        # Edit-related entities
                        {
                            "name": "apply_edits", 
                            "type": "function", 
                            "file_path": "base_coder.py",
                            "calls": ["validate_edit", "write_file"],
                            "used_by": ["main_loop"],
                            "complexity": 9,
                            "side_effects": ["file_io"],
                            "criticality_score": 8
                        },
                        {
                            "name": "edit_files", 
                            "type": "function", 
                            "file_path": "file_editor.py",
                            "calls": ["backup_file", "apply_changes"],
                            "used_by": ["apply_edits"],
                            "complexity": 7,
                            "side_effects": ["file_io"],
                            "criticality_score": 7
                        },
                        # Chat-related entities
                        {
                            "name": "run", 
                            "type": "function", 
                            "file_path": "main.py",
                            "calls": ["get_input", "send_message"],
                            "used_by": [],
                            "complexity": 12,
                            "side_effects": ["user_io"],
                            "criticality_score": 9
                        },
                        {
                            "name": "send_message", 
                            "type": "function", 
                            "file_path": "chat.py",
                            "calls": ["format_message", "api_call"],
                            "used_by": ["run"],
                            "complexity": 6,
                            "side_effects": ["network_io"],
                            "criticality_score": 8
                        },
                        # Git-related entities
                        {
                            "name": "GitRepo", 
                            "type": "class", 
                            "file_path": "git.py",
                            "calls": [],
                            "used_by": ["apply_edits"],
                            "complexity": 15,
                            "side_effects": ["file_io", "process_io"],
                            "criticality_score": 6
                        },
                        {
                            "name": "commit_changes", 
                            "type": "method", 
                            "file_path": "git.py",
                            "class_name": "GitRepo",
                            "calls": ["git_add", "git_commit"],
                            "used_by": ["apply_edits"],
                            "complexity": 8,
                            "side_effects": ["process_io"],
                            "criticality_score": 7
                        },
                    ]
                }
            ]
        }
        
        selector = HierarchicalContextSelector()
        
        # Test different queries and verify different results
        test_queries = [
            ("How does aider apply edits to files?", ["apply_edits", "edit_files"]),
            ("How does the chat loop work?", ["run", "send_message"]),
            ("What git operations are available?", ["GitRepo", "commit_changes"])
        ]
        
        results = {}
        
        for query, expected_entities in test_queries:
            print(f"\n🧪 TESTING: {query}")
            
            result = selector.select_hierarchical_context(
                ir_data=mock_ir_data,
                user_query=query,
                focus_entities=None,
                max_entities=3
            )
            
            selected_entities = result.get('selected_entities', [])
            entity_names = [e.get('name', '') for e in selected_entities]
            
            print(f"   Selected: {entity_names}")
            
            # Check if expected entities are found
            found_expected = []
            for expected in expected_entities:
                if any(expected.lower() in name.lower() for name in entity_names):
                    found_expected.append(expected)
            
            results[query] = {
                'selected': entity_names,
                'expected': expected_entities,
                'found': found_expected,
                'success': len(found_expected) > 0
            }
            
            print(f"   Expected: {expected_entities}")
            print(f"   Found: {found_expected}")
            print(f"   Success: {'✅' if len(found_expected) > 0 else '❌'}")
        
        # Verify that different queries produce different results
        all_selections = [set(r['selected']) for r in results.values()]
        unique_selections = len(set(frozenset(s) for s in all_selections))
        
        print(f"\n📊 FINAL VERIFICATION RESULTS:")
        print(f"   Total queries tested: {len(test_queries)}")
        print(f"   Unique result sets: {unique_selections}")
        print(f"   Different results per query: {'✅ YES' if unique_selections > 1 else '❌ NO'}")
        
        success_count = sum(1 for r in results.values() if r['success'])
        print(f"   Successful query matches: {success_count}/{len(test_queries)}")
        
        # Individual test results
        for query, result in results.items():
            short_query = query.split('?')[0] + '?'
            print(f"   {short_query}: {'✅ PASS' if result['success'] else '❌ FAIL'}")
        
        # Overall verdict
        if unique_selections > 1 and success_count >= 2:
            print(f"\n🎉 SUCCESS: CONTEXT PACKAGES CHANGE BASED ON USER QUERIES!")
            print(f"   The enhanced semantic intelligence system is working!")
            print(f"   Different queries produce different, relevant results!")
            return True
        else:
            print(f"\n❌ FAILURE: Context packages are not changing properly")
            print(f"   The system needs more work")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ir_data_utilization():
    """Test that IR data is actually being used for scoring"""
    print(f"\n🧠 TESTING IR DATA UTILIZATION")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        selector = HierarchicalContextSelector()
        
        # Test with IR data vs without IR data
        mock_ir_data = {
            "modules": [
                {
                    "name": "test",
                    "entities": [
                        {
                            "name": "high_criticality_func",
                            "type": "function",
                            "complexity": 10,
                            "criticality_score": 9,
                            "side_effects": ["file_io", "network_io"]
                        },
                        {
                            "name": "low_criticality_func",
                            "type": "function", 
                            "complexity": 3,
                            "criticality_score": 2,
                            "side_effects": []
                        }
                    ]
                }
            ]
        }
        
        # Test semantic intelligence directly
        print(f"🧪 Testing semantic intelligence with IR data...")
        
        high_score = selector._semantic_entity_intelligence(
            "high_criticality_func", "function", "test query", "test.py", mock_ir_data
        )
        
        low_score = selector._semantic_entity_intelligence(
            "low_criticality_func", "function", "test query", "test.py", mock_ir_data
        )
        
        print(f"   High criticality function score: {high_score:.1f}")
        print(f"   Low criticality function score: {low_score:.1f}")
        
        if high_score > low_score:
            print(f"   ✅ IR data is being used! High criticality scored higher.")
            return True
        else:
            print(f"   ❌ IR data is not being used properly.")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 FINAL VERIFICATION OF ENHANCED SEMANTIC INTELLIGENCE")
    print("=" * 80)
    
    test1_success = test_context_packages_change_with_queries()
    test2_success = test_ir_data_utilization()
    
    print(f"\n🏆 FINAL VERDICT:")
    print(f"   Context packages change with queries: {'✅' if test1_success else '❌'}")
    print(f"   IR data is being utilized: {'✅' if test2_success else '❌'}")
    
    if test1_success and test2_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   The enhanced semantic intelligence system is fully working!")
        print(f"   Context packages now change based on user queries!")
        print(f"   The 'amazing IR JSON' is finally being used properly!")
    else:
        print(f"\n❌ PARTIAL SUCCESS")
        print(f"   Some components are working but the system needs refinement.")
