#!/usr/bin/env python3
"""
Test that the hardcoded 3-entity limit is now fixed
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_fixed_entity_limit():
    """Test that we can now get more than 3 entities in the LLM package"""
    print("🚀 TESTING FIXED 3-ENTITY LIMIT")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test with higher max_entities to see if we get more than 3
        print("\n🧪 TEST: Request 10 entities with higher token budget")
        print("-" * 50)
        
        request = IRContextRequest(
            user_query="how does the context system work with integration?",
            task_description="Test fixed entity limit",
            task_type="general_analysis",
            max_tokens=25000,  # High token budget
            max_entities=10,   # Request 10 entities
            llm_friendly=True,
            max_output_chars=150000
        )
        
        print(f"   📊 Request settings:")
        print(f"      max_tokens: {request.max_tokens}")
        print(f"      max_entities: {request.max_entities}")
        print(f"      max_output_chars: {request.max_output_chars}")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
            return False
        
        # Analyze results
        ir_slices = result.get('ir_slices', [])
        llm_package = result.get('llm_friendly_package', '')
        token_utilization = result.get('summary', {}).get('token_utilization', 0)
        
        print(f"\n📊 RESULTS:")
        print(f"   Entities selected: {len(ir_slices)}")
        print(f"   Token utilization: {token_utilization}%")
        print(f"   Package size: {len(llm_package):,} characters")
        
        # Check the LLM package for the critical entities section
        lines = llm_package.split('\n')
        critical_entities_line = None
        
        for line in lines:
            if 'CRITICAL ENTITIES' in line and 'most important' in line:
                critical_entities_line = line
                break
        
        if critical_entities_line:
            print(f"\n🔍 CRITICAL ENTITIES SECTION:")
            print(f"   Found: {critical_entities_line.strip()}")
            
            # Extract the number from the line
            import re
            match = re.search(r'CRITICAL ENTITIES \((\d+) most important\)', critical_entities_line)
            if match:
                entities_in_package = int(match.group(1))
                print(f"   Entities in package: {entities_in_package}")
                
                if entities_in_package > 3:
                    print(f"   ✅ SUCCESS! Got {entities_in_package} entities (more than 3)")
                    
                    # Count actual entity sections
                    entity_sections = 0
                    for line in lines:
                        if re.match(r'^### \d+\.', line):
                            entity_sections += 1
                    
                    print(f"   📋 Actual entity sections found: {entity_sections}")
                    
                    if entity_sections >= entities_in_package:
                        print(f"   ✅ Entity sections match or exceed header count")
                        return True
                    else:
                        print(f"   ⚠️  Entity sections ({entity_sections}) less than header count ({entities_in_package})")
                        return False
                else:
                    print(f"   ❌ STILL LIMITED: Only got {entities_in_package} entities (still capped at 3)")
                    return False
            else:
                print(f"   ❌ Could not parse entity count from: {critical_entities_line}")
                return False
        else:
            print(f"   ❌ Could not find CRITICAL ENTITIES section in package")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_entity_limits():
    """Test with different max_entities values to verify dynamic behavior"""
    print(f"\n🧪 TEST: Different max_entities values")
    print("-" * 50)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        test_limits = [5, 8, 12]
        results = []
        
        for max_entities in test_limits:
            print(f"\n   Testing max_entities = {max_entities}")
            
            # Clear caches
            ContextRequestHandler._ir_cache.clear()
            ContextRequestHandler._ir_cache_timestamps.clear()
            handler = ContextRequestHandler('.')
            handler.cache.clear()
            
            request = IRContextRequest(
                user_query="context request handling system",
                task_description=f"Test {max_entities} entities",
                task_type="general_analysis",
                max_tokens=30000,
                max_entities=max_entities,
                llm_friendly=True,
                max_output_chars=200000
            )
            
            result = handler.process_ir_context_request(request)
            
            if 'error' not in result:
                ir_slices = result.get('ir_slices', [])
                llm_package = result.get('llm_friendly_package', '')
                
                # Extract entities from package
                import re
                critical_line = None
                for line in llm_package.split('\n'):
                    if 'CRITICAL ENTITIES' in line and 'most important' in line:
                        critical_line = line
                        break
                
                entities_in_package = 0
                if critical_line:
                    match = re.search(r'CRITICAL ENTITIES \((\d+) most important\)', critical_line)
                    if match:
                        entities_in_package = int(match.group(1))
                
                results.append({
                    'requested': max_entities,
                    'selected': len(ir_slices),
                    'in_package': entities_in_package
                })
                
                print(f"      Requested: {max_entities}, Selected: {len(ir_slices)}, In package: {entities_in_package}")
            else:
                print(f"      ERROR: {result['error']}")
                results.append({
                    'requested': max_entities,
                    'selected': 0,
                    'in_package': 0
                })
        
        print(f"\n📊 SUMMARY:")
        all_dynamic = True
        for result in results:
            print(f"   Requested {result['requested']} → Got {result['in_package']} in package")
            if result['in_package'] <= 3 and result['requested'] > 3:
                all_dynamic = False
        
        if all_dynamic:
            print(f"   ✅ SUCCESS! Entity limits are now dynamic")
            return True
        else:
            print(f"   ❌ STILL ISSUES: Some limits still capped at 3")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING FIXED 3-ENTITY LIMIT")
    print("=" * 80)
    
    # Test 1: Basic fix verification
    basic_success = test_fixed_entity_limit()
    
    # Test 2: Different limits
    dynamic_success = test_different_entity_limits()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Basic fix test: {'✅' if basic_success else '❌'}")
    print(f"   Dynamic limits test: {'✅' if dynamic_success else '❌'}")
    
    if basic_success and dynamic_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   ✅ 3-entity limit is now fixed")
        print(f"   ✅ Entity limits are dynamic based on max_entities")
        print(f"   ✅ Users can now get 5, 8, 10, 15+ entities in packages")
        print(f"   ✅ Class methods enhancement works with more entities")
        print(f"\n🚀 USERS WILL NOW GET:")
        print(f"   • Dynamic entity counts (not limited to 3)")
        print(f"   • More classes with methods displayed")
        print(f"   • Richer context packages")
        print(f"   • Better architectural understanding")
    else:
        print(f"\n❌ ISSUES REMAIN")
        print(f"   Need to investigate remaining hardcoded limits")
