#!/usr/bin/env python3
"""
Test the FIXED semantic intelligence system
This should actually work now!
"""

import sys
import os
import json
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector

def load_ir_data():
    """Load IR data for testing"""
    ir_files = list(Path(".").glob("ir_data_*.json"))
    if not ir_files:
        print("❌ No IR data files found. Please run IR generation first.")
        return None
    
    # Use the most recent IR file
    latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
    print(f"📊 Loading IR data from: {latest_ir_file}")
    
    with open(latest_ir_file, 'r') as f:
        return json.load(f)

def test_fixed_system():
    """Test the fixed system with critical queries"""
    print("🔧 TESTING FIXED SEMANTIC INTELLIGENCE SYSTEM")
    print("=" * 60)
    
    # Load IR data
    ir_data = load_ir_data()
    if not ir_data:
        return
    
    # Initialize the hierarchical context selector
    selector = HierarchicalContextSelector()
    
    # Test the most critical failing cases
    critical_tests = [
        {
            "query": "How does aider apply edits to files?",
            "expected": ["apply_edits", "edit_files", "FileEditor", "apply_edit"],
            "description": "File editing - MUST find apply_edits function"
        },
        {
            "query": "How does the chat loop work in aider?",
            "expected": ["run", "chat_loop", "send_message", "get_input"],
            "description": "Chat workflow - MUST find run/loop functions"
        },
        {
            "query": "What classes handle git operations?",
            "expected": ["GitRepo", "commit", "git_add", "git_commit"],
            "description": "Git operations - MUST find Git classes"
        }
    ]
    
    for i, test in enumerate(critical_tests, 1):
        print(f"\n🧪 CRITICAL TEST {i}: {test['description']}")
        print(f"Query: '{test['query']}'")
        print(f"MUST FIND: {test['expected']}")
        print("-" * 50)
        
        try:
            # Test keyword extraction first
            keywords = selector._extract_intelligent_keywords(test['query'])
            print(f"📝 Keywords extracted: {keywords}")
            
            # Run hierarchical context selection
            result = selector.select_hierarchical_context(
                ir_data=ir_data,
                user_query=test['query'],
                focus_entities=None,
                max_entities=8
            )
            
            # Analyze results
            selected_entities = result.get('selected_entities', [])
            print(f"\n📊 SELECTED {len(selected_entities)} ENTITIES:")
            
            found_expected = 0
            for j, entity in enumerate(selected_entities, 1):
                name = entity.get('name', 'unknown')
                score = entity.get('relevance_score', 0)
                entity_type = entity.get('type', 'unknown')
                
                # Check if this matches expected
                is_expected = any(exp.lower() in name.lower() or name.lower() in exp.lower() 
                                for exp in test['expected'])
                marker = "✅" if is_expected else "❌"
                if is_expected:
                    found_expected += 1
                
                print(f"   {j}. {marker} {name} ({entity_type}) - Score: {score:.2f}")
            
            # Calculate success
            success_rate = found_expected / len(test['expected'])
            print(f"\n🎯 SUCCESS RATE: {found_expected}/{len(test['expected'])} = {success_rate:.1%}")
            
            if success_rate >= 0.5:
                print("✅ TEST PASSED!")
            else:
                print("❌ TEST FAILED!")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()

def test_direct_relevance():
    """Test the direct relevance function specifically"""
    print("\n🎯 TESTING DIRECT RELEVANCE FUNCTION")
    print("=" * 60)
    
    ir_data = load_ir_data()
    if not ir_data:
        return
    
    selector = HierarchicalContextSelector()
    
    # Test cases that should score high
    test_cases = [
        {
            "entity_name": "apply_edits",
            "query_keywords": ["apply", "edits", "files"],
            "expected_score": "> 500"
        },
        {
            "entity_name": "run",
            "query_keywords": ["chat", "loop", "work"],
            "expected_score": "> 300"
        },
        {
            "entity_name": "GitRepo",
            "query_keywords": ["git", "operations", "classes"],
            "expected_score": "> 300"
        }
    ]
    
    for test in test_cases:
        print(f"\n🧪 Testing: {test['entity_name']}")
        print(f"Keywords: {test['query_keywords']}")
        
        score = selector._calculate_direct_relevance(
            entity_name=test['entity_name'],
            entity_type="function",
            entity_file="test.py",
            query_keywords=test['query_keywords'],
            ir_data=ir_data
        )
        
        print(f"🎯 Score: {score:.2f} (expected {test['expected_score']})")

if __name__ == "__main__":
    print("🚀 TESTING THE FIXED SYSTEM")
    print("This should actually work now - no more excuses!")
    print("=" * 80)
    
    test_direct_relevance()
    test_fixed_system()
    
    print("\n🎯 FIXED SYSTEM TEST COMPLETE")
    print("If this still doesn't work, we need a completely different approach!")
