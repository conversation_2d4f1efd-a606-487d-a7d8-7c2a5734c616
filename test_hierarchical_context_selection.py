"""
Test Hierarchical Context Selection
Test the new hierarchical architectural context selection approach.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest


def test_hierarchical_context_selection():
    """Test the hierarchical context selection with system architecture."""
    print("🏗️ Testing Hierarchical Context Selection")
    print("=" * 70)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Test different types of queries to see how architectural selection works
    test_queries = [
        {
            'query': 'How does user authentication work in the system?',
            'expected_strategy': 'workflow_focused',
            'description': 'Security workflow analysis'
        },
        {
            'query': 'What is the overall architecture of this system?',
            'expected_strategy': 'architecture_overview',
            'description': 'System architecture overview'
        },
        {
            'query': 'How does the context_request_handler work?',
            'expected_strategy': 'cluster_deep_dive',
            'description': 'Specific component deep dive'
        },
        {
            'query': 'How are errors handled across the system?',
            'expected_strategy': 'cross_cutting',
            'description': 'Cross-cutting concern analysis'
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print("-" * 50)
        
        # Create IR context request
        ir_request = IRContextRequest(
            user_query=test_case['query'],
            task_description=f"Analyze: {test_case['query']}",
            task_type="general_analysis",
            focus_entities=[],
            max_tokens=8000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_output_chars=30000,
            max_entities=8
        )
        
        try:
            print(f"🚀 Processing query: '{test_case['query']}'")
            
            # Process the request
            result = handler.process_ir_context_request(ir_request)
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                continue
            
            # Check if we got hierarchical context
            if 'llm_friendly_package' in result:
                package = result['llm_friendly_package']
                print(f"   ✅ Generated LLM package: {len(package):,} characters")
                
                # Look for architectural indicators in the package
                architectural_indicators = [
                    'Architectural Context Selection',
                    'Strategy:',
                    'Primary Clusters:',
                    'Workflow Path:',
                    'system architecture'
                ]
                
                found_indicators = []
                for indicator in architectural_indicators:
                    if indicator in package:
                        found_indicators.append(indicator)
                
                print(f"   📊 Architectural indicators found: {len(found_indicators)}/{len(architectural_indicators)}")
                
                if found_indicators:
                    print(f"   ✅ Contains architectural context:")
                    for indicator in found_indicators:
                        print(f"      • {indicator}")
                else:
                    print(f"   ⚠️  No architectural indicators found")
                
                # Show a preview of the architectural content
                if 'Strategy:' in package:
                    lines = package.split('\n')
                    for j, line in enumerate(lines):
                        if 'Strategy:' in line:
                            print(f"\n   📋 Architectural Context Preview:")
                            for k in range(j, min(j + 5, len(lines))):
                                if lines[k].strip():
                                    print(f"      {lines[k]}")
                            break
                
            else:
                print(f"   ❌ No LLM package generated")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    return True


def test_architecture_generation():
    """Test the system architecture generation specifically."""
    print(f"\n🏛️ Testing System Architecture Generation")
    print("=" * 70)
    
    try:
        from aider.context_request.system_architecture_generator import SystemArchitectureGenerator
        import json
        
        # Load IR data
        ir_file = "ir_data_20250601_175217.json"
        if not os.path.exists(ir_file):
            print(f"   ⚠️  IR file not found: {ir_file}")
            print(f"   💡 Run a context request first to generate IR data")
            return False
        
        with open(ir_file, 'r') as f:
            ir_data = json.load(f)
        
        print(f"   📊 Loaded IR data: {len(ir_data.get('modules', []))} modules")
        
        # Generate architecture
        generator = SystemArchitectureGenerator()
        architecture = generator.generate_architecture(ir_data)
        
        print(f"   ✅ Generated architecture:")
        print(f"      • {len(architecture.clusters)} clusters")
        print(f"      • {len(architecture.layer_hierarchy)} layers")
        print(f"      • {len(architecture.critical_paths)} critical paths")
        print(f"      • {len(architecture.entry_points)} entry points")
        
        # Show cluster details
        print(f"\n   🏗️ Cluster Details:")
        for cluster in architecture.clusters:
            print(f"      • {cluster.name}: {len(cluster.modules)} modules, criticality: {cluster.criticality:.2f}")
        
        # Show layer hierarchy
        print(f"\n   🏛️ Layer Hierarchy:")
        for layer, clusters in architecture.layer_hierarchy.items():
            print(f"      • {layer.value}: {', '.join(clusters)}")
        
        # Generate summary
        summary = generator.get_architecture_summary(architecture)
        print(f"\n   📋 Architecture Summary:")
        print(summary[:500] + "..." if len(summary) > 500 else summary)
        
        return True
        
    except Exception as e:
        print(f"   ❌ Architecture generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_strategy_selection():
    """Test that different queries select appropriate strategies."""
    print(f"\n🎯 Testing Strategy Selection Logic")
    print("=" * 70)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        from aider.context_request.intelligent_semantic_selector import IntelligentSemanticSelector
        
        selector = HierarchicalContextSelector()
        semantic_selector = IntelligentSemanticSelector()
        
        test_cases = [
            {
                'query': 'How does the authentication workflow work?',
                'expected': 'workflow_focused'
            },
            {
                'query': 'What is the system architecture?',
                'expected': 'architecture_overview'
            },
            {
                'query': 'How does the FileHandler class work?',
                'expected': 'cluster_deep_dive'
            },
            {
                'query': 'How is logging implemented across the system?',
                'expected': 'cross_cutting'
            }
        ]
        
        for test_case in test_cases:
            query = test_case['query']
            expected = test_case['expected']
            
            # Analyze query
            query_analysis = semantic_selector.analyze_query(query)
            strategy = selector._determine_selection_strategy(query_analysis, [])
            
            print(f"   Query: '{query}'")
            print(f"   Intent: {query_analysis.intent.value}")
            print(f"   Strategy: {strategy.value}")
            print(f"   Expected: {expected}")
            
            if strategy.value == expected:
                print(f"   ✅ Strategy selection correct")
            else:
                print(f"   ⚠️  Strategy selection differs from expected")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"   ❌ Strategy selection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔧 Testing Hierarchical Architectural Context Selection")
    print("=" * 70)
    
    # Test 1: Architecture generation
    test1_passed = test_architecture_generation()
    
    # Test 2: Strategy selection
    test2_passed = test_strategy_selection()
    
    # Test 3: End-to-end hierarchical context selection
    test3_passed = test_hierarchical_context_selection()
    
    print(f"\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"   Architecture Generation: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Strategy Selection: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   Hierarchical Context Selection: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed and test3_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   ✅ Hierarchical context selection is working")
        print(f"   ✅ System architecture generation is functional")
        print(f"   ✅ Strategy selection logic is correct")
        print(f"\n💡 The new approach should provide:")
        print(f"   • Better architectural understanding")
        print(f"   • More relevant context selection")
        print(f"   • Clearer explanation of why components were selected")
        print(f"   • Top-down system structure awareness")
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print(f"   Check the failed tests above for details")
        print(f"   The hierarchical approach may need debugging")
