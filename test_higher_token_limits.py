#!/usr/bin/env python3
"""
Test class methods enhancement with higher token limits to get more entities
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_higher_token_limits():
    """Test class methods with higher token limits"""
    print("🚀 TESTING HIGHER TOKEN LIMITS FOR MORE ENTITIES")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test with different token limits
        test_configs = [
            {"max_tokens": 5000, "max_entities": 5, "description": "5K tokens, 5 entities"},
            {"max_tokens": 10000, "max_entities": 8, "description": "10K tokens, 8 entities"},
            {"max_tokens": 20000, "max_entities": 12, "description": "20K tokens, 12 entities"},
            {"max_tokens": 50000, "max_entities": 20, "description": "50K tokens, 20 entities"},
        ]
        
        query = "how does the system manage position?"
        
        for i, config in enumerate(test_configs):
            print(f"\n🧪 TEST {i+1}: {config['description']}")
            print("=" * 50)
            
            request = IRContextRequest(
                user_query=query,
                task_description=f'Test higher limits: {query}',
                task_type='debugging',
                focus_entities=None,
                max_tokens=config['max_tokens'],
                include_ir_slices=True,
                include_code_context=False,
                llm_friendly=True,
                max_entities=config['max_entities'],
                max_output_chars=100000  # Allow large output
            )
            
            print(f"   📊 Config: {config['max_tokens']} tokens, {config['max_entities']} entities")
            
            # Process the request
            result = handler.process_ir_context_request(request)
            
            # Check results
            if 'error' in result:
                print(f"   ❌ ERROR: {result['error']}")
                continue
            
            # Analyze results
            ir_slices = result.get('ir_slices', [])
            llm_package = result.get('llm_friendly_package', '')
            token_utilization = result.get('summary', {}).get('token_utilization', 0)
            
            print(f"   📊 RESULTS:")
            print(f"      Entities selected: {len(ir_slices)}")
            print(f"      Token utilization: {token_utilization}%")
            print(f"      Package size: {len(llm_package):,} characters")
            
            # Count classes with methods
            classes_with_methods = 0
            total_methods = 0
            
            for ir_slice in ir_slices:
                entity_type = ir_slice.get('entity_type', '')
                entity_name = ir_slice.get('entity_name', '')
                
                if entity_type == 'class':
                    class_methods = ir_slice.get('class_methods', [])
                    if class_methods:
                        classes_with_methods += 1
                        total_methods += len(class_methods)
                        print(f"      🏛️ {entity_name}: {len(class_methods)} methods")
            
            print(f"   🏛️ METHODS SUMMARY:")
            print(f"      Classes with methods: {classes_with_methods}")
            print(f"      Total methods shown: {total_methods}")
            
            # Check if class methods section exists in LLM package
            methods_sections = llm_package.count('🏛️ Class Methods')
            print(f"      Methods sections in package: {methods_sections}")
            
            # Show entity types breakdown
            entity_types = {}
            for ir_slice in ir_slices:
                entity_type = ir_slice.get('entity_type', 'unknown')
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            print(f"   📋 ENTITY TYPES:")
            for entity_type, count in sorted(entity_types.items()):
                print(f"      {entity_type}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_position_specific_query():
    """Test with a position-specific query to see if we get PositionOpener/PositionCloser"""
    print(f"\n🎯 TESTING POSITION-SPECIFIC QUERY")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test with position-specific queries
        queries = [
            "PositionOpener and PositionCloser classes",
            "position management classes",
            "how to open and close positions",
            "position entry and exit system"
        ]
        
        for query in queries:
            print(f"\n🧪 Query: '{query}'")
            print("-" * 50)
            
            request = IRContextRequest(
                user_query=query,
                task_description=f'Position query: {query}',
                task_type='debugging',
                focus_entities=None,
                max_tokens=20000,  # High token limit
                include_ir_slices=True,
                include_code_context=False,
                llm_friendly=True,
                max_entities=15,  # More entities
                max_output_chars=100000
            )
            
            # Process the request
            result = handler.process_ir_context_request(request)
            
            # Check results
            if 'error' in result:
                print(f"   ❌ ERROR: {result['error']}")
                continue
            
            # Look for position-related entities
            ir_slices = result.get('ir_slices', [])
            position_entities = []
            
            for ir_slice in ir_slices:
                entity_name = ir_slice.get('entity_name', '').lower()
                if 'position' in entity_name:
                    position_entities.append({
                        'name': ir_slice.get('entity_name', ''),
                        'type': ir_slice.get('entity_type', ''),
                        'score': ir_slice.get('relevance_score', 0),
                        'methods': len(ir_slice.get('class_methods', []))
                    })
            
            print(f"   📊 Total entities: {len(ir_slices)}")
            print(f"   🎯 Position entities found: {len(position_entities)}")
            
            for entity in position_entities:
                print(f"      - {entity['name']} ({entity['type']}) - score: {entity['score']}, methods: {entity['methods']}")
            
            if not position_entities:
                print(f"   ❌ No position entities found")
                # Show top entities instead
                print(f"   📋 Top entities found:")
                for i, ir_slice in enumerate(ir_slices[:5]):
                    entity_name = ir_slice.get('entity_name', '')
                    entity_type = ir_slice.get('entity_type', '')
                    score = ir_slice.get('relevance_score', 0)
                    print(f"      {i+1}. {entity_name} ({entity_type}) - score: {score}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING HIGHER TOKEN LIMITS FOR CLASS METHODS")
    print("=" * 80)
    
    # Test 1: Higher token limits
    higher_limits_success = test_higher_token_limits()
    
    # Test 2: Position-specific queries
    position_success = test_position_specific_query()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Higher limits test: {'✅' if higher_limits_success else '❌'}")
    print(f"   Position query test: {'✅' if position_success else '❌'}")
    
    if higher_limits_success and position_success:
        print(f"\n🎉 SUCCESS!")
        print(f"   ✅ Higher token limits allow more entities")
        print(f"   ✅ More classes with methods are included")
        print(f"   ✅ Better context coverage achieved!")
    else:
        print(f"\n❌ SOME ISSUES REMAIN")
        print(f"   Need to investigate token calculation or entity selection")
