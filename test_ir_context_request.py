#!/usr/bin/env python3
"""
Test script to demonstrate the IR_CONTEXT_REQUEST system.
This shows how LLMs can request comprehensive context packages using JSON format.
"""

import sys
import os
import json
sys.path.insert(0, 'aider-main')

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest

def test_ir_context_request():
    """Test the IR_CONTEXT_REQUEST system with LLM-friendly packages."""
    print("🧠 Testing IR_CONTEXT_REQUEST system...")
    print("=" * 60)
    
    # Initialize handler
    handler = ContextRequestHandler('.')
    
    # Test 1: Basic IR context request
    print("\n🔍 Test 1: Basic IR Context Request")
    print("-" * 40)
    
    request = IRContextRequest(
        user_query="How does the context request system work?",
        task_description="Understand the context request implementation and architecture",
        task_type="general_analysis",
        focus_entities=["ContextRequestHandler", "process_context_request", "extract_symbol"],
        max_tokens=8000,
        include_ir_slices=True,
        include_code_context=True,
        llm_friendly=True,
        max_output_chars=30000,
        max_entities=8
    )
    
    # Process the request
    result = handler.process_ir_context_request(request)
    
    # Display results
    print(f"✅ Request processed successfully!")
    print(f"📊 Result keys: {list(result.keys())}")
    
    if "error" in result:
        print(f"❌ Error: {result['error']}")
        return
    
    # Show summary
    if "context_bundle" in result:
        bundle = result["context_bundle"]
        print(f"📦 Context Bundle:")
        print(f"   Total entities: {bundle.get('total_entities', 'N/A')}")
        print(f"   Total tokens: {bundle.get('total_tokens', 'N/A')}")
        print(f"   Selection rationale: {bundle.get('selection_rationale', 'N/A')[:100]}...")
    
    # Show LLM-friendly package
    if "llm_friendly_package" in result:
        package = result["llm_friendly_package"]
        print(f"\n🤖 LLM-Friendly Package Generated:")
        print(f"   Package size: {len(package)} characters")
        print(f"   LLM compatibility: {result.get('llm_compatibility', 'Unknown')}")
        
        # Show preview
        print(f"\n📄 Package Preview (first 800 chars):")
        print("=" * 60)
        print(package[:800])
        if len(package) > 800:
            print("... [truncated]")
        print("=" * 60)
        
        # Save to file for inspection
        filename = "test_ir_context_package.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(package)
        print(f"💾 Full package saved to: {filename}")
    
    # Test 2: Debugging-focused request
    print("\n\n🐛 Test 2: Debugging-Focused Request")
    print("-" * 40)
    
    debug_request = IRContextRequest(
        user_query="Why is my context selection taking so long?",
        task_description="Debug performance issues in context selection",
        task_type="debugging",
        focus_entities=["performance", "selection", "cache", "extraction"],
        max_tokens=5000,
        llm_friendly=True,
        max_entities=6
    )
    
    debug_result = handler.process_ir_context_request(debug_request)
    
    if "llm_friendly_package" in debug_result:
        debug_package = debug_result["llm_friendly_package"]
        print(f"✅ Debug package generated: {len(debug_package)} characters")
        print(f"🎯 LLM compatibility: {debug_result.get('llm_compatibility', 'Unknown')}")
        
        # Save debug package
        debug_filename = "test_debug_ir_context_package.txt"
        with open(debug_filename, 'w', encoding='utf-8') as f:
            f.write(debug_package)
        print(f"💾 Debug package saved to: {debug_filename}")
    
    # Test 3: JSON format simulation (how LLM would send it)
    print("\n\n📝 Test 3: JSON Format Simulation")
    print("-" * 40)
    
    # Simulate how an LLM would send the request
    llm_request_json = """{IR_CONTEXT_REQUEST: {
        "user_query": "How do I add a new extraction method?",
        "task_description": "Understand the extraction system to add new functionality",
        "task_type": "feature_development",
        "focus_entities": ["extract", "symbol", "content", "method"],
        "max_tokens": 6000,
        "include_ir_slices": true,
        "include_code_context": true,
        "llm_friendly": true,
        "max_output_chars": 25000,
        "max_entities": 10
    }}"""
    
    print(f"📤 Simulated LLM request:")
    print(llm_request_json.strip())
    
    # Parse the JSON (this would be done by the message processing system)
    import re
    pattern = r'\{IR_CONTEXT_REQUEST:\s*(.*?)\}\}'
    match = re.search(pattern, llm_request_json, re.DOTALL)
    
    if match:
        json_str = match.group(1).strip()
        try:
            request_data = json.loads(json_str)
            
            # Create IRContextRequest from parsed data
            parsed_request = IRContextRequest(
                user_query=request_data["user_query"],
                task_description=request_data["task_description"],
                task_type=request_data["task_type"],
                focus_entities=request_data["focus_entities"],
                max_tokens=request_data["max_tokens"],
                include_ir_slices=request_data["include_ir_slices"],
                include_code_context=request_data["include_code_context"],
                llm_friendly=request_data["llm_friendly"],
                max_output_chars=request_data["max_output_chars"],
                max_entities=request_data["max_entities"]
            )
            
            print(f"✅ JSON parsed successfully!")
            
            # Process the parsed request
            parsed_result = handler.process_ir_context_request(parsed_request)
            
            if "llm_friendly_package" in parsed_result:
                parsed_package = parsed_result["llm_friendly_package"]
                print(f"🎉 Feature development package generated: {len(parsed_package)} characters")
                
                # Save parsed package
                parsed_filename = "test_feature_dev_ir_context_package.txt"
                with open(parsed_filename, 'w', encoding='utf-8') as f:
                    f.write(parsed_package)
                print(f"💾 Feature dev package saved to: {parsed_filename}")
            
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
    else:
        print(f"❌ Failed to extract JSON from request")

def demonstrate_advantages():
    """Demonstrate the advantages of IR_CONTEXT_REQUEST over CONTEXT_REQUEST."""
    print("\n\n🚀 IR_CONTEXT_REQUEST vs CONTEXT_REQUEST Comparison")
    print("=" * 60)
    
    print("✅ IR_CONTEXT_REQUEST Advantages:")
    print("   • Single-step process (no MAP_REQUEST needed)")
    print("   • Intelligent entity selection based on relevance")
    print("   • Task-specific optimization (debugging, feature dev, etc.)")
    print("   • LLM-friendly packages with token optimization")
    print("   • Includes full class implementations (not just signatures)")
    print("   • Comprehensive context with dependencies")
    print("   • Automatic caching and performance optimization")
    print("   • Rich metadata and analysis")
    
    print("\n❌ CONTEXT_REQUEST Limitations:")
    print("   • Requires MAP_REQUEST first (two-step process)")
    print("   • Manual symbol selection")
    print("   • Class extraction issues (signatures only)")
    print("   • No intelligent relevance scoring")
    print("   • Limited context optimization")

if __name__ == "__main__":
    test_ir_context_request()
    demonstrate_advantages()
