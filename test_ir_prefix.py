#!/usr/bin/env python3
"""
Test the IR_REQUEST prefix functionality
"""

import sys
import os

# Add the aider-main directory to the path
aider_main_path = os.path.join(os.path.dirname(__file__), 'aider-main')
sys.path.insert(0, aider_main_path)

from aider.coders.base_prompts import CoderPrompts

def test_ir_prefix():
    """Test that the IR context response prefix is correctly set"""
    
    print("🧪 Testing IR Context Response Prefix")
    print("=" * 50)
    
    # Get the prompts
    prompts = CoderPrompts()
    
    # Check the prefix
    prefix = prompts.ir_context_response_prefix
    suffix = prompts.ir_context_response_suffix
    
    print(f"📋 IR Context Response Prefix:")
    print(f"'{prefix}'")
    print()
    print(f"📋 IR Context Response Suffix:")
    print(f"'{suffix}'")
    print()
    
    # Test the full template
    sample_content = "## CRITICAL ENTITIES\n\n### 1. test_function\n- Sample content"
    
    full_response = prefix + sample_content + suffix
    
    print(f"📋 Full Response Template:")
    print(f"'{full_response}'")
    print()
    
    # Verify the prefix is correct
    expected_prefix = "Based on your request, here's the relevant context generated by the system:\n\n"
    
    if prefix == expected_prefix:
        print("✅ PREFIX CORRECT: IR context response prefix is properly set")
    else:
        print("❌ PREFIX INCORRECT:")
        print(f"   Expected: '{expected_prefix}'")
        print(f"   Actual:   '{prefix}'")
    
    # Verify suffix is empty
    if suffix == "":
        print("✅ SUFFIX CORRECT: IR context response suffix is empty (clean)")
    else:
        print(f"❌ SUFFIX INCORRECT: Expected empty, got '{suffix}'")

if __name__ == "__main__":
    test_ir_prefix()
