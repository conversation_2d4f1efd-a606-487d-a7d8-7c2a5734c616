#!/usr/bin/env python3
"""
Test the IR_REQUEST format fix to ensure both JSON formats work correctly.
"""

import re
import json

def test_ir_request_formats():
    """Test both standard JSON and simplified IR_REQUEST formats."""
    
    print("🧪 Testing IR_REQUEST Format Compatibility")
    print("=" * 60)
    
    # Test cases with different formats
    test_cases = [
        {
            "name": "Standard JSON Format (LLM preferred)",
            "content": '''{"IR_REQUEST": {"focus_entities": ["process_all_positions", "close_all_positions", "update_positions"], "task_type": "verification"}}''',
            "expected_focus": ["process_all_positions", "close_all_positions", "update_positions"],
            "expected_task": "verification"
        },
        {
            "name": "Standard JSON with whitespace",
            "content": '''{ "IR_REQUEST" : { "focus_entities": ["cache", "performance"], "task_type": "debugging" } }''',
            "expected_focus": ["cache", "performance"],
            "expected_task": "debugging"
        },
        {
            "name": "Simplified Format (Original)",
            "content": '''{IR_REQUEST: {"focus_entities": ["keyword1", "keyword2"], "task_type": "verification"}}''',
            "expected_focus": ["keyword1", "keyword2"],
            "expected_task": "verification"
        },
        {
            "name": "Simplified Format with double braces",
            "content": '''{{IR_REQUEST: {"focus_entities": ["test"], "task_type": "debugging"}}}''',
            "expected_focus": ["test"],
            "expected_task": "debugging"
        },
        {
            "name": "Embedded in text (Standard JSON)",
            "content": '''I need more context. {"IR_REQUEST": {"focus_entities": ["extract", "symbol"], "task_type": "feature_development"}} Please provide this.''',
            "expected_focus": ["extract", "symbol"],
            "expected_task": "feature_development"
        }
    ]
    
    # Updated patterns from the fix
    patterns = [
        # Standard JSON format: {"IR_REQUEST": {...}}
        r'\{\s*"IR_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_REQUEST": {...}}
        # Simplified format: {IR_REQUEST: {...}}
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"   Content: {test_case['content']}")
        
        # Try to match with patterns
        matched = False
        for j, pattern in enumerate(patterns, 1):
            match = re.search(pattern, test_case['content'], re.DOTALL)
            if match:
                captured = match.group(1).strip()
                print(f"   ✅ Pattern {j} matched: {repr(captured)}")
                
                try:
                    parsed = json.loads(captured)
                    focus_entities = parsed.get("focus_entities", [])
                    task_type = parsed.get("task_type", "")
                    
                    print(f"   📋 Parsed focus_entities: {focus_entities}")
                    print(f"   📋 Parsed task_type: {task_type}")
                    
                    # Verify expected results
                    if (focus_entities == test_case["expected_focus"] and 
                        task_type == test_case["expected_task"]):
                        print(f"   ✅ VALIDATION SUCCESS")
                        success_count += 1
                        matched = True
                        break
                    else:
                        print(f"   ❌ VALIDATION FAILED")
                        print(f"      Expected focus: {test_case['expected_focus']}")
                        print(f"      Expected task: {test_case['expected_task']}")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON parsing failed: {e}")
                    continue
        
        if not matched:
            print(f"   ❌ NO PATTERN MATCHED")
    
    print(f"\n📊 Test Results: {success_count}/{total_count} tests passed")
    
    if success_count == total_count:
        print("🎉 ALL TESTS PASSED! Both JSON formats are now supported.")
        return True
    else:
        print("❌ Some tests failed. Format compatibility issues remain.")
        return False

def test_ir_context_request_formats():
    """Test IR_CONTEXT_REQUEST format compatibility as well."""
    
    print("\n🧪 Testing IR_CONTEXT_REQUEST Format Compatibility")
    print("=" * 60)
    
    test_cases = [
        {
            "name": "Standard JSON Format",
            "content": '''{"IR_CONTEXT_REQUEST": {"user_query": "test", "task_type": "debugging"}}''',
        },
        {
            "name": "Simplified Format",
            "content": '''{IR_CONTEXT_REQUEST: {"user_query": "test", "task_type": "debugging"}}''',
        }
    ]
    
    # Updated patterns for IR_CONTEXT_REQUEST
    patterns = [
        # Standard JSON format: {"IR_CONTEXT_REQUEST": {...}}
        r'\{\s*"IR_CONTEXT_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_CONTEXT_REQUEST": {...}}
        # Simplified format: {IR_CONTEXT_REQUEST: {...}}
        r'\{IR_CONTEXT_REQUEST:\s*(\{.*?\})\s*\}\}',  # {IR_CONTEXT_REQUEST: {...}}} - double closing brace
        r'\{IR_CONTEXT_REQUEST:\s*(\{.*?\})\s*\}',    # {IR_CONTEXT_REQUEST: {...}} - single closing brace
        r'\{IR_CONTEXT_REQUEST:\s*(.*)'               # {IR_CONTEXT_REQUEST: ... - fallback for incomplete
    ]
    
    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔍 Test {i}: {test_case['name']}")
        print(f"   Content: {test_case['content']}")
        
        for j, pattern in enumerate(patterns, 1):
            match = re.search(pattern, test_case['content'], re.DOTALL)
            if match:
                captured = match.group(1).strip()
                print(f"   ✅ Pattern {j} matched: {repr(captured)}")
                
                try:
                    parsed = json.loads(captured)
                    print(f"   ✅ JSON parsed successfully: {parsed}")
                    success_count += 1
                    break
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON parsing failed: {e}")
                    continue
        else:
            print(f"   ❌ NO PATTERN MATCHED")
    
    print(f"\n📊 IR_CONTEXT_REQUEST Results: {success_count}/{len(test_cases)} tests passed")
    return success_count == len(test_cases)

if __name__ == "__main__":
    ir_success = test_ir_request_formats()
    ir_context_success = test_ir_context_request_formats()
    
    if ir_success and ir_context_success:
        print("\n🎉 ALL FORMAT COMPATIBILITY TESTS PASSED!")
        print("✅ Both standard JSON and simplified formats are now supported")
        print("✅ LLMs can use either format successfully")
    else:
        print("\n❌ Some format compatibility issues remain")
