#!/usr/bin/env python3
"""
Test various IR_REQUEST format variations to ensure robustness
"""

import re
import json

def test_ir_request_variations():
    """Test different IR_REQUEST format variations"""
    
    print("🧪 Testing IR_REQUEST Format Variations")
    print("=" * 50)
    
    # Test cases with different formats
    test_cases = [
        # Single closing brace (most common)
        '{IR_REQUEST: { "focus_entities": ["compute_next_boundary"], "task_type": "debugging" }}',
        
        # Double closing brace (some LLMs might do this)
        '{IR_REQUEST: { "focus_entities": ["cache"], "task_type": "debugging" }}}',
        
        # With extra whitespace
        '{IR_REQUEST:   { "focus_entities": ["test"], "task_type": "debugging" }  }',
        
        # Multiline format
        '''{IR_REQUEST: {
  "focus_entities": ["multiline", "test"],
  "task_type": "debugging"
}}''',
        
        # Embedded in text
        'Some text before {IR_REQUEST: { "focus_entities": ["embedded"], "task_type": "debugging" }} and after',
    ]
    
    # Fixed regex patterns
    patterns = [
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace  
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}:")
        print(f"Input: {repr(test_case)}")
        
        success = False
        for j, pattern in enumerate(patterns, 1):
            match = re.search(pattern, test_case, re.DOTALL)
            if match:
                captured = match.group(1).strip()
                print(f"✅ Pattern {j} matched: {repr(captured)}")
                
                try:
                    parsed = json.loads(captured)
                    print(f"✅ JSON parsed: {parsed}")
                    success = True
                    break
                except json.JSONDecodeError as e:
                    print(f"❌ JSON failed: {e}")
                    continue
        
        if not success:
            print("❌ No pattern matched or JSON parsing failed")

if __name__ == "__main__":
    test_ir_request_variations()
