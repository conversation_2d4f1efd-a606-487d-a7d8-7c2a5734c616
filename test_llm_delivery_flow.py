"""
Test LLM Delivery Flow
Test the complete flow from LLM IR_CONTEXT_REQUEST to package delivery.
"""

import sys
import os
import json

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest


def test_llm_ir_request_flow():
    """Test the complete LLM IR_CONTEXT_REQUEST flow."""
    print("🧪 Testing Complete LLM IR_CONTEXT_REQUEST Flow")
    print("=" * 70)
    
    # Simulate what an LLM would send
    llm_request_json = {
        "user_query": "How does user authentication work in the system?",
        "task_description": "Analyze authentication workflow and security components",
        "task_type": "security_analysis",
        "focus_entities": ["authenticate", "user", "login", "auth"],
        "max_tokens": 8000,
        "llm_friendly": True,
        "include_ir_slices": True,
        "include_code_context": True,
        "max_output_chars": 30000,
        "max_entities": 8
    }
    
    print(f"🤖 Simulating LLM IR_CONTEXT_REQUEST:")
    print(f"   Query: {llm_request_json['user_query']}")
    print(f"   Task: {llm_request_json['task_description']}")
    print(f"   LLM Friendly: {llm_request_json['llm_friendly']}")
    
    # Initialize the context request handler (like the real system does)
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Create the request object (like the real system does)
    ir_request = IRContextRequest(
        user_query=llm_request_json["user_query"],
        task_description=llm_request_json["task_description"],
        task_type=llm_request_json["task_type"],
        focus_entities=llm_request_json["focus_entities"],
        max_tokens=llm_request_json["max_tokens"],
        llm_friendly=llm_request_json["llm_friendly"],
        include_ir_slices=llm_request_json["include_ir_slices"],
        include_code_context=llm_request_json["include_code_context"],
        max_output_chars=llm_request_json["max_output_chars"],
        max_entities=llm_request_json["max_entities"]
    )
    
    try:
        print(f"\n🚀 Processing IR_CONTEXT_REQUEST...")
        
        # Process the request (this is where semantic intelligence kicks in)
        result = handler.process_ir_context_request(ir_request)
        
        print(f"\n📊 PROCESSING RESULTS:")
        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return
        
        print(f"   ✅ Processing successful")
        print(f"   Selected entities: {result.get('context_bundle', {}).get('total_entities', 0)}")
        
        # Check if LLM-friendly package was created
        if 'llm_friendly_package' in result:
            llm_package = result['llm_friendly_package']
            print(f"   ✅ LLM-friendly package created: {len(llm_package):,} characters")
            
            # This is what should be delivered to the LLM
            print(f"\n📦 PACKAGE DELIVERY TEST:")
            print(f"   Package size: {len(llm_package):,} characters")
            print(f"   Compatibility: {result.get('llm_compatibility', 'Unknown')}")
            
            # Show what the LLM would receive
            print(f"\n📄 WHAT LLM RECEIVES (first 800 chars):")
            print("-" * 60)
            print(llm_package[:800])
            if len(llm_package) > 800:
                print("... (content continues)")
            print("-" * 60)
            
            # Test the augmented prompt creation (like base_coder.py does)
            print(f"\n🔄 TESTING AUGMENTED PROMPT CREATION:")
            
            # Simulate _create_ir_context_augmented_prompt logic
            if "llm_friendly_package" in result:
                augmented_prompt = result["llm_friendly_package"]
                print(f"   ✅ Augmented prompt created from LLM package")
                print(f"   ✅ Prompt size: {len(augmented_prompt):,} characters")
                
                # This is what gets sent back to the LLM
                print(f"\n🎯 FINAL LLM DELIVERY:")
                print(f"   The LLM receives the semantic package as its context")
                print(f"   Package contains semantic analysis and selected components")
                print(f"   Size: {len(augmented_prompt):,} characters")
                
                # Check if it contains semantic intelligence markers
                if "Intelligent Semantic Analysis" in augmented_prompt:
                    print(f"   ✅ Contains semantic intelligence markers")
                if "Query Intent:" in augmented_prompt:
                    print(f"   ✅ Contains query intent analysis")
                if "Semantic Rationale:" in augmented_prompt:
                    print(f"   ✅ Contains semantic rationale")
                
                print(f"\n🎉 LLM DELIVERY FLOW TEST SUCCESSFUL!")
                print(f"   The LLM should receive the enhanced semantic package")
                
            else:
                print(f"   ❌ No LLM package found in result")
                
        else:
            print(f"   ❌ No LLM-friendly package created")
            print(f"   Available keys: {list(result.keys())}")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


def test_response_template():
    """Test the response template that LLMs should see."""
    print(f"\n" + "=" * 70)
    print("🧪 Testing LLM Response Template")
    print("=" * 70)
    
    # Test the template that should be used for IR_REQUEST responses
    user_query = "How does user authentication work in the system?"
    
    # This is what the LLM should see in response to their IR_CONTEXT_REQUEST
    expected_response_template = f"""based on your request {user_query}, here's the relevant context generated by the system:

[SEMANTIC LLM PACKAGE CONTENT WOULD BE HERE]

This context was selected using intelligent semantic analysis instead of keyword matching."""
    
    print(f"🎯 Expected LLM Response Template:")
    print("-" * 50)
    print(expected_response_template)
    print("-" * 50)
    
    print(f"\n📋 Template Analysis:")
    print(f"   ✅ Includes user query reference")
    print(f"   ✅ Mentions semantic analysis")
    print(f"   ✅ Clear context delivery format")
    
    print(f"\n💡 The LLM should see this template with the actual semantic package content")


if __name__ == "__main__":
    test_llm_ir_request_flow()
    test_response_template()
