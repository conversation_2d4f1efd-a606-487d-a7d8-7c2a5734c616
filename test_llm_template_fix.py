"""
Test LLM Template Fix
Test that the LLM receives the correct template format with user query.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import Context<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest
from aider.coders.base_prompts import CoderPrompts


def test_template_format():
    """Test that the template format includes the user query correctly."""
    print("🧪 Testing LLM Template Format Fix")
    print("=" * 70)
    
    # Check the template format
    prompts = CoderPrompts()
    template = prompts.ir_context_response_prefix
    
    print(f"📋 Current Template:")
    print(f"   '{template}'")
    
    # Test template formatting
    user_query = "How does user authentication work in the system?"
    if "{user_query}" in template:
        formatted_template = template.format(user_query=user_query)
        print(f"\n✅ Template supports user query placeholder")
        print(f"📝 Formatted Template:")
        print(f"   '{formatted_template}'")
    else:
        print(f"\n❌ Template does not support user query placeholder")
        return False
    
    return True


def test_end_to_end_llm_delivery():
    """Test the complete end-to-end LLM delivery with the new template."""
    print(f"\n" + "=" * 70)
    print("🧪 Testing End-to-End LLM Delivery with Template")
    print("=" * 70)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Create a test request
    user_query = "How does user authentication work in the system?"
    ir_request = IRContextRequest(
        user_query=user_query,
        task_description="Analyze authentication workflow and security components",
        task_type="security_analysis",
        focus_entities=["authenticate", "user", "login", "auth"],
        max_tokens=8000,
        llm_friendly=True,
        include_ir_slices=True,
        include_code_context=True,
        max_output_chars=30000,
        max_entities=8
    )
    
    try:
        print(f"🚀 Processing IR_CONTEXT_REQUEST...")
        
        # Process the request
        result = handler.process_ir_context_request(ir_request)
        
        if 'error' in result:
            print(f"   ❌ Error: {result['error']}")
            return False
        
        print(f"   ✅ Processing successful")
        
        # Check if LLM-friendly package was created
        if 'llm_friendly_package' not in result:
            print(f"   ❌ No LLM-friendly package created")
            return False
        
        llm_package = result['llm_friendly_package']
        print(f"   ✅ LLM package created: {len(llm_package):,} characters")
        
        # Simulate the augmented prompt creation (like base_coder.py does)
        print(f"\n🔄 Testing Augmented Prompt Creation...")
        
        # Import the template
        from aider.coders.base_prompts import CoderPrompts
        prompts = CoderPrompts()
        prefix = prompts.ir_context_response_prefix
        suffix = prompts.ir_context_response_suffix
        
        # Format the prefix with the user query
        if "{user_query}" in prefix:
            formatted_prefix = prefix.format(user_query=user_query)
        else:
            formatted_prefix = prefix
        
        # Create the final augmented prompt (what the LLM receives)
        augmented_prompt = formatted_prefix + llm_package + suffix
        
        print(f"   ✅ Augmented prompt created: {len(augmented_prompt):,} characters")
        
        # Show what the LLM receives
        print(f"\n📄 WHAT LLM RECEIVES (first 200 chars):")
        print("-" * 60)
        print(augmented_prompt[:200])
        print("... (content continues)")
        print("-" * 60)
        
        # Verify the template format
        expected_start = f"based on your request {user_query}, here's the relevant context"
        if augmented_prompt.startswith(expected_start):
            print(f"\n✅ TEMPLATE FORMAT CORRECT!")
            print(f"   ✅ Starts with: 'based on your request {user_query}...'")
            print(f"   ✅ Contains semantic package content")
            print(f"   ✅ LLM will receive properly formatted response")
            return True
        else:
            print(f"\n❌ TEMPLATE FORMAT INCORRECT!")
            print(f"   Expected start: '{expected_start}'")
            print(f"   Actual start: '{augmented_prompt[:len(expected_start)]}'")
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_both_ir_methods():
    """Test both IR_CONTEXT_REQUEST and IR_REQUEST methods."""
    print(f"\n" + "=" * 70)
    print("🧪 Testing Both IR Methods Use Same Template")
    print("=" * 70)
    
    # Test that both methods use the same template format
    from aider.coders.base_prompts import CoderPrompts
    prompts = CoderPrompts()
    
    print(f"📋 IR Context Response Template:")
    print(f"   Prefix: '{prompts.ir_context_response_prefix}'")
    print(f"   Suffix: '{prompts.ir_context_response_suffix}'")
    
    # Check if template has user query placeholder
    if "{user_query}" in prompts.ir_context_response_prefix:
        print(f"   ✅ Template supports user query placeholder")
        
        # Test formatting
        test_query = "Test query"
        formatted = prompts.ir_context_response_prefix.format(user_query=test_query)
        print(f"   ✅ Formatted example: '{formatted}'")
        
        return True
    else:
        print(f"   ❌ Template missing user query placeholder")
        return False


if __name__ == "__main__":
    print("🔧 Testing LLM Template Fix for IR Context System")
    print("=" * 70)
    
    # Test 1: Template format
    test1_passed = test_template_format()
    
    # Test 2: Both IR methods
    test2_passed = test_both_ir_methods()
    
    # Test 3: End-to-end delivery
    test3_passed = test_end_to_end_llm_delivery()
    
    print(f"\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"   Template Format: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   IR Methods Consistency: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"   End-to-End Delivery: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed and test3_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   ✅ LLM template fix is working correctly")
        print(f"   ✅ LLMs will receive properly formatted responses")
        print(f"   ✅ Template includes user query as expected")
    else:
        print(f"\n❌ SOME TESTS FAILED!")
        print(f"   The LLM delivery issue may not be fully resolved")
