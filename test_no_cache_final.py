#!/usr/bin/env python3
"""
Final test with all caching disabled to verify context packages change with queries
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_context_packages_change():
    """Test that context packages actually change with different queries"""
    print("🎯 FINAL TEST: Context packages change with NO CACHING")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches completely
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Test different queries
        test_queries = [
            "How does aider apply edits to files?",
            "How does the chat loop work in aider?", 
            "What git operations are available?"
        ]
        
        results = {}
        
        for i, query in enumerate(test_queries):
            print(f"\n🧪 TEST {i+1}: {query}")
            
            # Create a fresh handler for each test
            handler = ContextRequestHandler('.')
            handler.cache.clear()  # Clear instance cache too
            
            # Create request
            request = IRContextRequest(
                user_query=query,
                task_description=f'Test query {i+1}',
                task_type='debugging',
                focus_entities=None,
                max_tokens=2000,
                include_ir_slices=True,
                include_code_context=False,  # Skip code context for speed
                llm_friendly=False,
                max_entities=3
            )
            
            try:
                # Process the request
                print(f"   🔄 Processing request...")
                result = handler.process_ir_context_request(request)
                
                # Check results
                if 'error' in result:
                    print(f"   ❌ ERROR: {result['error']}")
                    results[query] = []
                    continue
                
                # Get IR slices
                ir_slices = result.get('ir_slices', [])
                entity_names = [slice.get('entity_name', '') for slice in ir_slices]
                
                print(f"   📊 Found {len(ir_slices)} entities: {entity_names}")
                results[query] = entity_names
                
                # Check for expected entities
                if i == 0:  # Apply edits query
                    if any('apply_edits' in name.lower() for name in entity_names):
                        print(f"   ✅ SUCCESS: Found apply_edits related function!")
                    else:
                        print(f"   ⚠️ WARNING: No apply_edits function found")
                
                elif i == 1:  # Chat loop query
                    if any(name.lower() in ['run', 'send_message', 'get_input'] for name in entity_names):
                        print(f"   ✅ SUCCESS: Found chat loop function!")
                    else:
                        print(f"   ⚠️ WARNING: No chat loop functions found")
                
                elif i == 2:  # Git operations query
                    if any('git' in name.lower() for name in entity_names):
                        print(f"   ✅ SUCCESS: Found git-related function!")
                    else:
                        print(f"   ⚠️ WARNING: No git functions found")
                
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
                results[query] = []
        
        # Analyze results
        print(f"\n📊 FINAL ANALYSIS:")
        unique_results = len(set(frozenset(entities) for entities in results.values() if entities))
        
        for i, (query, entities) in enumerate(results.items(), 1):
            short_query = query.split('?')[0] + '?'
            print(f"   Test {i}: {short_query}")
            print(f"      Results: {entities}")
        
        print(f"\n   Unique result sets: {unique_results}")
        print(f"   Different results per query: {'✅ YES' if unique_results > 1 else '❌ NO'}")
        
        # Success criteria
        has_different_results = unique_results > 1
        has_some_results = any(len(entities) > 0 for entities in results.values())
        
        if has_different_results and has_some_results:
            print(f"\n🎉 COMPLETE SUCCESS!")
            print(f"   ✅ Context packages change based on user queries!")
            print(f"   ✅ Enhanced semantic intelligence is working!")
            print(f"   ✅ Caching has been successfully disabled!")
            return True
        elif has_some_results:
            print(f"\n⚠️ PARTIAL SUCCESS")
            print(f"   ✅ System generates results")
            print(f"   ❌ But results are not different enough between queries")
            return False
        else:
            print(f"\n❌ FAILURE")
            print(f"   ❌ System is not generating any results")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nuclear_option_still_works():
    """Test that the nuclear option still works after cache removal"""
    print(f"\n🔧 TESTING NUCLEAR OPTION AFTER CACHE REMOVAL")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Create a simple mock IR dataset
        mock_ir_data = {
            "modules": [
                {
                    "name": "test_module",
                    "entities": [
                        {"name": "apply_edits", "type": "function", "file_path": "base_coder.py"},
                        {"name": "run", "type": "function", "file_path": "main.py"},
                        {"name": "GitRepo", "type": "class", "file_path": "git.py"},
                    ]
                }
            ]
        }
        
        selector = HierarchicalContextSelector()
        
        # Test the nuclear option
        result = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='How does aider apply edits to files?',
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities = result.get('selected_entities', [])
        entity_names = [e.get('name', '') for e in selected_entities]
        
        print(f"   Selected entities: {entity_names}")
        
        if 'apply_edits' in entity_names:
            print(f"   ✅ Nuclear option still works!")
            return True
        else:
            print(f"   ❌ Nuclear option broken!")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 FINAL TEST: NO CACHING SYSTEM")
    print("=" * 80)
    
    # Test 1: Nuclear option still works
    nuclear_success = test_nuclear_option_still_works()
    
    # Test 2: Context packages change with queries
    context_success = test_context_packages_change()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Nuclear option working: {'✅' if nuclear_success else '❌'}")
    print(f"   Context packages change: {'✅' if context_success else '❌'}")
    
    if nuclear_success and context_success:
        print(f"\n🎉 MISSION ACCOMPLISHED!")
        print(f"   ✅ All caching has been removed!")
        print(f"   ✅ Context packages now change based on user queries!")
        print(f"   ✅ The enhanced semantic intelligence system is fully operational!")
        print(f"   ✅ The user's complaint has been resolved!")
    else:
        print(f"\n❌ MISSION NOT COMPLETE")
        print(f"   Some issues remain to be resolved.")
