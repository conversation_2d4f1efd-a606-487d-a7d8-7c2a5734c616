#!/usr/bin/env python3
"""
Test class methods enhancement with higher token limits and NO CACHING
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_no_cache_higher_limits():
    """Test class methods with higher token limits and no caching"""
    print("🚀 TESTING HIGHER TOKEN LIMITS WITH NO CACHING")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Test with different token limits - each with fresh handler to avoid caching
        test_configs = [
            {"max_tokens": 5000, "max_entities": 5, "description": "5K tokens, 5 entities"},
            {"max_tokens": 10000, "max_entities": 8, "description": "10K tokens, 8 entities"},
            {"max_tokens": 20000, "max_entities": 12, "description": "20K tokens, 12 entities"},
            {"max_tokens": 50000, "max_entities": 20, "description": "50K tokens, 20 entities"},
        ]
        
        query = "how does the system manage dependencies and integration?"
        
        for i, config in enumerate(test_configs):
            print(f"\n🧪 TEST {i+1}: {config['description']}")
            print("=" * 50)
            
            # Create FRESH handler for each test to avoid caching
            ContextRequestHandler._ir_cache.clear()
            ContextRequestHandler._ir_cache_timestamps.clear()
            handler = ContextRequestHandler('.')
            handler.cache.clear()
            
            request = IRContextRequest(
                user_query=query,
                task_description=f'No cache test {i+1}: {query}',
                task_type='debugging',
                focus_entities=None,
                max_tokens=config['max_tokens'],
                include_ir_slices=True,
                include_code_context=False,
                llm_friendly=True,
                max_entities=config['max_entities'],
                max_output_chars=100000  # Allow large output
            )
            
            print(f"   📊 Config: {config['max_tokens']} tokens, {config['max_entities']} entities")
            print(f"   🚫 Cache cleared for fresh results")
            
            # Process the request
            result = handler.process_ir_context_request(request)
            
            # Check results
            if 'error' in result:
                print(f"   ❌ ERROR: {result['error']}")
                continue
            
            # Analyze results
            ir_slices = result.get('ir_slices', [])
            llm_package = result.get('llm_friendly_package', '')
            token_utilization = result.get('summary', {}).get('token_utilization', 0)
            
            print(f"   📊 RESULTS:")
            print(f"      Entities selected: {len(ir_slices)}")
            print(f"      Token utilization: {token_utilization}%")
            print(f"      Package size: {len(llm_package):,} characters")
            
            # Count classes with methods
            classes_with_methods = 0
            total_methods = 0
            
            print(f"   🏛️ CLASSES AND METHODS:")
            for ir_slice in ir_slices:
                entity_type = ir_slice.get('entity_type', '')
                entity_name = ir_slice.get('entity_name', '')
                
                if entity_type == 'class':
                    class_methods = ir_slice.get('class_methods', [])
                    if class_methods:
                        classes_with_methods += 1
                        total_methods += len(class_methods)
                        print(f"      🏛️ {entity_name}: {len(class_methods)} methods")
                    else:
                        print(f"      🏛️ {entity_name}: 0 methods (enum/data class)")
                else:
                    print(f"      📄 {entity_name} ({entity_type})")
            
            print(f"   📊 SUMMARY:")
            print(f"      Classes with methods: {classes_with_methods}")
            print(f"      Total methods shown: {total_methods}")
            
            # Check if class methods section exists in LLM package
            methods_sections = llm_package.count('🏛️ Class Methods')
            print(f"      Methods sections in package: {methods_sections}")
            
            # Show entity types breakdown
            entity_types = {}
            for ir_slice in ir_slices:
                entity_type = ir_slice.get('entity_type', 'unknown')
                entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
            
            print(f"   📋 ENTITY TYPES:")
            for entity_type, count in sorted(entity_types.items()):
                print(f"      {entity_type}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_realistic_query():
    """Test with a realistic query that should find many relevant entities"""
    print(f"\n🎯 TESTING REALISTIC QUERY WITH HIGH TOKEN LIMITS")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Use a query that should find many relevant entities
        query = "context request handling and IR generation system"
        
        request = IRContextRequest(
            user_query=query,
            task_description=f'Realistic test: {query}',
            task_type='debugging',
            focus_entities=None,
            max_tokens=30000,  # High token limit
            include_ir_slices=True,
            include_code_context=False,
            llm_friendly=True,
            max_entities=25,  # Many entities
            max_output_chars=150000
        )
        
        print(f"🧪 Query: '{query}'")
        print(f"📊 Config: 30K tokens, 25 entities max")
        print(f"🚫 Fresh handler, no caching")
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"❌ ERROR: {result['error']}")
            return False
        
        # Analyze results
        ir_slices = result.get('ir_slices', [])
        llm_package = result.get('llm_friendly_package', '')
        token_utilization = result.get('summary', {}).get('token_utilization', 0)
        
        print(f"\n📊 COMPREHENSIVE RESULTS:")
        print(f"   Entities selected: {len(ir_slices)}")
        print(f"   Token utilization: {token_utilization}%")
        print(f"   Package size: {len(llm_package):,} characters")
        
        # Detailed analysis
        classes_found = []
        functions_found = []
        other_entities = []
        
        for ir_slice in ir_slices:
            entity_type = ir_slice.get('entity_type', '')
            entity_name = ir_slice.get('entity_name', '')
            relevance_score = ir_slice.get('relevance_score', 0)
            
            if entity_type == 'class':
                class_methods = ir_slice.get('class_methods', [])
                classes_found.append({
                    'name': entity_name,
                    'methods': len(class_methods),
                    'score': relevance_score
                })
            elif entity_type == 'function':
                functions_found.append({
                    'name': entity_name,
                    'score': relevance_score
                })
            else:
                other_entities.append({
                    'name': entity_name,
                    'type': entity_type,
                    'score': relevance_score
                })
        
        print(f"\n🏛️ CLASSES FOUND ({len(classes_found)}):")
        for cls in sorted(classes_found, key=lambda x: x['score'], reverse=True)[:10]:
            print(f"   {cls['name']}: {cls['methods']} methods (score: {cls['score']})")
        
        print(f"\n📄 FUNCTIONS FOUND ({len(functions_found)}):")
        for func in sorted(functions_found, key=lambda x: x['score'], reverse=True)[:10]:
            print(f"   {func['name']} (score: {func['score']})")
        
        print(f"\n📋 OTHER ENTITIES ({len(other_entities)}):")
        for entity in sorted(other_entities, key=lambda x: x['score'], reverse=True)[:5]:
            print(f"   {entity['name']} ({entity['type']}) - score: {entity['score']}")
        
        # Check class methods in LLM package
        methods_sections = llm_package.count('🏛️ Class Methods')
        total_methods = sum(cls['methods'] for cls in classes_found)
        
        print(f"\n🎯 CLASS METHODS ANALYSIS:")
        print(f"   Classes with methods: {len([c for c in classes_found if c['methods'] > 0])}")
        print(f"   Total methods across all classes: {total_methods}")
        print(f"   Methods sections in LLM package: {methods_sections}")
        
        # Success criteria
        success = (
            len(ir_slices) >= 10 and  # Got many entities
            len(classes_found) >= 3 and  # Got multiple classes
            total_methods >= 10 and  # Got many methods
            methods_sections >= 2  # Methods are displayed
        )
        
        if success:
            print(f"\n🎉 EXCELLENT RESULTS!")
            print(f"   ✅ Found {len(ir_slices)} entities (target: ≥10)")
            print(f"   ✅ Found {len(classes_found)} classes (target: ≥3)")
            print(f"   ✅ Found {total_methods} total methods (target: ≥10)")
            print(f"   ✅ {methods_sections} method sections in package (target: ≥2)")
        else:
            print(f"\n⚠️ MIXED RESULTS:")
            print(f"   Entities: {len(ir_slices)} (target: ≥10)")
            print(f"   Classes: {len(classes_found)} (target: ≥3)")
            print(f"   Methods: {total_methods} (target: ≥10)")
            print(f"   Method sections: {methods_sections} (target: ≥2)")
        
        return success
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING HIGHER TOKEN LIMITS WITH NO CACHING")
    print("=" * 80)
    
    # Test 1: Progressive token limits
    progressive_success = test_no_cache_higher_limits()
    
    # Test 2: Realistic comprehensive query
    realistic_success = test_realistic_query()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Progressive limits test: {'✅' if progressive_success else '❌'}")
    print(f"   Realistic query test: {'✅' if realistic_success else '❌'}")
    
    if progressive_success and realistic_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   ✅ Higher token limits work perfectly")
        print(f"   ✅ More entities are selected with higher limits")
        print(f"   ✅ Class methods are displayed correctly")
        print(f"   ✅ System scales well with token budget")
        print(f"\n💡 RECOMMENDATION:")
        print(f"   Use max_tokens=20000+ and max_entities=15+ for rich context")
    else:
        print(f"\n❌ ISSUES FOUND")
        print(f"   Need to investigate token calculation or entity selection")
