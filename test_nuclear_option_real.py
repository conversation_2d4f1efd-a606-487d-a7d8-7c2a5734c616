#!/usr/bin/env python3
"""
Test the nuclear option in the real context system
"""

import sys
import os
import json
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_nuclear_option():
    """Test if the nuclear option is actually being used"""
    print("🔧 TESTING NUCLEAR OPTION IN REAL SYSTEM")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Test the actual system
        handler = ContextRequestHandler('.')
        
        # Create a simple request
        request = IRContextRequest(
            user_query='How does aider apply edits to files?',
            task_description='Find edit functions',
            task_type='debugging',
            focus_entities=['apply_edits', 'edit_files'],
            max_tokens=2000,
            include_ir_slices=True,
            include_code_context=False,  # Skip code context for faster testing
            llm_friendly=False,  # Skip LLM package for faster testing
            max_entities=5
        )
        
        print(f"📝 Request: {request.user_query}")
        print(f"🎯 Focus entities: {request.focus_entities}")
        
        # Process the request
        print("\n🔄 Processing request...")
        result = handler.process_ir_context_request(request)
        
        # Check results
        print(f"\n📊 RESULTS:")
        print(f"   Result keys: {list(result.keys())}")
        
        if 'error' in result:
            print(f"❌ ERROR: {result['error']}")
            return
        
        # Check context bundle
        context_bundle = result.get('context_bundle', {})
        total_entities = context_bundle.get('total_entities', 0)
        print(f"   Total entities selected: {total_entities}")
        
        # Check IR slices
        ir_slices = result.get('ir_slices', [])
        print(f"   IR slices: {len(ir_slices)}")
        
        # Look for apply_edits specifically
        found_apply_edits = False
        found_entities = []
        
        for ir_slice in ir_slices:
            entity_name = ir_slice.get('entity_name', '')
            entity_type = ir_slice.get('entity_type', '')
            relevance_score = ir_slice.get('relevance_score', 0)
            
            found_entities.append(f"{entity_name} ({entity_type}) - {relevance_score:.1f}")
            
            if 'apply_edits' in entity_name.lower():
                found_apply_edits = True
        
        print(f"\n🎯 FOUND ENTITIES:")
        for entity in found_entities[:10]:  # Show top 10
            print(f"   - {entity}")
        
        if found_apply_edits:
            print(f"\n✅ SUCCESS: apply_edits function found!")
        else:
            print(f"\n❌ FAILURE: apply_edits function NOT found!")
            print(f"   This means the nuclear option is not working properly")
        
        # Check selection rationale
        selection_rationale = context_bundle.get('selection_rationale', '')
        if 'NUCLEAR OPTION' in selection_rationale or 'Direct entity search' in selection_rationale:
            print(f"✅ Nuclear option was used!")
        else:
            print(f"❌ Nuclear option was NOT used!")
            print(f"   Selection method: {selection_rationale[:200]}...")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_hierarchical_selector_directly():
    """Test the hierarchical selector directly"""
    print("\n🔧 TESTING HIERARCHICAL SELECTOR DIRECTLY")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Load IR data
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        with open(latest_ir_file, 'r') as f:
            ir_data = json.load(f)
        
        # Test the selector
        selector = HierarchicalContextSelector()
        
        result = selector.select_hierarchical_context(
            ir_data=ir_data,
            user_query='How does aider apply edits to files?',
            focus_entities=None,
            max_entities=5
        )
        
        selected_entities = result.get('selected_entities', [])
        print(f"\n📊 HIERARCHICAL SELECTOR RESULTS:")
        print(f"   Selected entities: {len(selected_entities)}")
        
        found_apply_edits = False
        for entity in selected_entities:
            name = entity.get('name', '')
            entity_type = entity.get('type', '')
            score = entity.get('relevance_score', 0)
            
            print(f"   - {name} ({entity_type}) - Score: {score:.1f}")
            
            if 'apply_edits' in name.lower():
                found_apply_edits = True
        
        if found_apply_edits:
            print(f"\n✅ SUCCESS: Hierarchical selector found apply_edits!")
        else:
            print(f"\n❌ FAILURE: Hierarchical selector did NOT find apply_edits!")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_nuclear_option()
    test_hierarchical_selector_directly()
    
    print("\n🎯 NUCLEAR OPTION TEST COMPLETE")
    print("If apply_edits is not found, the nuclear option needs more work!")
