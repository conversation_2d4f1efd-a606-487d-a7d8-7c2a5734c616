#!/usr/bin/env python3
"""
Simple test to verify the nuclear option works without loading huge IR files
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_nuclear_option_simple():
    """Test the nuclear option with a small mock dataset"""
    print("🔧 TESTING NUCLEAR OPTION WITH MOCK DATA")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Create a small mock IR dataset
        mock_ir_data = {
            "modules": [
                {
                    "name": "base_coder",
                    "entities": [
                        {"name": "apply_edits", "type": "function", "file_path": "base_coder.py"},
                        {"name": "get_edits", "type": "function", "file_path": "base_coder.py"},
                        {"name": "run", "type": "function", "file_path": "base_coder.py"},
                        {"name": "send_message", "type": "function", "file_path": "base_coder.py"},
                        {"name": "get_input", "type": "function", "file_path": "base_coder.py"},
                        {"name": "GitRepo", "type": "class", "file_path": "git.py"},
                        {"name": "files", "type": "variable", "file_path": "base_coder.py"},
                        {"name": "edits", "type": "variable", "file_path": "base_coder.py"},
                        {"name": "classes", "type": "variable", "file_path": "base_coder.py"},
                    ]
                }
            ]
        }
        
        print(f"📊 Mock IR data created with {len(mock_ir_data['modules'][0]['entities'])} entities")
        
        # Test the selector
        selector = HierarchicalContextSelector()
        
        # Test 1: Apply edits query
        print(f"\n🧪 TEST 1: Apply edits query")
        result1 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='How does aider apply edits to files?',
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities1 = result1.get('selected_entities', [])
        print(f"   Selected {len(selected_entities1)} entities:")
        
        found_apply_edits = False
        for entity in selected_entities1:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            print(f"   - {name}: {score:.1f}")
            
            if 'apply_edits' in name.lower():
                found_apply_edits = True
                print(f"     ✅ FOUND apply_edits!")
        
        if not found_apply_edits:
            print(f"   ❌ apply_edits NOT found in results!")
        
        # Test 2: Chat loop query
        print(f"\n🧪 TEST 2: Chat loop query")
        result2 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='How does the chat loop work in aider?',
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities2 = result2.get('selected_entities', [])
        print(f"   Selected {len(selected_entities2)} entities:")
        
        found_run = False
        for entity in selected_entities2:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            print(f"   - {name}: {score:.1f}")
            
            if name.lower() in ['run', 'send_message', 'get_input']:
                found_run = True
                print(f"     ✅ FOUND chat function: {name}!")
        
        if not found_run:
            print(f"   ❌ Chat functions NOT found in results!")
        
        # Test 3: Git operations query
        print(f"\n🧪 TEST 3: Git operations query")
        result3 = selector.select_hierarchical_context(
            ir_data=mock_ir_data,
            user_query='What classes handle git operations?',
            focus_entities=None,
            max_entities=3
        )
        
        selected_entities3 = result3.get('selected_entities', [])
        print(f"   Selected {len(selected_entities3)} entities:")
        
        found_git = False
        for entity in selected_entities3:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            print(f"   - {name}: {score:.1f}")
            
            if 'git' in name.lower():
                found_git = True
                print(f"     ✅ FOUND git class: {name}!")
        
        if not found_git:
            print(f"   ❌ Git classes NOT found in results!")
        
        # Summary
        print(f"\n📊 NUCLEAR OPTION TEST SUMMARY:")
        print(f"   Test 1 (apply_edits): {'✅ PASS' if found_apply_edits else '❌ FAIL'}")
        print(f"   Test 2 (chat loop): {'✅ PASS' if found_run else '❌ FAIL'}")
        print(f"   Test 3 (git operations): {'✅ PASS' if found_git else '❌ FAIL'}")
        
        if found_apply_edits and found_run and found_git:
            print(f"\n🎉 ALL TESTS PASSED! Nuclear option is working!")
        else:
            print(f"\n❌ SOME TESTS FAILED! Nuclear option needs fixes!")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

def test_query_term_extraction():
    """Test the query term extraction specifically"""
    print(f"\n🔧 TESTING QUERY TERM EXTRACTION")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        selector = HierarchicalContextSelector()
        
        test_queries = [
            'How does aider apply edits to files?',
            'How does the chat loop work in aider?',
            'What classes handle git operations?'
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: {query}")
            terms = selector._extract_query_terms(query)
            print(f"   Terms: {terms}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_nuclear_option_simple()
    test_query_term_extraction()
    
    print(f"\n🎯 SIMPLE NUCLEAR OPTION TEST COMPLETE")
