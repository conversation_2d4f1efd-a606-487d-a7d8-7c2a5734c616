#!/usr/bin/env python3
"""
Test why PositionOpener and PositionCloser classes aren't found for position management query
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_position_query_analysis():
    """Test the position management query to see what's happening"""
    print("🔍 TESTING POSITION QUERY ANALYSIS")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        # Test query
        query = "how does the system manage position?"
        
        selector = HierarchicalContextSelector()
        
        # Test query term extraction
        print(f"📝 Query: '{query}'")
        query_terms = selector._extract_query_terms(query)
        print(f"🔍 Extracted query terms: {query_terms}")
        
        # Test intelligent keywords
        intelligent_keywords = selector._extract_intelligent_keywords(query)
        print(f"🧠 Intelligent keywords: {intelligent_keywords}")
        
        # Test query intent
        query_intent = selector._analyze_query_intent(query)
        print(f"🎯 Query intent: {query_intent}")
        
        # Create mock entities to test matching
        mock_entities = [
            {'name': 'PositionOpener', 'type': 'class'},
            {'name': 'PositionCloser', 'type': 'class'},
            {'name': 'position_manager', 'type': 'function'},
            {'name': 'manage_position', 'type': 'function'},
            {'name': 'open_position', 'type': 'function'},
            {'name': 'close_position', 'type': 'function'},
            {'name': 'DatabaseManager', 'type': 'class'},
            {'name': 'TelegramManager', 'type': 'class'},
            {'name': 'some_random_function', 'type': 'function'},
        ]
        
        print(f"\n🧪 Testing entity matching:")
        
        # Test direct relevance calculation
        for entity in mock_entities:
            entity_name = entity['name']
            entity_type = entity['type']
            
            # Test direct relevance
            relevance = selector._calculate_direct_relevance(
                entity_name, entity_type, "", intelligent_keywords, {}
            )
            
            print(f"   {entity_name} ({entity_type}): relevance = {relevance:.1f}")
        
        # Test the nuclear option matching
        print(f"\n🚀 Testing nuclear option matching:")
        relevant_entities = selector._find_directly_relevant_entities(
            mock_entities, query_terms, {}
        )
        
        print(f"   Found {len(relevant_entities)} relevant entities:")
        for entity in relevant_entities:
            name = entity.get('name', '')
            score = entity.get('relevance_score', 0)
            matches = entity.get('matches', [])
            print(f"      {name}: {score} - {matches}")
        
        # Check if PositionOpener/PositionCloser were found
        position_classes = [e for e in relevant_entities if 'Position' in e.get('name', '')]
        
        if position_classes:
            print(f"\n✅ SUCCESS: Found position classes!")
            for cls in position_classes:
                print(f"   {cls['name']}: score {cls['relevance_score']}")
        else:
            print(f"\n❌ PROBLEM: No position classes found!")
            print(f"   This explains why they weren't in the context package")
        
        return len(position_classes) > 0
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_position_keyword_matching():
    """Test specific keyword matching for position-related terms"""
    print(f"\n🔬 TESTING POSITION KEYWORD MATCHING")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        
        selector = HierarchicalContextSelector()
        
        # Test different variations of position queries
        test_queries = [
            "how does the system manage position?",
            "position management",
            "PositionOpener class",
            "position opener",
            "open position",
            "close position"
        ]
        
        for query in test_queries:
            print(f"\n📝 Query: '{query}'")
            
            # Extract terms
            terms = selector._extract_query_terms(query)
            keywords = selector._extract_intelligent_keywords(query)
            
            print(f"   Terms: {terms}")
            print(f"   Keywords: {keywords}")
            
            # Test if 'position' is in the terms
            has_position = any('position' in term.lower() for term in terms + keywords)
            print(f"   Contains 'position': {has_position}")
            
            # Test matching against PositionOpener
            entity_name = "PositionOpener"
            matches = []
            
            for term in terms + keywords:
                term_lower = term.lower()
                entity_lower = entity_name.lower()
                
                if term_lower == entity_lower:
                    matches.append(f"EXACT:{term}")
                elif term_lower in entity_lower:
                    matches.append(f"CONTAINS:{term}")
                elif entity_lower in term_lower:
                    matches.append(f"REVERSE:{term}")
            
            print(f"   PositionOpener matches: {matches}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_ir_data_search():
    """Test searching for position classes in real IR data"""
    print(f"\n🗂️ TESTING REAL IR DATA SEARCH")
    print("=" * 60)
    
    try:
        import json
        
        # Find the latest IR data file
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return False
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        with open(latest_ir_file, 'r', encoding='utf-8') as f:
            ir_data = json.load(f)
        
        # Search for position-related entities
        position_entities = []
        
        for module in ir_data.get('modules', []):
            module_name = module.get('name', '')
            for entity in module.get('entities', []):
                entity_name = entity.get('name', '')
                entity_type = entity.get('type', '')
                
                # Look for position-related entities
                if 'position' in entity_name.lower():
                    position_entities.append({
                        'name': entity_name,
                        'type': entity_type,
                        'module': module_name,
                        'file': module.get('file', '')
                    })
        
        print(f"🔍 Found {len(position_entities)} position-related entities:")
        for entity in position_entities:
            print(f"   {entity['name']} ({entity['type']}) in {entity['module']}")
        
        # Check specifically for PositionOpener and PositionCloser
        opener = [e for e in position_entities if 'opener' in e['name'].lower()]
        closer = [e for e in position_entities if 'closer' in e['name'].lower()]
        
        print(f"\n🎯 Position management classes:")
        print(f"   Openers: {[e['name'] for e in opener]}")
        print(f"   Closers: {[e['name'] for e in closer]}")
        
        if opener or closer:
            print(f"✅ Position classes exist in IR data!")
            return True
        else:
            print(f"❌ No PositionOpener/PositionCloser found in IR data")
            print(f"   This means they're not in the codebase being analyzed")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 DIAGNOSING POSITION QUERY ISSUE")
    print("=" * 80)
    
    # Test 1: Query analysis
    query_success = test_position_query_analysis()
    
    # Test 2: Keyword matching
    keyword_success = test_position_keyword_matching()
    
    # Test 3: Real IR data search
    ir_success = test_real_ir_data_search()
    
    print(f"\n🏆 DIAGNOSTIC RESULTS:")
    print(f"   Query analysis: {'✅' if query_success else '❌'}")
    print(f"   Keyword matching: {'✅' if keyword_success else '❌'}")
    print(f"   IR data search: {'✅' if ir_success else '❌'}")
    
    if not ir_success:
        print(f"\n💡 LIKELY CAUSE:")
        print(f"   PositionOpener/PositionCloser classes are not in the current codebase")
        print(f"   The system is analyzing the aider codebase, not your trading system")
    elif not query_success:
        print(f"\n💡 LIKELY CAUSE:")
        print(f"   The query term extraction or semantic matching is broken")
        print(f"   Need to fix the keyword extraction logic")
    else:
        print(f"\n💡 LIKELY CAUSE:")
        print(f"   The semantic scoring is not prioritizing position classes correctly")
        print(f"   Need to enhance the relevance scoring algorithm")
