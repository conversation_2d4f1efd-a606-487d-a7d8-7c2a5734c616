#!/usr/bin/env python3
"""
Test Queries for Enhanced Metadata System Comprehensive Testing

This module contains all 50 test queries organized by category as specified in task.txt.
Each query includes the expected strategy and category for validation.
"""

from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class TestQuery:
    """Represents a single test query with metadata."""
    id: int
    query: str
    category: str
    expected_strategy: str
    description: str = ""


class TestQueries:
    """Container for all test queries organized by category."""
    
    @staticmethod
    def get_all_queries() -> List[TestQuery]:
        """
        Get all 50 test queries organized by category.
        
        Returns:
            List of TestQuery objects
        """
        queries = []
        
        # Category 1: Architecture Overview (10 queries)
        architecture_queries = [
            TestQuery(1, "What is the overall architecture of this codebase?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "System-wide architectural understanding"),
            TestQuery(2, "How are the main components organized in this system?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Component organization analysis"),
            TestQuery(3, "What are the key architectural layers and their responsibilities?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Layer responsibility mapping"),
            TestQuery(4, "Can you explain the high-level design patterns used?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Design pattern identification"),
            TestQuery(5, "What is the system's modular structure?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Modular structure analysis"),
            TestQuery(6, "How does data flow through the main components?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Data flow understanding"),
            TestQuery(7, "What are the primary entry points of the application?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Entry point identification"),
            TestQuery(8, "How is the system structured for scalability?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Scalability architecture"),
            TestQuery(9, "What are the core architectural decisions made?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Architectural decision analysis"),
            TestQuery(10, "Can you provide an architectural overview diagram explanation?", 
                     "Architecture Overview", "ARCHITECTURE_OVERVIEW",
                     "Diagram-based architecture explanation")
        ]
        
        # Category 2: Workflow Analysis (10 queries)
        workflow_queries = [
            TestQuery(11, "How does user authentication work in this system?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Authentication workflow analysis"),
            TestQuery(12, "What happens when a file is processed?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "File processing workflow"),
            TestQuery(13, "Can you trace the request handling workflow?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Request handling flow"),
            TestQuery(14, "How does the error handling process work?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Error handling workflow"),
            TestQuery(15, "What is the data validation workflow?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Data validation process"),
            TestQuery(16, "How does the caching mechanism operate?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Caching workflow analysis"),
            TestQuery(17, "Can you explain the deployment process flow?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Deployment workflow"),
            TestQuery(18, "What happens during system initialization?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Initialization workflow"),
            TestQuery(19, "How does the logging workflow function?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Logging process analysis"),
            TestQuery(20, "What is the user session management process?", 
                     "Workflow Analysis", "WORKFLOW_FOCUSED",
                     "Session management workflow")
        ]
        
        # Category 3: Component Deep Dive (10 queries)
        component_queries = [
            TestQuery(21, "How does the FileHandler class work?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "FileHandler component analysis"),
            TestQuery(22, "What does the DatabaseManager component do?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "DatabaseManager functionality"),
            TestQuery(23, "Can you analyze the ConfigurationLoader class?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "ConfigurationLoader analysis"),
            TestQuery(24, "How is the ResponseGenerator implemented?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "ResponseGenerator implementation"),
            TestQuery(25, "What functionality does the UserService provide?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "UserService functionality analysis"),
            TestQuery(26, "How does the CacheManager class operate?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "CacheManager operation analysis"),
            TestQuery(27, "Can you explain the ValidationEngine component?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "ValidationEngine explanation"),
            TestQuery(28, "What does the SecurityHandler class handle?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "SecurityHandler responsibilities"),
            TestQuery(29, "How is the EventDispatcher implemented?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "EventDispatcher implementation"),
            TestQuery(30, "What does the DataProcessor class process?", 
                     "Component Deep Dive", "CLUSTER_DEEP_DIVE",
                     "DataProcessor functionality")
        ]
        
        # Category 4: Cross-Cutting Concerns (10 queries)
        cross_cutting_queries = [
            TestQuery(31, "How is error handling implemented across the system?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "System-wide error handling"),
            TestQuery(32, "What logging mechanisms are used throughout?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Logging infrastructure analysis"),
            TestQuery(33, "How is configuration management handled?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Configuration management system"),
            TestQuery(34, "What security measures are implemented?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Security infrastructure"),
            TestQuery(35, "How does the system handle data validation?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Data validation infrastructure"),
            TestQuery(36, "What monitoring and metrics are in place?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Monitoring infrastructure"),
            TestQuery(37, "How is dependency injection implemented?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Dependency injection system"),
            TestQuery(38, "What caching strategies are used?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Caching infrastructure"),
            TestQuery(39, "How does the system handle async operations?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Async operation handling"),
            TestQuery(40, "What utility functions are available?", 
                     "Cross-Cutting Concerns", "CROSS_CUTTING",
                     "Utility function analysis")
        ]
        
        # Category 5: Integration & External Systems (5 queries)
        integration_queries = [
            TestQuery(41, "How does the system integrate with external APIs?", 
                     "Integration & External Systems", "WORKFLOW_FOCUSED",
                     "External API integration"),
            TestQuery(42, "What database connections are established?", 
                     "Integration & External Systems", "CROSS_CUTTING",
                     "Database connection management"),
            TestQuery(43, "How are third-party services consumed?", 
                     "Integration & External Systems", "WORKFLOW_FOCUSED",
                     "Third-party service integration"),
            TestQuery(44, "What external configurations are required?", 
                     "Integration & External Systems", "CROSS_CUTTING",
                     "External configuration management"),
            TestQuery(45, "How does the system handle external data sources?", 
                     "Integration & External Systems", "WORKFLOW_FOCUSED",
                     "External data source handling")
        ]
        
        # Category 6: Debugging & Maintenance (5 queries)
        debugging_queries = [
            TestQuery(46, "Where should I look to debug connection issues?", 
                     "Debugging & Maintenance", "CLUSTER_DEEP_DIVE",
                     "Connection debugging guidance"),
            TestQuery(47, "How can I trace performance bottlenecks?", 
                     "Debugging & Maintenance", "WORKFLOW_FOCUSED",
                     "Performance bottleneck tracing"),
            TestQuery(48, "What components handle system health checks?", 
                     "Debugging & Maintenance", "CLUSTER_DEEP_DIVE",
                     "Health check component analysis"),
            TestQuery(49, "Where are the main error logging points?", 
                     "Debugging & Maintenance", "CROSS_CUTTING",
                     "Error logging point identification"),
            TestQuery(50, "How can I monitor system resource usage?", 
                     "Debugging & Maintenance", "WORKFLOW_FOCUSED",
                     "Resource monitoring guidance")
        ]
        
        # Combine all queries
        queries.extend(architecture_queries)
        queries.extend(workflow_queries)
        queries.extend(component_queries)
        queries.extend(cross_cutting_queries)
        queries.extend(integration_queries)
        queries.extend(debugging_queries)
        
        return queries
    
    @staticmethod
    def get_queries_by_category(category: str) -> List[TestQuery]:
        """
        Get queries filtered by category.
        
        Args:
            category: Category name to filter by
            
        Returns:
            List of TestQuery objects for the specified category
        """
        all_queries = TestQueries.get_all_queries()
        return [q for q in all_queries if q.category == category]
    
    @staticmethod
    def get_category_summary() -> Dict[str, int]:
        """
        Get summary of queries by category.
        
        Returns:
            Dictionary mapping category names to query counts
        """
        all_queries = TestQueries.get_all_queries()
        summary = {}
        for query in all_queries:
            summary[query.category] = summary.get(query.category, 0) + 1
        return summary


if __name__ == "__main__":
    # Test the query loading
    queries = TestQueries.get_all_queries()
    print(f"✅ Loaded {len(queries)} test queries")
    
    summary = TestQueries.get_category_summary()
    print("\n📊 Query Distribution by Category:")
    for category, count in summary.items():
        print(f"   {category}: {count} queries")
