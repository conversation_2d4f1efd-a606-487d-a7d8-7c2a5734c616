#!/usr/bin/env python3
"""
Test the real context system to see if it's using our nuclear option
"""

import sys
import os
import json
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_real_context_system():
    """Test if the real context system is using our nuclear option"""
    print("🔧 TESTING REAL CONTEXT SYSTEM")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Create a handler
        handler = ContextRequestHandler('.')
        
        # Create a simple request that should be fast
        request = IRContextRequest(
            user_query='How does aider apply edits to files?',
            task_description='Find edit functions',
            task_type='debugging',
            focus_entities=['apply_edits'],
            max_tokens=1000,
            include_ir_slices=True,
            include_code_context=False,  # Skip code context for speed
            llm_friendly=False,  # Skip LLM package for speed
            max_entities=3
        )
        
        print(f"📝 Request: {request.user_query}")
        print(f"🎯 Focus entities: {request.focus_entities}")
        print(f"⏱️ Starting request processing...")
        
        # Set a timeout to avoid hanging
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("Request timed out!")
        
        # Set 30 second timeout
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(30)
        
        try:
            # Process the request
            result = handler.process_ir_context_request(request)
            signal.alarm(0)  # Cancel timeout
            
            # Check results
            print(f"\n📊 RESULTS:")
            print(f"   Result keys: {list(result.keys())}")
            
            if 'error' in result:
                print(f"❌ ERROR: {result['error']}")
                return False
            
            # Check IR slices
            ir_slices = result.get('ir_slices', [])
            print(f"   IR slices: {len(ir_slices)}")
            
            # Look for apply_edits specifically
            found_apply_edits = False
            found_entities = []
            
            for ir_slice in ir_slices:
                entity_name = ir_slice.get('entity_name', '')
                entity_type = ir_slice.get('entity_type', '')
                relevance_score = ir_slice.get('relevance_score', 0)
                
                found_entities.append(f"{entity_name} ({entity_type}) - {relevance_score:.1f}")
                
                if 'apply_edits' in entity_name.lower():
                    found_apply_edits = True
            
            print(f"\n🎯 FOUND ENTITIES:")
            for entity in found_entities:
                print(f"   - {entity}")
            
            if found_apply_edits:
                print(f"\n✅ SUCCESS: apply_edits function found!")
                print(f"   The nuclear option is working in the real system!")
                return True
            else:
                print(f"\n❌ FAILURE: apply_edits function NOT found!")
                print(f"   The nuclear option is NOT working in the real system!")
                return False
                
        except TimeoutError:
            signal.alarm(0)
            print(f"\n⏱️ TIMEOUT: Request took longer than 30 seconds!")
            print(f"   This suggests the system is hanging on IR data loading")
            print(f"   The nuclear option might be working, but IR loading is the bottleneck")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ir_loading_performance():
    """Test how long it takes to load IR data"""
    print(f"\n🔧 TESTING IR LOADING PERFORMANCE")
    print("=" * 60)
    
    try:
        import time
        
        # Find the latest IR file
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        file_size = latest_ir_file.stat().st_size / (1024 * 1024)  # MB
        
        print(f"📊 Testing IR file: {latest_ir_file}")
        print(f"   File size: {file_size:.1f} MB")
        
        # Test loading time
        start_time = time.time()
        
        print(f"⏱️ Loading IR data...")
        with open(latest_ir_file, 'r', encoding='utf-8') as f:
            ir_data = json.load(f)
        
        load_time = time.time() - start_time
        
        modules = ir_data.get('modules', [])
        total_entities = sum(len(module.get('entities', [])) for module in modules)
        
        print(f"✅ IR data loaded in {load_time:.2f} seconds")
        print(f"   Modules: {len(modules):,}")
        print(f"   Total entities: {total_entities:,}")
        
        if load_time > 10:
            print(f"⚠️ WARNING: IR loading is slow ({load_time:.2f}s)")
            print(f"   This could be causing the context system to hang")
        else:
            print(f"✅ IR loading is reasonable ({load_time:.2f}s)")
        
        # Check for apply_edits
        found_apply_edits = []
        for module in modules:
            entities = module.get('entities', [])
            for entity in entities:
                name = entity.get('name', '')
                if 'apply_edits' in name.lower():
                    found_apply_edits.append({
                        'name': name,
                        'type': entity.get('type', ''),
                        'module': module.get('name', '')
                    })
        
        print(f"\n🎯 Found apply_edits entities in IR: {len(found_apply_edits)}")
        for entity in found_apply_edits[:5]:  # Show first 5
            print(f"   - {entity['name']} ({entity['type']}) in {entity['module']}")
        
        if found_apply_edits:
            print(f"✅ apply_edits exists in IR data - nuclear option should find it!")
        else:
            print(f"❌ apply_edits NOT found in IR data - that's the problem!")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Test IR loading first
    test_ir_loading_performance()
    
    # Then test the real system
    success = test_real_context_system()
    
    print(f"\n🎯 REAL CONTEXT SYSTEM TEST COMPLETE")
    if success:
        print(f"✅ The nuclear option is working in the real system!")
        print(f"   Context packages should be changing based on user queries.")
    else:
        print(f"❌ The nuclear option is NOT working in the real system!")
        print(f"   This explains why context packages are not changing.")
