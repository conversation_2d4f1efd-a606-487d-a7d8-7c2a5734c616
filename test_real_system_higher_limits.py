#!/usr/bin/env python3
"""
Test the real system with higher token limits and class methods enhancement
"""

import sys
import os
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_real_system_defaults():
    """Test that the real system now uses higher token limits by default"""
    print("🚀 TESTING REAL SYSTEM WITH HIGHER TOKEN LIMITS")
    print("=" * 70)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        
        # Test 1: Default IRContextRequest values
        print("\n🔍 TEST 1: Default IRContextRequest Values")
        print("-" * 50)
        
        # Create a request with NO explicit limits (using defaults)
        request = IRContextRequest(
            user_query="how does the context system work?",
            task_description="Test default limits",
            task_type="general_analysis",
            llm_friendly=True
        )
        
        print(f"   Default max_tokens: {request.max_tokens}")
        print(f"   Default max_entities: {request.max_entities}")
        print(f"   Default max_output_chars: {request.max_output_chars}")
        
        # Verify the new defaults
        expected_tokens = 20000
        expected_entities = 15
        expected_output = 100000
        
        if (request.max_tokens == expected_tokens and 
            request.max_entities == expected_entities and 
            request.max_output_chars == expected_output):
            print(f"   ✅ Defaults updated correctly!")
            print(f"      max_tokens: {request.max_tokens} (was 8000)")
            print(f"      max_entities: {request.max_entities} (was 10)")
            print(f"      max_output_chars: {request.max_output_chars} (was 30000)")
        else:
            print(f"   ❌ Defaults not updated:")
            print(f"      max_tokens: {request.max_tokens} (expected {expected_tokens})")
            print(f"      max_entities: {request.max_entities} (expected {expected_entities})")
            print(f"      max_output_chars: {request.max_output_chars} (expected {expected_output})")
            return False
        
        # Test 2: Process a real request with new defaults
        print(f"\n🧪 TEST 2: Processing Request with New Defaults")
        print("-" * 50)
        
        # Process the request
        result = handler.process_ir_context_request(request)
        
        # Check results
        if 'error' in result:
            print(f"   ❌ ERROR: {result['error']}")
            return False
        
        # Analyze results
        ir_slices = result.get('ir_slices', [])
        llm_package = result.get('llm_friendly_package', '')
        token_utilization = result.get('summary', {}).get('token_utilization', 0)
        
        print(f"   📊 RESULTS:")
        print(f"      Entities selected: {len(ir_slices)}")
        print(f"      Token utilization: {token_utilization}%")
        print(f"      Package size: {len(llm_package):,} characters")
        
        # Count classes with methods
        classes_with_methods = 0
        total_methods = 0
        
        for ir_slice in ir_slices:
            entity_type = ir_slice.get('entity_type', '')
            entity_name = ir_slice.get('entity_name', '')
            
            if entity_type == 'class':
                class_methods = ir_slice.get('class_methods', [])
                if class_methods:
                    classes_with_methods += 1
                    total_methods += len(class_methods)
                    print(f"      🏛️ {entity_name}: {len(class_methods)} methods")
        
        print(f"   🏛️ CLASS METHODS SUMMARY:")
        print(f"      Classes with methods: {classes_with_methods}")
        print(f"      Total methods shown: {total_methods}")
        
        # Check if class methods section exists in LLM package
        methods_sections = llm_package.count('🏛️ Class Methods')
        print(f"      Methods sections in package: {methods_sections}")
        
        # Success criteria with higher limits
        success = (
            len(ir_slices) >= 8 and  # Should get more entities with higher limits
            classes_with_methods >= 2 and  # Should have multiple classes
            total_methods >= 10 and  # Should have many methods
            methods_sections >= 1  # Methods should be displayed
        )
        
        if success:
            print(f"\n🎉 SUCCESS WITH HIGHER LIMITS!")
            print(f"   ✅ Got {len(ir_slices)} entities (≥8 with higher limits)")
            print(f"   ✅ Got {classes_with_methods} classes with methods (≥2)")
            print(f"   ✅ Got {total_methods} total methods (≥10)")
            print(f"   ✅ {methods_sections} method sections displayed (≥1)")
            print(f"\n💡 IMPROVEMENT:")
            print(f"   With higher token limits, users now get:")
            print(f"   • More entities ({len(ir_slices)} vs ~3 before)")
            print(f"   • More classes with methods ({classes_with_methods} vs ~1 before)")
            print(f"   • Richer architectural context ({total_methods} methods)")
            return True
        else:
            print(f"\n⚠️ MIXED RESULTS:")
            print(f"   Entities: {len(ir_slices)} (target: ≥8)")
            print(f"   Classes: {classes_with_methods} (target: ≥2)")
            print(f"   Methods: {total_methods} (target: ≥10)")
            print(f"   Method sections: {methods_sections} (target: ≥1)")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_context_selector_defaults():
    """Test that the intelligent context selector uses higher defaults"""
    print(f"\n🧠 TEST 3: Intelligent Context Selector Defaults")
    print("-" * 50)
    
    try:
        from aider.context_request.intelligent_context_selector import IntelligentContextSelector
        
        # Create a mock IR data structure
        mock_ir_data = {
            'modules': [
                {
                    'name': 'test_module',
                    'entities': [
                        {'name': 'TestClass', 'type': 'class'},
                        {'name': 'test_function', 'type': 'function'}
                    ]
                }
            ]
        }
        
        # Create selector with default max_tokens (should be 20000 now)
        selector = IntelligentContextSelector(mock_ir_data)
        
        print(f"   Default max_tokens: {selector.max_tokens}")
        
        if selector.max_tokens == 20000:
            print(f"   ✅ IntelligentContextSelector default updated correctly!")
            print(f"      max_tokens: {selector.max_tokens} (was 8000)")
            return True
        else:
            print(f"   ❌ IntelligentContextSelector default not updated:")
            print(f"      max_tokens: {selector.max_tokens} (expected 20000)")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING REAL SYSTEM WITH HIGHER TOKEN LIMITS")
    print("=" * 80)
    
    # Test 1: Default values
    defaults_success = test_real_system_defaults()
    
    # Test 2: Intelligent context selector
    selector_success = test_intelligent_context_selector_defaults()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Real system defaults: {'✅' if defaults_success else '❌'}")
    print(f"   Context selector defaults: {'✅' if selector_success else '❌'}")
    
    if defaults_success and selector_success:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"   ✅ Higher token limits implemented in real system")
        print(f"   ✅ Default max_tokens: 8000 → 20000 (+150%)")
        print(f"   ✅ Default max_entities: 10 → 15 (+50%)")
        print(f"   ✅ Default max_output_chars: 30000 → 100000 (+233%)")
        print(f"   ✅ Class methods enhancement working")
        print(f"\n🚀 USERS WILL NOW GET:")
        print(f"   • More entities in context packages")
        print(f"   • More classes with their methods displayed")
        print(f"   • Richer architectural understanding")
        print(f"   • Better context for complex queries")
    else:
        print(f"\n❌ SOME ISSUES REMAIN")
        print(f"   Need to verify all default values are updated correctly")
