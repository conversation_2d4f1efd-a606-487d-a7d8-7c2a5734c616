#!/usr/bin/env python3
"""
Test the real system without caching to verify enhanced semantic intelligence works
"""

import sys
import os
import time
from pathlib import Path

# Add the aider-main directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "aider-main"))

def test_real_system_without_cache():
    """Test the real context system by bypassing caches"""
    print("🔧 TESTING REAL SYSTEM WITHOUT CACHE")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Clear any existing caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        # Create a handler
        handler = ContextRequestHandler('.')
        
        # Clear instance cache too
        handler.cache.clear()
        handler.cache_timestamps.clear()
        
        # Test different queries
        test_queries = [
            "How does aider apply edits to files?",
            "How does the chat loop work in aider?", 
            "What git operations are available?"
        ]
        
        results = {}
        
        for i, query in enumerate(test_queries):
            print(f"\n🧪 TEST {i+1}: {query}")
            
            # Create a unique request to avoid caching
            request = IRContextRequest(
                user_query=query,
                task_description=f'Test query {i+1}: {query}',
                task_type='debugging',
                focus_entities=None,
                max_tokens=2000,
                include_ir_slices=True,
                include_code_context=False,
                llm_friendly=False,
                max_entities=3
            )
            
            # Force cache bypass by modifying the handler
            original_get_from_cache = handler._get_from_cache
            handler._get_from_cache = lambda x: None  # Always return None (cache miss)
            
            try:
                # Process the request
                result = handler.process_ir_context_request(request)
                
                # Check results
                if 'error' in result:
                    print(f"   ❌ ERROR: {result['error']}")
                    continue
                
                # Get IR slices
                ir_slices = result.get('ir_slices', [])
                entity_names = [slice.get('entity_name', '') for slice in ir_slices]
                
                print(f"   📊 Found {len(ir_slices)} entities: {entity_names}")
                results[query] = entity_names
                
            finally:
                # Restore original cache method
                handler._get_from_cache = original_get_from_cache
        
        # Analyze results
        print(f"\n📊 RESULTS ANALYSIS:")
        unique_results = len(set(frozenset(entities) for entities in results.values()))
        
        for query, entities in results.items():
            short_query = query.split('?')[0] + '?'
            print(f"   {short_query}: {entities}")
        
        print(f"\n   Unique result sets: {unique_results}")
        print(f"   Different results per query: {'✅ YES' if unique_results > 1 else '❌ NO'}")
        
        if unique_results > 1:
            print(f"\n🎉 SUCCESS: Real system produces different results!")
            print(f"   The enhanced semantic intelligence is working!")
            return True
        else:
            print(f"\n❌ FAILURE: Real system produces same results")
            print(f"   Caching or other issues are preventing changes")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_clearing():
    """Test clearing all caches and regenerating IR data"""
    print(f"\n🧹 TESTING CACHE CLEARING")
    print("=" * 60)
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler
        
        # Clear class-level caches
        ContextRequestHandler._ir_cache.clear()
        ContextRequestHandler._ir_cache_timestamps.clear()
        
        print("✅ Cleared class-level IR cache")
        
        # Create handler and clear instance cache
        handler = ContextRequestHandler('.')
        handler.cache.clear()
        handler.cache_timestamps.clear()
        
        print("✅ Cleared instance cache")
        
        # Force IR regeneration by setting a very old timestamp
        if hasattr(handler, '_last_ir_generation'):
            handler._last_ir_generation = 0
        
        print("✅ Reset IR generation timestamp")
        
        # Test a simple query to force IR regeneration
        from aider.context_request.context_request_handler import IRContextRequest
        
        request = IRContextRequest(
            user_query="Test query for cache clearing",
            task_description="Cache clearing test",
            task_type='debugging',
            max_entities=1
        )
        
        print("🔄 Processing test request to regenerate IR...")
        result = handler.process_ir_context_request(request)
        
        if 'error' not in result:
            print("✅ IR regeneration successful")
            return True
        else:
            print(f"❌ IR regeneration failed: {result['error']}")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_hierarchical_selector():
    """Test the hierarchical selector directly with real IR data"""
    print(f"\n🏗️ TESTING HIERARCHICAL SELECTOR DIRECTLY")
    print("=" * 60)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        import json
        
        # Load the latest IR data file
        ir_files = list(Path(".").glob("ir_data_*.json"))
        if not ir_files:
            print("❌ No IR data files found")
            return False
            
        latest_ir_file = max(ir_files, key=lambda f: f.stat().st_mtime)
        print(f"📊 Loading IR data from: {latest_ir_file}")
        
        try:
            with open(latest_ir_file, 'r', encoding='utf-8') as f:
                ir_data = json.load(f)
        except UnicodeDecodeError:
            print("❌ Unicode decode error - IR file may be corrupted")
            return False
        
        selector = HierarchicalContextSelector()
        
        # Test different queries
        test_queries = [
            "How does aider apply edits to files?",
            "How does the chat loop work?",
            "What git operations are available?"
        ]
        
        results = {}
        
        for query in test_queries:
            print(f"\n🧪 Testing: {query}")
            
            try:
                result = selector.select_hierarchical_context(
                    ir_data=ir_data,
                    user_query=query,
                    focus_entities=None,
                    max_entities=3
                )
                
                selected_entities = result.get('selected_entities', [])
                entity_names = [e.get('name', '') for e in selected_entities]
                
                print(f"   Selected: {entity_names}")
                results[query] = entity_names
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
                results[query] = []
        
        # Analyze results
        unique_results = len(set(frozenset(entities) for entities in results.values() if entities))
        
        print(f"\n📊 HIERARCHICAL SELECTOR RESULTS:")
        for query, entities in results.items():
            print(f"   {query}: {entities}")
        
        print(f"\n   Unique result sets: {unique_results}")
        
        if unique_results > 1:
            print(f"✅ Hierarchical selector produces different results!")
            return True
        else:
            print(f"❌ Hierarchical selector produces same/empty results")
            return False
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🎯 TESTING REAL SYSTEM WITHOUT CACHING")
    print("=" * 80)
    
    # Test 1: Clear caches
    cache_success = test_cache_clearing()
    
    # Test 2: Test real system without cache
    real_system_success = test_real_system_without_cache()
    
    # Test 3: Test hierarchical selector directly
    hierarchical_success = test_direct_hierarchical_selector()
    
    print(f"\n🏆 FINAL RESULTS:")
    print(f"   Cache clearing: {'✅' if cache_success else '❌'}")
    print(f"   Real system (no cache): {'✅' if real_system_success else '❌'}")
    print(f"   Hierarchical selector: {'✅' if hierarchical_success else '❌'}")
    
    if real_system_success or hierarchical_success:
        print(f"\n🎉 ENHANCED SEMANTIC INTELLIGENCE IS WORKING!")
        print(f"   The issue is caching - context packages do change when cache is bypassed!")
        print(f"   Solution: Implement cache invalidation or disable caching for context selection")
    else:
        print(f"\n❌ SYSTEM STILL NOT WORKING")
        print(f"   The enhanced semantic intelligence needs more investigation")
