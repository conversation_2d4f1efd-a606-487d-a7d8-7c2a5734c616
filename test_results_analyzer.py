#!/usr/bin/env python3
"""
Test Results Analyzer for Enhanced Metadata System

This module provides comprehensive analysis and reporting capabilities
for the Enhanced Metadata System test results.
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import asdict
from enhanced_metadata_comprehensive_test import TestResult


class TestResultsAnalyzer:
    """
    Analyzes test results and generates comprehensive reports.
    """
    
    def __init__(self, results: List[TestResult]):
        """
        Initialize the analyzer with test results.
        
        Args:
            results: List of TestResult objects to analyze
        """
        self.results = results
        self.analysis_timestamp = datetime.now().isoformat()
    
    def generate_summary_metrics(self) -> Dict[str, Any]:
        """
        Generate summary metrics for all test results.
        
        Returns:
            Dictionary containing summary metrics
        """
        if not self.results:
            return {
                'success_rate': 0.0,
                'average_processing_time': 0.0,
                'metadata_completeness_avg': 0.0,
                'type_icons_coverage': 0.0,
                'strategy_alignment_avg': 0.0,
                'overall_quality_avg': 0.0,
                'total_queries': 0,
                'successful_queries': 0,
                'failed_queries': 0
            }
        
        successful_results = [r for r in self.results if r.success]
        
        # Calculate basic metrics
        success_rate = len(successful_results) / len(self.results)
        avg_processing_time = sum(r.processing_time for r in self.results) / len(self.results)
        
        # Calculate quality metrics (only for successful results)
        if successful_results:
            metadata_avg = sum(
                r.analysis.get('metadata_completeness', 0) for r in successful_results
            ) / len(successful_results)
            
            icons_avg = sum(
                r.analysis.get('type_icons_present', 0) for r in successful_results
            ) / len(successful_results)
            
            strategy_avg = sum(
                r.analysis.get('strategy_indicators', 0) for r in successful_results
            ) / len(successful_results)
            
            quality_avg = sum(
                r.analysis.get('overall_quality_score', 0) for r in successful_results
            ) / len(successful_results)
        else:
            metadata_avg = icons_avg = strategy_avg = quality_avg = 0.0
        
        return {
            'success_rate': success_rate,
            'average_processing_time': avg_processing_time,
            'metadata_completeness_avg': metadata_avg,
            'type_icons_coverage': icons_avg,
            'strategy_alignment_avg': strategy_avg,
            'overall_quality_avg': quality_avg,
            'total_queries': len(self.results),
            'successful_queries': len(successful_results),
            'failed_queries': len(self.results) - len(successful_results)
        }
    
    def generate_category_breakdown(self) -> Dict[str, Dict[str, Any]]:
        """
        Generate breakdown of results by category.
        
        Returns:
            Dictionary mapping categories to their metrics
        """
        categories = {}
        
        for result in self.results:
            category = result.category
            if category not in categories:
                categories[category] = []
            categories[category].append(result)
        
        breakdown = {}
        for category, category_results in categories.items():
            successful = [r for r in category_results if r.success]
            
            if successful:
                avg_quality = sum(
                    r.analysis.get('overall_quality_score', 0) for r in successful
                ) / len(successful)
                avg_time = sum(r.processing_time for r in successful) / len(successful)
            else:
                avg_quality = avg_time = 0.0
            
            breakdown[category] = {
                'total_queries': len(category_results),
                'successful_queries': len(successful),
                'success_rate': len(successful) / len(category_results) if category_results else 0,
                'average_processing_time': avg_time,
                'average_quality_score': avg_quality,
                'failed_queries': len(category_results) - len(successful)
            }
        
        return breakdown
    
    def identify_performance_issues(self) -> List[Dict[str, Any]]:
        """
        Identify performance and quality issues from the results.
        
        Returns:
            List of identified issues with details
        """
        issues = []
        
        # Check for slow queries (>15 seconds)
        slow_queries = [r for r in self.results if r.processing_time > 15.0]
        if slow_queries:
            issues.append({
                'type': 'performance',
                'severity': 'high',
                'description': f'{len(slow_queries)} queries exceeded 15-second processing limit',
                'affected_queries': [r.query_id for r in slow_queries],
                'details': f'Slowest query: {max(slow_queries, key=lambda x: x.processing_time).processing_time:.2f}s'
            })
        
        # Check for low metadata completeness
        successful_results = [r for r in self.results if r.success]
        low_metadata = [
            r for r in successful_results 
            if r.analysis and r.analysis.get('metadata_completeness', 1.0) < 0.95
        ]
        if low_metadata:
            issues.append({
                'type': 'quality',
                'severity': 'medium',
                'description': f'{len(low_metadata)} queries have incomplete metadata (<95%)',
                'affected_queries': [r.query_id for r in low_metadata],
                'details': f'Lowest completeness: {min(low_metadata, key=lambda x: x.analysis.get("metadata_completeness", 1.0)).analysis.get("metadata_completeness", 0):.1%}'
            })
        
        # Check for missing type icons
        missing_icons = [
            r for r in successful_results 
            if r.analysis and r.analysis.get('type_icons_present', 1.0) < 0.8
        ]
        if missing_icons:
            issues.append({
                'type': 'formatting',
                'severity': 'low',
                'description': f'{len(missing_icons)} queries have poor type icon coverage (<80%)',
                'affected_queries': [r.query_id for r in missing_icons],
                'details': 'Type icons improve package readability'
            })
        
        # Check for strategy misalignment
        poor_strategy = [
            r for r in successful_results 
            if r.analysis and r.analysis.get('strategy_indicators', 1.0) < 0.6
        ]
        if poor_strategy:
            issues.append({
                'type': 'strategy',
                'severity': 'high',
                'description': f'{len(poor_strategy)} queries show poor strategy alignment (<60%)',
                'affected_queries': [r.query_id for r in poor_strategy],
                'details': 'Strategy selection may need improvement'
            })
        
        # Check for failed queries
        failed_results = [r for r in self.results if not r.success]
        if failed_results:
            issues.append({
                'type': 'failure',
                'severity': 'critical',
                'description': f'{len(failed_results)} queries failed completely',
                'affected_queries': [r.query_id for r in failed_results],
                'details': 'System reliability needs attention'
            })
        
        return issues
    
    def generate_recommendations(self) -> List[str]:
        """
        Generate improvement recommendations based on analysis.
        
        Returns:
            List of recommendation strings
        """
        recommendations = []
        summary = self.generate_summary_metrics()
        issues = self.identify_performance_issues()
        
        # Performance recommendations
        if summary['average_processing_time'] > 10.0:
            recommendations.append(
                "🚀 Optimize processing performance - average time exceeds 10 seconds"
            )
        
        # Quality recommendations
        if summary['metadata_completeness_avg'] < 0.95:
            recommendations.append(
                "📋 Improve metadata completeness - ensure all required fields are populated"
            )
        
        if summary['type_icons_coverage'] < 0.8:
            recommendations.append(
                "🎨 Enhance type icon coverage - improve visual formatting of packages"
            )
        
        if summary['strategy_alignment_avg'] < 0.7:
            recommendations.append(
                "🎯 Improve strategy selection - better alignment between queries and strategies"
            )
        
        # Reliability recommendations
        if summary['success_rate'] < 0.9:
            recommendations.append(
                "🔧 Address system reliability - too many queries are failing"
            )
        
        # Specific issue recommendations
        critical_issues = [i for i in issues if i['severity'] == 'critical']
        if critical_issues:
            recommendations.append(
                "🚨 Address critical issues immediately - system has fundamental problems"
            )
        
        high_issues = [i for i in issues if i['severity'] == 'high']
        if high_issues:
            recommendations.append(
                "⚠️ Resolve high-priority issues - significant impact on quality"
            )
        
        return recommendations
    
    def save_detailed_results(self, filename: str = None) -> str:
        """
        Save detailed results to JSON file.
        
        Args:
            filename: Optional filename, auto-generated if not provided
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_metadata_test_results_{timestamp}.json"
        
        # Convert results to serializable format
        serializable_results = []
        for result in self.results:
            result_dict = asdict(result)
            serializable_results.append(result_dict)
        
        # Create comprehensive results structure
        comprehensive_results = {
            'test_run_info': {
                'timestamp': self.analysis_timestamp,
                'total_queries': len(self.results),
                'test_environment': 'development',
                'system_version': '1.0.0'
            },
            'summary_metrics': self.generate_summary_metrics(),
            'category_breakdown': self.generate_category_breakdown(),
            'performance_issues': self.identify_performance_issues(),
            'recommendations': self.generate_recommendations(),
            'detailed_results': serializable_results
        }
        
        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(comprehensive_results, f, indent=2, ensure_ascii=False)
        
        return filename


if __name__ == "__main__":
    # Test the analyzer with empty results
    analyzer = TestResultsAnalyzer([])
    print("✅ Test Results Analyzer initialized successfully")
    
    summary = analyzer.generate_summary_metrics()
    print(f"📊 Summary metrics: {summary}")
