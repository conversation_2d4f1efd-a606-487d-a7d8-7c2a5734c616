"""
Test Semantic Integration
Test the integrated semantic intelligence system in the IR Context System.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest


def test_semantic_integration():
    """Test the semantic integration with the IR Context System."""
    print("🧪 Testing Semantic Integration with IR Context System")
    print("=" * 70)
    
    # Initialize the context request handler
    project_path = "."  # Current directory
    handler = ContextRequestHandler(project_path)
    
    # Create a test request
    test_request = IRContextRequest(
        user_query="How does user authentication work in the system?",
        task_description="Analyze authentication workflow and security components",
        task_type="security_analysis",
        focus_entities=["authenticate", "user", "login", "auth"],
        max_tokens=8000,
        llm_friendly=True,
        include_ir_slices=True,
        include_code_context=True,
        max_output_chars=30000,
        max_entities=8
    )
    
    print(f"🔍 Test Query: {test_request.user_query}")
    print(f"📋 Task Type: {test_request.task_type}")
    print(f"🎯 Focus Entities: {test_request.focus_entities}")
    print(f"📊 Max Entities: {test_request.max_entities}")
    
    try:
        # Process the request using semantic intelligence
        print(f"\n🚀 Processing request with SEMANTIC INTELLIGENCE...")
        result = handler.process_ir_context_request(test_request)
        
        # Analyze the results
        print(f"\n📊 RESULTS ANALYSIS:")
        print(f"   Status: {'✅ Success' if 'error' not in result else '❌ Error'}")
        
        if 'error' in result:
            print(f"   Error: {result['error']}")
            return
        
        # Check context bundle
        context_bundle = result.get('context_bundle', {})
        print(f"   Selected Entities: {context_bundle.get('total_entities', 0)}")
        print(f"   Token Utilization: {result.get('summary', {}).get('token_utilization', 'N/A')}")
        
        # Check semantic analysis
        if 'ir_slices' in result:
            print(f"   IR Slices: {len(result['ir_slices'])}")
            
            # Show semantic explanations
            print(f"\n🧠 SEMANTIC EXPLANATIONS:")
            for i, ir_slice in enumerate(result['ir_slices'][:3], 1):  # Show first 3
                entity_name = ir_slice.get('entity_name', 'Unknown')
                explanation = ir_slice.get('semantic_explanation', 'No explanation')
                relevance = ir_slice.get('relevance_score', 0.0)
                print(f"   {i}. {entity_name} (score: {relevance:.3f})")
                print(f"      → {explanation}")
        
        # Check LLM package
        if 'llm_friendly_package' in result:
            package_size = result.get('package_size_chars', 0)
            compatibility = result.get('llm_compatibility', 'Unknown')
            print(f"\n📦 LLM PACKAGE:")
            print(f"   Size: {package_size:,} characters")
            print(f"   Compatibility: {compatibility}")
            
            # Show package preview
            package = result['llm_friendly_package']
            preview_length = 500
            print(f"\n📄 PACKAGE PREVIEW (first {preview_length} chars):")
            print("-" * 50)
            print(package[:preview_length])
            if len(package) > preview_length:
                print("... (content continues)")
            print("-" * 50)
        
        # Check selection rationale
        selection_rationale = context_bundle.get('selection_rationale', '')
        if selection_rationale and '🧠 SEMANTIC INTELLIGENCE' in selection_rationale:
            print(f"\n🎯 SEMANTIC SELECTION CONFIRMED:")
            print("   ✅ Using semantic intelligence instead of keyword matching")
            print("   ✅ Query intent analysis performed")
            print("   ✅ Component purpose analysis completed")
            print("   ✅ Architectural significance considered")
        else:
            print(f"\n⚠️  WARNING: May not be using semantic selection")
        
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


def test_comparison_queries():
    """Test multiple queries to show semantic understanding."""
    print("\n" + "=" * 70)
    print("🔬 Testing Multiple Queries for Semantic Understanding")
    print("=" * 70)
    
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    test_queries = [
        {
            'query': "How does user authentication work?",
            'task_type': "security_analysis",
            'focus_entities': ["auth", "user"]
        },
        {
            'query': "What handles error processing and validation?",
            'task_type': "debugging",
            'focus_entities': ["error", "validate"]
        },
        {
            'query': "How to add a new feature to the system?",
            'task_type': "feature_development",
            'focus_entities': ["add", "feature"]
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {test_case['query']}")
        
        request = IRContextRequest(
            user_query=test_case['query'],
            task_description=f"Analysis for: {test_case['query']}",
            task_type=test_case['task_type'],
            focus_entities=test_case['focus_entities'],
            max_tokens=4000,
            llm_friendly=False,  # Skip LLM package for speed
            include_ir_slices=True,
            include_code_context=False,
            max_entities=5
        )
        
        try:
            result = handler.process_ir_context_request(request)
            
            if 'error' not in result:
                context_bundle = result.get('context_bundle', {})
                entities = context_bundle.get('total_entities', 0)
                print(f"   ✅ Selected {entities} entities using semantic analysis")
                
                # Show selection rationale preview
                rationale = context_bundle.get('selection_rationale', '')
                if 'Query Intent:' in rationale:
                    lines = rationale.split('\n')
                    for line in lines[:3]:  # Show first 3 lines
                        if line.strip():
                            print(f"   📋 {line.strip()}")
            else:
                print(f"   ❌ Error: {result['error']}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")


if __name__ == "__main__":
    test_semantic_integration()
    test_comparison_queries()
