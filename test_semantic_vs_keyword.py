"""
Test: Semantic vs Keyword Matching
Demonstrates the improvement of semantic understanding over keyword matching.
"""

import sys
import os
import json
from typing import Dict, List, Any

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from semantic_context_integration import SemanticContextIntegration
from query_intent_analyzer import QueryIntentAnalyzer
from component_purpose_analyzer import ComponentPurposeAnalyzer


class KeywordMatchingSimulator:
    """Simulates the old keyword-based matching approach for comparison."""
    
    def __init__(self):
        pass
    
    def keyword_based_selection(self, ir_data: Dict, query: str, 
                              focus_entities: List[str] = None, 
                              max_entities: int = 8) -> List[Dict]:
        """
        Simulate keyword-based selection (the old approach).
        """
        print(f"🔤 Keyword-based selection for: {query}")
        
        query_words = set(query.lower().split())
        if focus_entities:
            query_words.update([entity.lower() for entity in focus_entities])
        
        scored_entities = []
        
        # Process all entities in IR data
        for module in ir_data.get('modules', []):
            for func in module.get('functions', []):
                entity_name = func.get('name', '').lower()
                file_path = func.get('file_path', '').lower()
                
                # Simple keyword matching score
                score = 0
                for word in query_words:
                    if word in entity_name:
                        score += 2  # Name match
                    if word in file_path:
                        score += 1  # File path match
                
                if score > 0:
                    scored_entities.append({
                        'entity_name': func.get('name', ''),
                        'score': score,
                        'file_path': func.get('file_path', ''),
                        'method': 'keyword_matching',
                        'explanation': f"Keyword matches: {score}"
                    })
        
        # Sort by score and return top entities
        scored_entities.sort(key=lambda x: x['score'], reverse=True)
        selected = scored_entities[:max_entities]
        
        print(f"   Selected {len(selected)} entities using keyword matching")
        for entity in selected:
            print(f"     - {entity['entity_name']} (score: {entity['score']})")
        
        return selected


def create_realistic_ir_data() -> Dict:
    """Create realistic IR data for testing."""
    return {
        'modules': [
            {
                'module_name': 'user_service',
                'file_path': 'services/user_service.py',
                'functions': [
                    {
                        'name': 'create_user_account',
                        'type': 'function',
                        'calls': ['validate_email', 'hash_password', 'save_to_database'],
                        'used_by': ['registration_handler', 'admin_panel'],
                        'source_code': '''def create_user_account(email, password, profile_data):
    """Create a new user account with validation and security."""
    if not validate_email(email):
        raise ValueError("Invalid email format")
    
    hashed_password = hash_password(password)
    user_data = {
        'email': email,
        'password': hashed_password,
        'profile': profile_data,
        'created_at': datetime.now()
    }
    
    return save_to_database('users', user_data)''',
                        'file_path': 'services/user_service.py',
                        'module_name': 'user_service'
                    },
                    {
                        'name': 'authenticate_user_credentials',
                        'type': 'function',
                        'calls': ['verify_password', 'create_session_token', 'log_login_attempt'],
                        'used_by': ['login_endpoint', 'api_middleware'],
                        'source_code': '''def authenticate_user_credentials(email, password):
    """Authenticate user login credentials and create session."""
    user = find_user_by_email(email)
    if not user:
        log_login_attempt(email, 'user_not_found')
        return None
    
    if verify_password(password, user.password_hash):
        session_token = create_session_token(user.id)
        log_login_attempt(email, 'success')
        return {'user': user, 'token': session_token}
    
    log_login_attempt(email, 'invalid_password')
    return None''',
                        'file_path': 'services/user_service.py',
                        'module_name': 'user_service'
                    }
                ]
            },
            {
                'module_name': 'payment_processor',
                'file_path': 'payment/payment_processor.py',
                'functions': [
                    {
                        'name': 'process_payment_transaction',
                        'type': 'function',
                        'calls': ['validate_payment_data', 'charge_credit_card', 'send_receipt'],
                        'used_by': ['checkout_handler', 'subscription_manager'],
                        'source_code': '''def process_payment_transaction(payment_data, amount):
    """Process a payment transaction with validation and fraud detection."""
    if not validate_payment_data(payment_data):
        raise PaymentValidationError("Invalid payment data")
    
    # Fraud detection
    if detect_fraud(payment_data, amount):
        raise FraudDetectionError("Transaction flagged as suspicious")
    
    # Process the charge
    charge_result = charge_credit_card(payment_data, amount)
    if charge_result.success:
        send_receipt(payment_data.email, charge_result.transaction_id)
        return charge_result
    
    raise PaymentProcessingError("Payment failed")''',
                        'file_path': 'payment/payment_processor.py',
                        'module_name': 'payment_processor'
                    }
                ]
            },
            {
                'module_name': 'data_validator',
                'file_path': 'utils/data_validator.py',
                'functions': [
                    {
                        'name': 'validate_user_input',
                        'type': 'function',
                        'calls': ['sanitize_input', 'check_required_fields', 'validate_formats'],
                        'used_by': ['user_service', 'payment_processor', 'api_handlers'],
                        'source_code': '''def validate_user_input(input_data, validation_rules):
    """Validate user input against specified rules."""
    sanitized_data = sanitize_input(input_data)
    
    # Check required fields
    missing_fields = check_required_fields(sanitized_data, validation_rules.required)
    if missing_fields:
        raise ValidationError(f"Missing required fields: {missing_fields}")
    
    # Validate formats
    format_errors = validate_formats(sanitized_data, validation_rules.formats)
    if format_errors:
        raise ValidationError(f"Format errors: {format_errors}")
    
    return sanitized_data''',
                        'file_path': 'utils/data_validator.py',
                        'module_name': 'data_validator'
                    }
                ]
            },
            {
                'module_name': 'error_handler',
                'file_path': 'core/error_handler.py',
                'functions': [
                    {
                        'name': 'handle_authentication_error',
                        'type': 'function',
                        'calls': ['log_error', 'send_alert', 'create_error_response'],
                        'used_by': ['user_service', 'api_middleware'],
                        'source_code': '''def handle_authentication_error(error, context):
    """Handle authentication-related errors with proper logging and response."""
    error_details = {
        'error_type': 'authentication_failure',
        'message': str(error),
        'context': context,
        'timestamp': datetime.now(),
        'severity': 'medium'
    }
    
    log_error(error_details)
    
    # Send alert for repeated failures
    if context.get('repeated_failure'):
        send_alert('security_team', error_details)
    
    return create_error_response(
        status_code=401,
        message="Authentication failed",
        error_code="AUTH_FAILED"
    )''',
                        'file_path': 'core/error_handler.py',
                        'module_name': 'error_handler'
                    }
                ]
            }
        ]
    }


def run_comparison_test():
    """Run comprehensive comparison between semantic and keyword approaches."""
    print("🧪 Semantic vs Keyword Matching Comparison Test")
    print("=" * 80)
    
    # Create test data
    ir_data = create_realistic_ir_data()
    
    # Initialize both approaches
    semantic_integration = SemanticContextIntegration()
    keyword_simulator = KeywordMatchingSimulator()
    
    # Test queries that demonstrate the difference
    test_queries = [
        {
            'query': "How does user authentication work in the system?",
            'focus_entities': ['authenticate', 'user'],
            'expected_semantic_advantage': "Should find authentication logic, not just keyword matches"
        },
        {
            'query': "What handles payment processing and validation?",
            'focus_entities': ['payment', 'process'],
            'expected_semantic_advantage': "Should understand business logic flow, not just name matches"
        },
        {
            'query': "Why might user registration be failing?",
            'focus_entities': ['user', 'registration'],
            'expected_semantic_advantage': "Should include error handling and validation components"
        },
        {
            'query': "How to add fraud detection to payment flow?",
            'focus_entities': ['fraud', 'payment'],
            'expected_semantic_advantage': "Should understand feature implementation context"
        }
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        query = test_case['query']
        focus_entities = test_case['focus_entities']
        
        print(f"\n{'='*60}")
        print(f"TEST {i}: {query}")
        print(f"Expected Advantage: {test_case['expected_semantic_advantage']}")
        print(f"{'='*60}")
        
        # Test keyword-based approach
        print(f"\n🔤 KEYWORD-BASED APPROACH:")
        keyword_results = keyword_simulator.keyword_based_selection(
            ir_data, query, focus_entities, max_entities=5
        )
        
        print(f"\nKeyword Results:")
        for j, result in enumerate(keyword_results, 1):
            print(f"  {j}. {result['entity_name']} (score: {result['score']}) - {result['explanation']}")
        
        # Test semantic approach
        print(f"\n🧠 SEMANTIC APPROACH:")
        semantic_results = semantic_integration.enhance_ir_context_selection(
            ir_data, query, focus_entities, max_entities=5
        )
        
        print(f"\nSemantic Results:")
        ir_slices = semantic_results.get('ir_slices', [])
        for j, result in enumerate(ir_slices, 1):
            score = result.get('relevance_score', 0)
            explanation = result.get('semantic_explanation', 'No explanation')
            print(f"  {j}. {result['entity_name']} (score: {score:.3f}) - {explanation}")
        
        # Analysis
        print(f"\n📊 ANALYSIS:")
        
        # Compare selection quality
        keyword_entities = set(r['entity_name'] for r in keyword_results)
        semantic_entities = set(r['entity_name'] for r in ir_slices)
        
        print(f"  Keyword selected: {len(keyword_entities)} entities")
        print(f"  Semantic selected: {len(semantic_entities)} entities")
        print(f"  Overlap: {len(keyword_entities & semantic_entities)} entities")
        print(f"  Semantic-only: {semantic_entities - keyword_entities}")
        print(f"  Keyword-only: {keyword_entities - semantic_entities}")
        
        # Show semantic analysis details
        semantic_analysis = semantic_results.get('semantic_analysis', {})
        query_analysis = semantic_analysis.get('query_analysis')
        if query_analysis:
            print(f"\n  Semantic Understanding:")
            print(f"    Intent: {query_analysis.intent.value}")
            print(f"    Scope: {query_analysis.scope.value}")
            print(f"    Confidence: {query_analysis.confidence:.2f}")
            print(f"    Domain Concepts: {len(query_analysis.domain_concepts)}")
            for concept in query_analysis.domain_concepts:
                print(f"      - {concept.category}: {concept.concept}")
    
    print(f"\n{'='*80}")
    print("🎯 SUMMARY")
    print(f"{'='*80}")
    print("""
The semantic approach provides several key advantages over keyword matching:

1. **Intent Understanding**: Analyzes what the user actually wants to accomplish
2. **Component Purpose Analysis**: Understands the role each component plays
3. **Architectural Awareness**: Considers how components work together
4. **Domain Concept Extraction**: Identifies business and technical concepts
5. **Contextual Relevance**: Selects components based on their relevance to the task

This results in more relevant, architecturally coherent context that better
helps users understand and work with their codebase.
""")


def save_comparison_results():
    """Save detailed comparison results to a file."""
    print("\n💾 Saving detailed comparison results...")
    
    ir_data = create_realistic_ir_data()
    semantic_integration = SemanticContextIntegration()
    
    query = "How does user authentication work in the system?"
    focus_entities = ['authenticate', 'user']
    
    # Generate semantic analysis
    semantic_results = semantic_integration.integrate_with_existing_system(
        context_request_handler=None,
        ir_data=ir_data,
        user_query=query,
        focus_entities=focus_entities,
        max_entities=5
    )
    
    # Save the LLM package
    llm_package = semantic_results.get('llm_friendly_package', '')
    
    with open('semantic_vs_keyword_comparison.txt', 'w', encoding='utf-8') as f:
        f.write("# Semantic vs Keyword Matching Comparison\n\n")
        f.write(f"Query: {query}\n")
        f.write(f"Focus Entities: {focus_entities}\n\n")
        f.write("## Semantic Analysis Result:\n\n")
        f.write(llm_package)
    
    print(f"✅ Results saved to 'semantic_vs_keyword_comparison.txt'")
    print(f"   Package size: {len(llm_package):,} characters")


if __name__ == "__main__":
    run_comparison_test()
    save_comparison_results()
