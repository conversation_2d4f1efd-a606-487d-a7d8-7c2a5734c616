#!/usr/bin/env python3
"""
Simple test for the IR_REQUEST format fix using exact patterns from the code.
"""

import re
import json

def test_exact_problematic_case():
    """Test the exact case that was reported as problematic."""
    
    print("🧪 Testing Exact Problematic Case")
    print("=" * 50)
    
    # The exact problematic format from the user's report
    problematic_format = '{"IR_REQUEST": {"focus_entities": ["process_all_positions", "close_all_positions", "update_positions"], "task_type": "verification"}}'
    
    # The exact working format from the user's report  
    working_format = '{{IR_REQUEST: {{ "focus_entities": ["keyword1", "keyword2"], "task_type": "verification" }}}}'
    
    print(f"❌ PROBLEMATIC (not working): {problematic_format}")
    print(f"✅ WORKING (was working): {working_format}")
    print()
    
    # The exact patterns from our fix in base_coder.py
    patterns = [
        # Standard JSON format: {"IR_REQUEST": {...}}
        r'\{\s*"IR_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_REQUEST": {...}}
        # Simplified format with double braces: {{IR_REQUEST: {{ ... }}}}
        r'\{\{IR_REQUEST:\s*\{\{\s*(.*?)\s*\}\}\s*\}\}',  # {{IR_REQUEST: {{ ... }}}}
        # Simplified format: {IR_REQUEST: {...}}
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    print("🔍 Testing PROBLEMATIC format (should now work):")
    test_format(problematic_format, patterns, "Standard JSON")
    
    print("\n🔍 Testing WORKING format (should still work):")
    test_format(working_format, patterns, "Simplified")
    
def test_format(content, patterns, format_name):
    """Test a specific format against the patterns."""

    print(f"   Format: {format_name}")
    print(f"   Content: {content}")

    for i, pattern in enumerate(patterns, 1):
        match = re.search(pattern, content, re.DOTALL)
        if match:
            captured = match.group(1).strip()
            print(f"   ✅ Pattern {i} matched: {repr(captured)}")

            # For the double brace format, we need to wrap the captured content in braces
            json_content = captured
            if i == 2:  # Double brace pattern
                json_content = "{" + captured + "}"

            try:
                parsed = json.loads(json_content)
                focus_entities = parsed.get("focus_entities", [])
                task_type = parsed.get("task_type", "")

                print(f"   ✅ JSON parsed successfully!")
                print(f"      focus_entities: {focus_entities}")
                print(f"      task_type: {task_type}")

                if focus_entities and task_type:
                    print(f"   🎉 {format_name} format WORKS!")
                    return True
                else:
                    print(f"   ❌ Missing required fields")
                    return False

            except json.JSONDecodeError as e:
                print(f"   ❌ JSON parsing failed: {e}")
                print(f"      Tried to parse: {repr(json_content)}")
                continue

    print(f"   ❌ {format_name} format FAILED - no pattern matched")
    return False

def test_additional_cases():
    """Test additional edge cases."""
    
    print("\n🧪 Testing Additional Cases")
    print("=" * 40)
    
    test_cases = [
        {
            "name": "Standard JSON with spaces",
            "content": '{ "IR_REQUEST" : { "focus_entities": ["test"], "task_type": "debugging" } }'
        },
        {
            "name": "Standard JSON embedded in text",
            "content": 'I need context. {"IR_REQUEST": {"focus_entities": ["func"], "task_type": "analysis"}} Please help.'
        },
        {
            "name": "Simplified format single brace",
            "content": '{IR_REQUEST: {"focus_entities": ["test"], "task_type": "debugging"}}'
        },
        {
            "name": "Simplified format double brace",
            "content": '{{IR_REQUEST: {"focus_entities": ["test"], "task_type": "debugging"}}}'
        }
    ]
    
    patterns = [
        r'\{\s*"IR_REQUEST"\s*:\s*(\{.*?\})\s*\}',  # {"IR_REQUEST": {...}}
        r'\{\{IR_REQUEST:\s*\{\{\s*(.*?)\s*\}\}\s*\}\}',  # {{IR_REQUEST: {{ ... }}}}
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}',  # {IR_REQUEST: {...}} - single closing brace
        r'\{IR_REQUEST:\s*(\{.*?\})\s*\}\}', # {IR_REQUEST: {...}}} - double closing brace
        r'\{IR_REQUEST:\s*(.*)'              # {IR_REQUEST: ... - fallback for incomplete
    ]
    
    success_count = 0
    for test_case in test_cases:
        print(f"\n   Testing: {test_case['name']}")
        if test_format(test_case['content'], patterns, test_case['name']):
            success_count += 1
    
    print(f"\n📊 Additional tests: {success_count}/{len(test_cases)} passed")
    return success_count == len(test_cases)

if __name__ == "__main__":
    print("🚀 Simple IR_REQUEST Format Fix Test")
    print("=" * 60)
    
    # Test the main problematic case
    test_exact_problematic_case()
    
    # Test additional cases
    additional_success = test_additional_cases()
    
    print(f"\n🎯 Summary:")
    print(f"   The fix adds support for standard JSON format: {{\"IR_REQUEST\": {{...}}}}")
    print(f"   While maintaining compatibility with simplified format: {{{{IR_REQUEST: {{{{ ... }}}}}}}}")
    print(f"   Additional test cases: {'✅ All passed' if additional_success else '❌ Some failed'}")
