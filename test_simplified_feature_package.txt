## CRITICAL ENTITIES (2 most important)

### 1. cmd_add (function)
- File: aider-main\aider\commands.py


- Criticality: medium | Risk: medium
- **Calls**: []
- **Used by**: ["search_repo", "repomap", "file_scanner", "search_replace", "intelligent_context_selector", "..."] (total: 10)
- **Side Effects**: none

### 2. _is_method (function)
- File: mid_level_ir\entity_extractor.py


- Criticality: low | Risk: low
- **Calls**: []
- **Used by**: ["architecture_diagram_generator"] (total: 1)
- **Side Effects**: none

## KEY IMPLEMENTATIONS (2 functions)
Complete code available on request for any function.

### 1. cmd_add
```python
    def cmd_add(self, args):
        "Add files to the chat so aider can edit them or review them in detail"

        all_matched_files = set()

        filenames = parse_quoted_filenames(args)
        for word in filenames:
            if Path(word).is_absolute():
                fname = Path(word)
            else:
                fname = Path(self.coder.root) / word

            if self.coder.repo and self.coder.repo.ignored_file(fname):
                self.io.tool_warning(f"Skipping {fname} due to aiderignore or --subtree-only.")
                continue

            if fname.exists():
                if fname.is_file():
                    all_matched_files.add(str(fname))
                    continue
                # an existing dir, escape any special chars so they won't be globs
                word = re.sub(r"([\*\?\[\]])", r"[\1]", word)

            matched_files = self.glob_filtered_to_repo(word)
            if matched_files:
                all_matched_files.update(matched_files)
                continue

            if "*" in str(fname) or "?" in str(fname):
                self.io.tool_error(
                    f"No match, and cannot create file with wildcard characters: {fname}"
                )
                continue

            if fname.exists() and fname.is_dir() and self.coder.repo:
                self.io.tool_error(f"Directory {fname} is not in git.")
                self.io.tool_output(f"You can add to git with: /git add {fname}")
                continue

    # ... (implementation continues)
```

### 2. _is_method
```python
    def _is_method(self, func_node: ast.FunctionDef, module_ast: ast.AST) -> bool:
        """
        Check if a function is a method (inside a class).

        Args:
            func_node: Function AST node
            module_ast: Module AST to search for parent class

        Returns:
            True if the function is a method
        """
        # Walk the module AST to find if this function is inside a class
        for node in ast.walk(module_ast):
            if isinstance(node, ast.ClassDef):
                for child in node.body:
                    if child is func_node:
                        return True
        return False

```
