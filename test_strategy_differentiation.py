"""
Test Strategy Differentiation
Test that different strategies now select different clusters and entities.
"""

import sys
import os

# Add the aider-main directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'aider-main'))

from aider.context_request.context_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRContextRequest


def test_strategy_differentiation():
    """Test that different strategies select different clusters and entities."""
    print("🎯 Testing Strategy Differentiation")
    print("=" * 70)
    
    # Initialize the context request handler
    project_path = "."
    handler = ContextRequestHandler(project_path)
    
    # Test queries with different expected strategies
    test_queries = [
        {
            'query': 'How does user authentication work in the system?',
            'expected_strategy': 'cross_cutting',
            'description': 'Security workflow analysis'
        },
        {
            'query': 'What is the overall architecture of this system?',
            'expected_strategy': 'architecture_overview',
            'description': 'System architecture overview'
        },
        {
            'query': 'How does the context_request_handler work?',
            'expected_strategy': 'workflow_focused',
            'description': 'Specific component workflow'
        }
    ]
    
    results = {}
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n🧪 Test {i}: {test_case['description']}")
        print("-" * 50)
        
        # Create IR context request
        ir_request = IRContextRequest(
            user_query=test_case['query'],
            task_description=f"Analyze: {test_case['query']}",
            task_type="general_analysis",
            focus_entities=[],
            max_tokens=8000,
            llm_friendly=True,
            include_ir_slices=True,
            include_code_context=True,
            max_output_chars=30000,
            max_entities=8
        )
        
        try:
            print(f"🚀 Processing: '{test_case['query']}'")
            
            # Process the request
            result = handler.process_ir_context_request(ir_request)
            
            if 'error' in result:
                print(f"   ❌ Error: {result['error']}")
                continue
            
            if 'llm_friendly_package' not in result:
                print(f"   ❌ No LLM package generated")
                continue
            
            package = result['llm_friendly_package']
            print(f"   ✅ Generated package: {len(package):,} characters")
            
            # Extract strategy information
            strategy_line = None
            cluster_lines = []
            entity_lines = []
            
            lines = package.split('\n')
            for j, line in enumerate(lines):
                if 'Strategy:' in line:
                    strategy_line = line.strip()
                elif 'Primary Clusters:' in line:
                    cluster_lines.append(line.strip())
                elif 'Supporting Clusters:' in line:
                    cluster_lines.append(line.strip())
                elif '##' in line and any(keyword in line.lower() for keyword in ['function', 'class', 'method']):
                    entity_lines.append(line.strip())
            
            # Store results for comparison
            results[test_case['description']] = {
                'strategy': strategy_line,
                'clusters': cluster_lines,
                'entities': entity_lines[:5],  # First 5 entities
                'package_size': len(package)
            }
            
            print(f"   📊 Strategy: {strategy_line}")
            print(f"   🏗️ Clusters: {len(cluster_lines)} cluster lines found")
            print(f"   🔧 Entities: {len(entity_lines)} entity lines found")
            
            # Show first few entities
            if entity_lines:
                print(f"   📋 First 3 entities:")
                for entity in entity_lines[:3]:
                    print(f"      • {entity}")
            
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
    
    # Compare results
    print(f"\n" + "=" * 70)
    print("🔍 STRATEGY DIFFERENTIATION ANALYSIS")
    print("=" * 70)
    
    if len(results) >= 2:
        strategies = [r['strategy'] for r in results.values()]
        clusters = [r['clusters'] for r in results.values()]
        entities = [r['entities'] for r in results.values()]
        
        print(f"📊 Strategies:")
        for desc, result in results.items():
            print(f"   • {desc}: {result['strategy']}")
        
        print(f"\n🏗️ Cluster Selection:")
        for desc, result in results.items():
            print(f"   • {desc}:")
            for cluster in result['clusters']:
                print(f"     - {cluster}")
        
        print(f"\n🔧 Entity Selection:")
        for desc, result in results.items():
            print(f"   • {desc}:")
            for entity in result['entities'][:3]:
                print(f"     - {entity}")
        
        # Check for differentiation
        unique_strategies = len(set(strategies))
        unique_entity_sets = len(set(tuple(e) for e in entities))
        
        print(f"\n🎯 Differentiation Results:")
        print(f"   • Unique strategies: {unique_strategies}/{len(results)}")
        print(f"   • Unique entity sets: {unique_entity_sets}/{len(results)}")
        
        if unique_strategies == len(results) and unique_entity_sets == len(results):
            print(f"   ✅ EXCELLENT: All strategies select different content!")
        elif unique_strategies == len(results):
            print(f"   ⚠️  PARTIAL: Different strategies but similar entities")
        elif unique_entity_sets == len(results):
            print(f"   ⚠️  PARTIAL: Different entities but similar strategies")
        else:
            print(f"   ❌ POOR: Strategies are not sufficiently differentiated")
        
        return unique_strategies == len(results) and unique_entity_sets == len(results)
    
    else:
        print(f"   ❌ Not enough results to compare")
        return False


def test_cluster_selection_logic():
    """Test the cluster selection logic directly."""
    print(f"\n🏗️ Testing Cluster Selection Logic")
    print("=" * 70)
    
    try:
        from aider.context_request.hierarchical_context_selector import HierarchicalContextSelector
        from aider.context_request.intelligent_semantic_selector import IntelligentSemanticSelector
        import json
        
        # Load IR data
        ir_file = "ir_data_20250601_183423.json"  # Use the latest one
        if not os.path.exists(ir_file):
            print(f"   ⚠️  IR file not found: {ir_file}")
            return False
        
        with open(ir_file, 'r') as f:
            ir_data = json.load(f)
        
        selector = HierarchicalContextSelector()
        semantic_selector = IntelligentSemanticSelector()
        
        # Generate architecture
        architecture = selector.architecture_generator.generate_architecture(ir_data)
        selector.system_architecture = architecture
        
        print(f"   📊 Architecture loaded: {len(architecture.clusters)} clusters")
        
        # Test different query types
        test_queries = [
            ('How does user authentication work?', 'security_analysis'),
            ('What is the system architecture?', 'architecture_understanding'),
            ('How does context processing work?', 'workflow_analysis')
        ]
        
        for query, expected_intent in test_queries:
            print(f"\n   🧪 Testing: '{query}'")
            
            # Analyze query
            query_analysis = semantic_selector.analyze_query(query)
            print(f"      Intent: {query_analysis.intent.value} (expected: {expected_intent})")
            
            # Determine strategy
            strategy = selector._determine_selection_strategy(query_analysis, [])
            print(f"      Strategy: {strategy.value}")
            
            # Select architectural context
            arch_context = selector._select_architectural_context(
                strategy, query_analysis, [], 8
            )
            
            print(f"      Primary clusters: {arch_context.primary_clusters}")
            print(f"      Supporting clusters: {arch_context.supporting_clusters}")
            print(f"      Rationale: {arch_context.architectural_rationale}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Cluster selection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🔧 Testing Strategy Differentiation in Hierarchical Context Selection")
    print("=" * 70)
    
    # Test 1: Cluster selection logic
    test1_passed = test_cluster_selection_logic()
    
    # Test 2: End-to-end strategy differentiation
    test2_passed = test_strategy_differentiation()
    
    print(f"\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"   Cluster Selection Logic: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"   Strategy Differentiation: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    
    if test1_passed and test2_passed:
        print(f"\n🎉 STRATEGY DIFFERENTIATION WORKING!")
        print(f"   ✅ Different strategies select different clusters")
        print(f"   ✅ Different clusters lead to different entities")
        print(f"   ✅ LLM packages now contain diverse, strategy-specific content")
        print(f"\n💡 The hierarchical approach now provides:")
        print(f"   • Architecture-aware context selection")
        print(f"   • Strategy-specific entity scoring")
        print(f"   • Diverse content based on query intent")
        print(f"   • Clear architectural rationale")
    else:
        print(f"\n❌ STRATEGY DIFFERENTIATION ISSUES DETECTED!")
        print(f"   Check the failed tests above for details")
        print(f"   The strategies may still be selecting similar content")
