#!/usr/bin/env python3
"""
System Verification Test for Enhanced Metadata System

This script performs a quick verification to ensure the Enhanced Metadata System
is working correctly before running the comprehensive test suite.
"""

import sys
import traceback
from datetime import datetime


def test_imports():
    """Test that all required modules can be imported."""
    print("🔍 Testing module imports...")
    
    try:
        from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        print("   ✅ ContextRequestHandler imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import ContextRequestHandler: {e}")
        try:
            from context_request_handler import ContextRequestHandler, IRContextRequest
            print("   ✅ ContextRequestHandler imported from local module")
        except ImportError as e2:
            print(f"   ❌ Failed to import from local module: {e2}")
            return False
    
    try:
        from test_queries import TestQueries
        print("   ✅ TestQueries imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import TestQueries: {e}")
        return False
    
    try:
        from test_results_analyzer import TestResultsAnalyzer
        print("   ✅ TestResultsAnalyzer imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import TestResultsAnalyzer: {e}")
        return False
    
    try:
        from enhanced_metadata_comprehensive_test import EnhancedMetadataTestRunner
        print("   ✅ EnhancedMetadataTestRunner imported successfully")
    except ImportError as e:
        print(f"   ❌ Failed to import EnhancedMetadataTestRunner: {e}")
        return False
    
    return True


def test_query_loading():
    """Test that test queries can be loaded correctly."""
    print("\n📋 Testing query loading...")
    
    try:
        from test_queries import TestQueries
        
        # Load all queries
        queries = TestQueries.get_all_queries()
        print(f"   ✅ Loaded {len(queries)} queries")
        
        if len(queries) != 50:
            print(f"   ⚠️ Expected 50 queries, got {len(queries)}")
            return False
        
        # Check category distribution
        summary = TestQueries.get_category_summary()
        print(f"   ✅ Found {len(summary)} categories")
        
        expected_categories = {
            "Architecture Overview": 10,
            "Workflow Analysis": 10,
            "Component Deep Dive": 10,
            "Cross-Cutting Concerns": 10,
            "Integration & External Systems": 5,
            "Debugging & Maintenance": 5
        }
        
        for category, expected_count in expected_categories.items():
            actual_count = summary.get(category, 0)
            if actual_count != expected_count:
                print(f"   ❌ Category '{category}': expected {expected_count}, got {actual_count}")
                return False
            else:
                print(f"   ✅ Category '{category}': {actual_count} queries")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error loading queries: {e}")
        return False


def test_context_handler():
    """Test that the context request handler can be initialized."""
    print("\n🔧 Testing context handler initialization...")
    
    try:
        # Try importing from the main module first
        try:
            from aider.context_request.context_request_handler import ContextRequestHandler, IRContextRequest
        except ImportError:
            from context_request_handler import ContextRequestHandler, IRContextRequest
        
        # Initialize handler
        handler = ContextRequestHandler(".")
        print("   ✅ ContextRequestHandler initialized successfully")
        
        # Test creating a simple request
        request = IRContextRequest(
            user_query="Test query for system verification",
            task_description="System verification test",
            task_type="verification",
            max_entities=3,
            llm_friendly=True
        )
        print("   ✅ IRContextRequest created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error with context handler: {e}")
        traceback.print_exc()
        return False


def test_single_query():
    """Test processing a single query to verify the system works end-to-end."""
    print("\n🧪 Testing single query processing...")
    
    try:
        from enhanced_metadata_comprehensive_test import EnhancedMetadataTestRunner
        
        # Initialize test runner
        runner = EnhancedMetadataTestRunner(".")
        print("   ✅ Test runner initialized")
        
        # Execute a simple test query
        result = runner.execute_test_query(
            query_id=999,
            query="What is the overall architecture of this codebase?",
            expected_strategy="ARCHITECTURE_OVERVIEW",
            category="System Verification"
        )
        
        if result.success:
            print(f"   ✅ Query processed successfully in {result.processing_time:.2f}s")
            print(f"   📊 Package length: {len(result.package):,} characters")
            
            if result.analysis:
                quality_score = result.analysis.get('overall_quality_score', 0)
                print(f"   📈 Quality score: {quality_score:.1%}")
            
            return True
        else:
            print(f"   ❌ Query failed: {result.error}")
            return False
        
    except Exception as e:
        print(f"   ❌ Error processing query: {e}")
        traceback.print_exc()
        return False


def run_verification():
    """Run all verification tests."""
    print("🧪 Enhanced Metadata System - Verification Test")
    print("=" * 60)
    print(f"🕒 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("Module Imports", test_imports),
        ("Query Loading", test_query_loading),
        ("Context Handler", test_context_handler),
        ("Single Query Processing", test_single_query)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 Running: {test_name}")
        try:
            if test_func():
                passed_tests += 1
                print(f"   ✅ {test_name} PASSED")
            else:
                print(f"   ❌ {test_name} FAILED")
        except Exception as e:
            print(f"   ❌ {test_name} FAILED with exception: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 VERIFICATION RESULTS: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("🎉 All verification tests passed! System is ready for comprehensive testing.")
        return True
    else:
        print("⚠️ Some verification tests failed. Please fix issues before running comprehensive tests.")
        return False


if __name__ == "__main__":
    success = run_verification()
    
    if success:
        print("\n💡 To run the comprehensive test suite, execute:")
        print("   python enhanced_metadata_comprehensive_test.py")
        print("\n📖 Available options:")
        print("   --no-save          Don't save detailed results")
        print("   --save-packages    Save individual test packages")
        print("   --project=PATH     Use custom project path")
        sys.exit(0)
    else:
        print("\n🔧 Please fix the issues above before proceeding.")
        sys.exit(1)
