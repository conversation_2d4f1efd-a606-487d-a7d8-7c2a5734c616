#!/usr/bin/env python3
"""
Trace how the system discovers PositionOpener and PositionCloser entities.
Debug the entire pipeline from IR generation to entity selection.
"""

import sys
import os
import json
sys.path.append('aider-main/aider/context_request')


def trace_ir_generation():
    """Trace IR generation to see if position classes are captured."""
    print("🔍 STEP 1: TRACING IR GENERATION")
    print("=" * 70)
    
    try:
        from aider_integration_service import AiderIntegrationService
        
        # Test IR generation on current directory
        service = AiderIntegrationService()
        
        print("🎯 Generating IR for current directory...")
        ir_data = service.generate_mid_level_ir(".")
        
        if not ir_data:
            print("❌ No IR data generated")
            return None
        
        print(f"✅ IR generated: {len(ir_data.get('modules', []))} modules")
        
        # Search for position-related entities
        position_entities = []
        all_entities = []
        
        for module in ir_data.get('modules', []):
            module_name = module.get('name', 'unknown')
            for entity in module.get('entities', []):
                entity_name = entity.get('name', '')
                entity_type = entity.get('type', '')
                entity_file = entity.get('file', '')
                
                all_entities.append(entity_name)
                
                # Check for position-related entities
                if 'position' in entity_name.lower():
                    position_entities.append({
                        'name': entity_name,
                        'type': entity_type,
                        'module': module_name,
                        'file': entity_file
                    })
        
        print(f"📊 Total entities found: {len(all_entities):,}")
        print(f"🎯 Position entities found: {len(position_entities)}")
        
        if position_entities:
            print("\n✅ POSITION ENTITIES IN IR:")
            for entity in position_entities:
                print(f"   - {entity['name']} ({entity['type']}) in {entity['file']}")
            
            # Check specifically for our target classes
            opener_found = any('PositionOpener' in e['name'] for e in position_entities)
            closer_found = any('PositionCloser' in e['name'] for e in position_entities)
            
            print(f"\n🔍 Target Classes:")
            print(f"   PositionOpener: {'✅ FOUND' if opener_found else '❌ MISSING'}")
            print(f"   PositionCloser: {'✅ FOUND' if closer_found else '❌ MISSING'}")
            
            if opener_found and closer_found:
                print("🎉 SUCCESS: Both target classes found in IR!")
                return ir_data, position_entities
            else:
                print("🚨 ISSUE: Target classes missing from IR")
                return ir_data, position_entities
        else:
            print("❌ NO position entities found in IR")
            print("🔍 Sample entities found:")
            for entity in all_entities[:10]:
                print(f"   - {entity}")
            return ir_data, []
        
    except Exception as e:
        print(f"❌ Error in IR generation: {e}")
        import traceback
        traceback.print_exc()
        return None, []


def trace_entity_collection(ir_data):
    """Trace how entities are collected for context selection."""
    print("\n🔍 STEP 2: TRACING ENTITY COLLECTION")
    print("=" * 70)
    
    if not ir_data:
        print("❌ No IR data to process")
        return []
    
    try:
        from hierarchical_context_selector import HierarchicalContextSelector
        from query_intent_analyzer import QueryIntentAnalyzer
        from system_architecture_generator import SystemArchitectureGenerator
        from semantic_context_selector import SemanticContextSelector
        from intelligent_context_models import QueryContext, QueryIntent, QueryScope
        
        # Initialize components
        analyzer = QueryIntentAnalyzer()
        arch_gen = SystemArchitectureGenerator()
        semantic_sel = SemanticContextSelector()
        selector = HierarchicalContextSelector(analyzer, arch_gen, semantic_sel)
        
        # Create query context for position management
        query_context = QueryContext(
            intent=QueryIntent.WORKFLOW_ANALYSIS,
            domain_concepts=[],
            scope=QueryScope.WORKFLOW_CHAIN,
            focus_entities=["PositionOpener", "PositionCloser"],
            original_query="how does the system manage positions?",
            confidence=0.8,
            query_patterns=[]
        )
        
        print("🎯 Simulating entity collection for position query...")
        
        # Get architectural context
        arch_context = arch_gen.generate_architecture(ir_data, query_context)
        
        print(f"🏗️ Architectural clusters: {arch_context.primary_clusters + arch_context.supporting_clusters}")
        
        # Collect entities from clusters
        all_clusters = arch_context.primary_clusters + arch_context.supporting_clusters
        candidate_entities = []
        
        for module in ir_data.get('modules', []):
            module_cluster = module.get('cluster', 'unknown')
            
            # Check if this module is in our target clusters
            if module_cluster in all_clusters:
                for entity in module.get('entities', []):
                    candidate_entities.append({
                        'name': entity.get('name', ''),
                        'type': entity.get('type', ''),
                        'file': entity.get('file', ''),
                        'cluster': module_cluster,
                        'module': module.get('name', '')
                    })
        
        print(f"📊 Candidate entities collected: {len(candidate_entities)}")
        
        # Check if position classes are in candidates
        position_candidates = [e for e in candidate_entities if 'position' in e['name'].lower()]
        opener_candidate = any('PositionOpener' in e['name'] for e in candidate_entities)
        closer_candidate = any('PositionCloser' in e['name'] for e in candidate_entities)
        
        print(f"🎯 Position candidates: {len(position_candidates)}")
        print(f"   PositionOpener in candidates: {'✅ YES' if opener_candidate else '❌ NO'}")
        print(f"   PositionCloser in candidates: {'✅ YES' if closer_candidate else '❌ NO'}")
        
        if position_candidates:
            print("\n✅ POSITION CANDIDATES:")
            for entity in position_candidates:
                print(f"   - {entity['name']} ({entity['type']}) from {entity['cluster']} cluster")
        
        if opener_candidate and closer_candidate:
            print("🎉 SUCCESS: Both target classes are candidates for selection!")
        else:
            print("🚨 ISSUE: Target classes not in candidate list")
            print("🔍 This means they're either:")
            print("   1. Not in the right clusters")
            print("   2. Filtered out during collection")
            print("   3. Missing from IR entirely")
        
        return candidate_entities
        
    except Exception as e:
        print(f"❌ Error in entity collection: {e}")
        import traceback
        traceback.print_exc()
        return []


def trace_scoring_and_selection(candidate_entities):
    """Trace the scoring and selection process."""
    print("\n🔍 STEP 3: TRACING SCORING AND SELECTION")
    print("=" * 70)
    
    if not candidate_entities:
        print("❌ No candidate entities to score")
        return []
    
    try:
        from hierarchical_context_selector import HierarchicalContextSelector
        from query_intent_analyzer import QueryIntentAnalyzer
        from system_architecture_generator import SystemArchitectureGenerator
        from semantic_context_selector import SemanticContextSelector
        from intelligent_context_models import QueryContext, QueryIntent, QueryScope
        
        # Initialize components
        analyzer = QueryIntentAnalyzer()
        arch_gen = SystemArchitectureGenerator()
        semantic_sel = SemanticContextSelector()
        selector = HierarchicalContextSelector(analyzer, arch_gen, semantic_sel)
        
        query = "how does the system manage positions?"
        
        print(f"🎯 Scoring {len(candidate_entities)} candidates for query: {query}")
        print()
        
        # Score entities using our AI-centric approach
        scored_entities = []
        
        for entity in candidate_entities:
            entity_name = entity['name']
            entity_type = entity['type']
            entity_file = entity['file']
            
            # Use the AI-centric scoring
            score = selector._ai_centric_entity_scoring(entity_name, entity_type, query, entity_file)
            
            scored_entities.append({
                'name': entity_name,
                'type': entity_type,
                'file': entity_file,
                'cluster': entity['cluster'],
                'score': score
            })
        
        # Sort by score
        scored_entities.sort(key=lambda x: x['score'], reverse=True)
        
        print("🏆 TOP 10 SCORED ENTITIES:")
        for i, entity in enumerate(scored_entities[:10], 1):
            name = entity['name']
            score = entity['score']
            entity_type = entity['type']
            cluster = entity['cluster']
            
            status = "🎯 POSITION" if 'position' in name.lower() else \
                    "🔧 INFRA" if name.endswith('Manager') else \
                    "⚙️ OTHER"
            
            print(f"{i:2d}. {name:<25} Score: {score:6.1f} ({entity_type}, {cluster}) {status}")
        
        # Check if position classes made it to top
        top_5 = scored_entities[:5]
        position_in_top_5 = [e for e in top_5 if 'position' in e['name'].lower()]
        
        print(f"\n📊 Position entities in top 5: {len(position_in_top_5)}")
        
        if len(position_in_top_5) >= 2:
            print("🎉 SUCCESS: Multiple position entities in top 5!")
        elif len(position_in_top_5) >= 1:
            print("🔄 PARTIAL: Some position entities in top 5")
        else:
            print("❌ FAILURE: No position entities in top 5")
        
        return scored_entities
        
    except Exception as e:
        print(f"❌ Error in scoring: {e}")
        import traceback
        traceback.print_exc()
        return []


def main():
    """Main tracing function."""
    print("🔍 TRACING POSITION DISCOVERY PIPELINE")
    print("=" * 70)
    print("Following PositionOpener and PositionCloser through the entire system")
    print()
    
    # Step 1: IR Generation
    ir_data, ir_position_entities = trace_ir_generation()
    
    # Step 2: Entity Collection
    candidate_entities = trace_entity_collection(ir_data)
    
    # Step 3: Scoring and Selection
    scored_entities = trace_scoring_and_selection(candidate_entities)
    
    # Final Analysis
    print("\n💡 PIPELINE ANALYSIS")
    print("=" * 70)
    
    ir_has_positions = len(ir_position_entities) > 0 if ir_position_entities else False
    candidates_have_positions = any('position' in e['name'].lower() for e in candidate_entities) if candidate_entities else False
    top_has_positions = any('position' in e['name'].lower() for e in scored_entities[:5]) if scored_entities else False
    
    print(f"Step 1 - IR Generation: {'✅ PASS' if ir_has_positions else '❌ FAIL'}")
    print(f"Step 2 - Entity Collection: {'✅ PASS' if candidates_have_positions else '❌ FAIL'}")
    print(f"Step 3 - Scoring & Selection: {'✅ PASS' if top_has_positions else '❌ FAIL'}")
    
    if ir_has_positions and candidates_have_positions and top_has_positions:
        print("\n🎉 OVERALL SUCCESS: Position classes flow through entire pipeline!")
    elif ir_has_positions and candidates_have_positions:
        print("\n🔧 SCORING ISSUE: Position classes found but not scored highly")
    elif ir_has_positions:
        print("\n🔧 COLLECTION ISSUE: Position classes in IR but not collected")
    else:
        print("\n🚨 IR ISSUE: Position classes not found in IR generation")
    
    print("\n🎯 NEXT STEPS:")
    if not ir_has_positions:
        print("1. Fix IR generation to capture position management files")
    elif not candidates_have_positions:
        print("1. Fix entity collection to include position classes")
    elif not top_has_positions:
        print("1. Fix scoring algorithm to prioritize position classes")
    else:
        print("1. System should now work correctly for position queries!")


if __name__ == "__main__":
    main()
